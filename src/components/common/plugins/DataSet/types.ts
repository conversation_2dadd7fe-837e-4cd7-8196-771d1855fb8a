import { DataSet } from '@weapp/ebdcoms';
import { ChartsConfigProps } from '@weapp/ebdcoms/lib/components/charts/types';

/**
 * <AUTHOR>
 * @createTime 2021 -11 -15
 */

export interface DataSetItem extends DataSet {}

export interface DataSetProps extends ChartsConfigProps<DataSetItem> {
  showBizData?: boolean; // 显示标准业务数据
  showCustomData?: boolean; // 显示自定义数据
  showEteamsData?: boolean; // 显示数据仓库数据,默认显示
  showLogicData?: boolean; // 业务模块
  showExternalDataSet?: boolean; // 数据集合
  showExternalData?: boolean; // 外部数据源
  showLocalds?: boolean; // 数据集
  showEbFormData?: boolean; //eb数据源（假的后续舍弃）
}

export interface DataSetField {
  compType: string;
  formatType: string;
  id: string;
  name: string;
  objId: string;
  optionType: string;
  text: string;
  type: string;
}
