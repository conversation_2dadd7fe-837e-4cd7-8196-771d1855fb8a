import { Dialog, Icon } from '@weapp/ui';
import { classnames, getLabel } from '@weapp/utils';
import { observer } from 'mobx-react';
import React from 'react';
import { TreeNodeProps } from '../../engine/drag-tree/types';

@observer
class TreeNode extends React.Component<TreeNodeProps> {

  addNode = () => {
    const { node, onCreate } = this.props;
    onCreate(node);
  };

  delNode = (oEvent: React.SyntheticEvent) => {
    const { onRemove, node } = this.props;

    Dialog.confirm({
      mask: true,
      content: getLabel('56178', '确定要刪除此分支？'),
      onOk: () => {
        onRemove(node);
      },
    });
    oEvent.stopPropagation();
  };

  render() {
    const { node, selector } = this.props;
    const nodeId = selector || '';
    const { name = '', children, pid, id, canDelete } = node;
    const hasNode = children && children.length > 0;
    const nameTxt = typeof name === 'string' ? name : name.nameAlias;
    const classns = classnames({
      'ebdf-ds-vertical-tree-item': true,
      'ebdf-ds-vertical-treeroot-item': !pid,
      'ebdf-ds-vertical-treenode-item': pid,
      'ebdf-ds-active': true,
      selected: nodeId == id,
    });
    return (
      <div className="ebdf-ds-vertical-tree-box">
        <div className={classns} data-nodeid={id}>
          {pid && (
            <div className="line"/>
          )}
          <div className="rootdisable-box">
            <h6 className="rootdisable" title={nameTxt}>{nameTxt}</h6>
            <div className="ebdf-ds-treenode-operate-add">
              <Icon
                weId={`${this.props.weId || ''}_oc9ok8`}
                name="Icon-add-to02"
                onClick={this.addNode}
              />
            </div>
            {!hasNode && pid && canDelete && (
              <div className="ebdf-ds-treenode-operate-del">
                <Icon
                  weId={`${this.props.weId || ''}_4nti0z`}
                  name="Icon-reduce02"
                  onClick={this.delNode}
                />
              </div>
            )}
          </div>
        </div>
        <div className="ebdf-ds-vertical-tree-wrap">{this.props.children}</div>
      </div>
    );
  }
}

export default TreeNode;
