import { getLabel } from '@weapp/utils';
import { observer } from 'mobx-react';
import React from 'react';
import DataSort from '.';
import { ebdfClsPrefix } from '../../../../constants';
import './index.less';
@observer
export default class DataSortCom extends React.PureComponent<any> {
  handleChange = (data: any[]) => {
    const { onChange } = this.props;
    onChange([...data]);
  };

  render() {
    const { value = [], config } = this.props;
    const {
      dataset = {
        id: '',
        text: '',
        groupId: '',
        type: '',
      },
    } = config;
    const hasValue = value.length > 0;
    const btnElm = !hasValue ? getLabel('105962', '新建排序') : getLabel('105963', '编辑排序');
    return (
      <span className={`${ebdfClsPrefix}-config-data-sort`}>
        <DataSort
          weId={`${this.props.weId || ''}_14y0bh`}
          value={value}
          dataset={dataset!}
          onChange={this.handleChange}
          buttonType="default"
          btnElm={btnElm}
        />
      </span>
    );
  }
}
