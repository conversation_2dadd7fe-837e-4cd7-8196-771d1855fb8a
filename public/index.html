<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="utf-8" />
    <link rel="icon" href="%PUBLIC_URL%/favicon.ico" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <meta name="theme-color" content="#000000" />
    <meta name="renderer" content="webkit">
    <meta http-equiv="Pragma" content="no-cache" />
    <meta http-equiv="Expires" content="0" />
    <meta http-equiv="Cache-Control" content="no-cache,no-store,must-revalidate">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    
    <!--
      manifest.json provides metadata used when your web app is installed on a
      user's mobile device or desktop. See https://developers.google.com/web/fundamentals/web-app-manifest/
    -->
    
    <!--
      Notice the use of %PUBLIC_URL% in the tags above.
      It will be replaced with the URL of the `public` folder during the build.
      Only files inside the `public` folder can be referenced from the HTML.

      Unlike "/favicon.ico" or "favicon.ico", "%PUBLIC_URL%/favicon.ico" will
      work correctly both with client-side routing and a non-root public URL.
      Learn how to configure a non-root public URL by running `npm run build`.
    -->
    <link rel="stylesheet" href="/build/ui/static/css/lib.css?v=d6e1c140" />
    <title></title>
  </head>
  <body>
    <noscript>You need to enable JavaScript to run this app.</noscript>
    <div id="root"></div>
    <script id="initLoadingScript">(function(){try{var a=navigator.userAgent,c=-1<a.indexOf("Mobi")&&0>a.indexOf("weapp-pc")||-1<a.indexOf("iPh")||-1<a.indexOf("480")||-1<a.indexOf("Tablet")||-1<a.indexOf("Pad")||-1<a.indexOf("Nexus 7"),b="weappInitLoading"+(c?"M":"PC");a=localStorage[b+"ImgUrl"];var d=localStorage[b+"ImgStyle"]||(c?"#initLoadingImg{display:block;margin:150px auto;max-width:60%;min-width:20%}":""),e='<img id="initLoadingImg" src="'+a+'" alt="" />',f=localStorage[b+"Style"]||(c?".init-loading{width:50px;height:50px;position:relative;margin:150px auto}.init-loading span{display:inline-block;width:10px;height:10px;border-radius:50%;background:#ccc;opacity:.2;position:absolute;animation:initLoading 1s ease infinite;-webkit-animation:initLoading 1s ease infinite}@keyframes initLoading{0%{opacity:1}100%{opacity:.2}}@-webkit-keyframes initLoading{0%{opacity:1}100%{opacity:.2}}.init-loading span:nth-child(1){left:0;top:50%;margin-top:-5px;animation-delay:.13s;-webkit-animation-delay:.13s}.init-loading span:nth-child(2){left:6px;top:6px;animation-delay:.26s;-webkit-animation-delay:.26s}.init-loading span:nth-child(3){left:50%;top:0;margin-left:-5px;animation-delay:.39s;-webkit-animation-delay:.39s}.init-loading span:nth-child(4){top:6px;right:6px;animation-delay:.52s;-webkit-animation-delay:.52s}.init-loading span:nth-child(5){right:0;top:50%;margin-top:-5px;animation-delay:.65s;-webkit-animation-delay:.65s}.init-loading span:nth-child(6){right:6px;bottom:6px;animation-delay:.78s;-webkit-animation-delay:.78s}.init-loading span:nth-child(7){bottom:0;left:50%;margin-left:-5px;animation-delay:.91s;-webkit-animation-delay:.91s}.init-loading span:nth-child(8){bottom:6px;left:6px;animation-delay:1.04s;-webkit-animation-delay:1.04s}":"");c=localStorage[b+"Element"]||(c?'<div class="init-loading"><span></span><span></span><span></span><span></span><span></span><span></span><span></span><span></span></div>':"");b=document.createElement("style");var g=document.getElementById("root");document.head.appendChild(b);b.innerHTML=a?d:f;g.innerHTML=a?e:c}catch(i){}})();</script>
    <script src="/build/passport/static/js/lib.js?v=26752598"></script>
    <script src="/build/vendor/static/js/lib.js?v=1acf4562"></script>
    <script src="/build/utils/static/js/lib.js?v=1dbec94a"></script>
    <script src="/build/ecodesdk/static/js/lib.js?v=a4e121fb"></script>
    <script src="/build/ui/static/js/lib.js?v=7fc95348"></script>
    <!--
      This HTML file is a template.
      If you open it directly in the browser, you will see an empty page.

      You can add webfonts, meta tags, or analytics to this file.
      The build step will place the bundled scripts into the <body> tag.

      To begin the development, run `npm start` or `yarn start`.
      To create a production bundle, use `npm run build` or `yarn build`.
    -->
  </body>
</html>
