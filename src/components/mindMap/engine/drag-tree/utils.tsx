import { AnyObj } from '@weapp/ui';
import { TreeData } from '../types';
import { toJS } from 'mobx';

export const transferToTreeData = (dmTables: TreeData[]) => {
  if (!dmTables.length) return dmTables;
  const _dmTables = [...dmTables];
  const rootNode = _dmTables.find((t: TreeData) => !t.pid) || {
    children: [],
    name: '',
    pid: '',
    id: 'root',
    floor: 1,
    isLeaf: false,
    canDelete: false,
  };
  const _children: any = (parentNode: TreeData) => {
    if (!parentNode) return;
    return _dmTables
      .map((t: TreeData) => {
        const ischild = t.pid === parentNode.id;
        return ischild ? { ...t, children: _children(t) } : t;
      })
      .filter((t) => t.pid === parentNode.id);
  };
  rootNode.children = _children(rootNode);
  return [rootNode];
};
/**
 * 移除无上下级关系的节点
 */
export const optimizeNodeTreeList = (dmTables: TreeData[]) => {
  if (!dmTables.length) return dmTables;
  const _dmTables = dmTables.sort((a, b) => b.floor - a.floor);
  const ids = _dmTables.map((i: any) => i.id)
  return dmTables.filter(i => ids.includes(i.pid) || i.id === 'root_');
};
/**
 *数据转化组件的tree数据结构
 */
export const transferToUITreeData = (dmTables: TreeData[], nodeTreeInfo: AnyObj) => {
  if (!dmTables.length) return dmTables;
  const _dmTables = [...dmTables];
  const rootNode = toJS(
    _dmTables.find((t: TreeData) => !t.pid) || {
      children: [],
      content: '',
      name: '',
      pid: '',
      id: 'root',
      floor: 1,
      isLeaf: false,
      canDelete: false,
    }
  );

  const _children: any = (parentNode: TreeData) => {
    if (!parentNode) return;
    return _dmTables
      .map((t: TreeData) => {
        const ischild = t.pid === parentNode.id;
        const _data = ischild ? { ...t, children: _children(t), isLeaf: false } : t;
        const showName = nodeTreeInfo[t.id]?.name || t.content || t.name;
        return { ..._data, content: showName, expandable: true };
      })
      .filter((t) => t.pid === parentNode.id);
  };
  const rootNodeName = nodeTreeInfo[rootNode.id]?.name?.nameAlias || nodeTreeInfo[rootNode.id]?.name || rootNode?.content?.nameAlias || rootNode?.content || rootNode?.name;
  if (_children(rootNode)?.length) {
    //树形数据展开转换
    rootNode.expandable = true;
  }
  return [{ ...rootNode, children: _children(rootNode), content: rootNodeName }];
};
export default transferToTreeData;
