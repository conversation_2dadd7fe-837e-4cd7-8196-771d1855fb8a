import { Cors<PERSON><PERSON>po<PERSON>, <PERSON>, Button } from '@weapp/ui';
import { classnames, getLabel, middleware, flattenDeep, isArray, isEmpty, isEqual, isObject, debounce } from '@weapp/utils';
import { runInAction, toJS } from 'mobx';
import { inject, observer } from 'mobx-react';
import React, { ComponentType, PureComponent } from 'react';
import { withRouter } from 'react-router-dom';
import { When } from 'react-if';
import { appName, mindMapViewClsPrefix, EbdMindMapEventKeys, MindDefaultPluginName, mindMapUrlParamsCache } from '../../../../constants';
import { invoke, hasRegisterFuncInPlugin, canLoadData } from '../../../../utils';
import FPSMonitor from '../../../common/FPSMonitor';
import MindMap from '../../../common/mind-map';
import { handleOffEvent } from '../../../common/mind-map/utils';
import { MindData } from '../../../common/mind-map/types';
import { NodeNames } from '../../engine/node-config/constants';
import { LayoutCardWithRouter } from '../../../common/plugins/field-layout/LayoutCard';
import TitleNew from '../../../common/title';
import UrlDockerDialog from '../../../common/url-docker/UrlDockerDialog';
import { onClickAction } from './actions';
import { JmNode, MindMapContentProps } from './types';
import { MindMapConfigData } from '../../../mindMap/types';
import {
  getAdaptiveHeight,
  getAdaptiveWidth,
  formatConfig,
  checkTreeInfoUpdate,
  checkTreeListUpdate,
  formatMindMapData,
  getUrlParamsWidthRefresh,
  getFuncSetting,
  getCurrentNode,
  getSizeInfo,
  getNodeStyleInfo,
  getShowFields,
  hasGridContent,
  runComEventsAction,
  customRenderCell,
  getTargetNode,
  getPrimaryColorWithOpacity,
  isEmptyShowFields,
} from './utils';
import { FieldsVisibleSet } from './comps';
import ScrollBar from '../../../common/scrollBar';

// 容器内默认高度
const InContainerHeight = 600;
const initial_mindMap_list_data = () => [
  {
    content: getLabel('56039', '根节点'),
    defaultExpendDepth: 0,
    floor: 1,
    id: '0_0',
    isLeaf: false,
    isroot: true,
    parentid: undefined,
    title: getLabel('129038', '思维导图标题'),
    topic: getLabel('56039', '根节点'),
  },
];
const initial_data = () => ({
  mindMapDatas: [],
  mindMapList: initial_mindMap_list_data(),
  appTitle: '',
  appId: '',
  isFirstMount: true, // 是否是第一次初始化 其余都按更新
  jmLoaded: false, // 插件是否加载成功
  hideScrollBar: true, // 是否显示滚动条
  updateKey: new Date().getTime(), // 控制更新
});

@inject('store')
@middleware(appName, 'MindMapContent')
@observer
class MindMapContent extends PureComponent<MindMapContentProps> {
  nodes = null as any;
  state = initial_data();
  jmInstance: any = null;
  pluginCenter: any = null;
  resizeObserver: any = null;
  catchStyle = {
    width: 0,
    height: 0,
  };
  componentDidMount() {
    // 清理一次本地存值
    this.resetLocalData();
    const { compId, pluginCenter, events, coms } = this.props;
    this.initMindMapData();
    this.getContentStyle();
    this.onFieldVisibleChange();
    if (pluginCenter && hasRegisterFuncInPlugin(pluginCenter, MindDefaultPluginName, EbdMindMapEventKeys.renderCustomMindMap)) {
      pluginCenter.nodeRenderCustom = this.nodeRenderCustom;
    }
    // 口子 二开支持复写数据
    if (events && events.on) {
      events.on(`${EbdMindMapEventKeys.onSetMindData}_${compId}`, this.onMindDataByEvent);
      // 对接组件的“刷新组件”动作
      events.on('filter', compId, (val: any[], filterId?: string) => {
        this.initMindMapData({ ...this.props, urlParams: { ...this.props.urlParams, ...getUrlParamsWidthRefresh(val, coms, pluginCenter) } });
      });
    }
  }
  componentDidUpdate(prevProps: MindMapContentProps) {
    const { compId: _compId, pageId: _pageId, config: _config, isMobile: _isMobile, comServicePath: _comServicePath, urlParams: _urlParams, location: _location } = prevProps;
    const { compId, pageId, config, isMobile, comServicePath, pluginCenter, events } = this.props;
    const canLoad = canLoadData(config);
    // 这里是判断是否要重新走接口
    const isLessNodeTreeList = (config.nodeTreeList || []).length < (_config.nodeTreeList || []).length;
    const isUnequalTreeInfo = checkTreeInfoUpdate(config.nodeTreeInfo || [], _config.nodeTreeInfo || []);
    const isUnequalTreeList = checkTreeInfoUpdate(config.nodeTreeList || [], _config.nodeTreeList || []);
    // 如果没有合法的数据源, 不请求 但是如果是删除了所有节点只有根节点 或者是节点属性变化 需要重新请求
    if ((!canLoad || canLoad === 'noData')) {
      if (isUnequalTreeList || isLessNodeTreeList) {
        this.setState({ updateKey: new Date().getTime() });
      }
      return;
    }
    if (
      !isEqual(compId, _compId) ||
      !isEqual(pageId, _pageId) ||
      !isEqual(isMobile, _isMobile) ||
      !isEqual(comServicePath, _comServicePath) ||
      // (!isEqual(urlParams, _urlParams) && location.pathname === _location.pathname) ||//处理点击动作 右侧滑出页面刷新
      isLessNodeTreeList || // 现节点长度小于原长度为删除操作
      isUnequalTreeInfo ||
      isUnequalTreeList
    ) {
      this.initMindMapData();
      invoke(pluginCenter, MindDefaultPluginName, EbdMindMapEventKeys.onDidUpdate, { args: [prevProps, this.props] });
      events && events.emit && events.emit(`${EbdMindMapEventKeys.onDidUpdate}_${compId}`, { prevProps, props: this.props });
    }
    // 这里判断属性变化只刷新视图
    const slideNodeKeys = [NodeNames.Name, NodeNames.NodeStyle, NodeNames.NodeSize, NodeNames.iconInfo, NodeNames.NameShowSwitch, NodeNames.IconHeight, NodeNames.IconWidth];
    const configKeys: (keyof MindMapConfigData)[] = ['commonNodeSizeSet', 'commonNodeStyleSet', 'isHideRoot', 'lineStyle', 'mindLayout'];
    if (configKeys.some((key) => !isEqual(config[key], _config[key])) || checkTreeInfoUpdate(config.nodeTreeInfo || [], _config.nodeTreeInfo || [], slideNodeKeys)) {
      this.refreshView(config);
    }
  }
  componentWillUnmount() {
    const { pluginCenter, compId, events, store } = this.props;
    invoke(pluginCenter, MindDefaultPluginName, EbdMindMapEventKeys.onDestroy);
    events && events.emit && events.emit(EbdMindMapEventKeys.onDestroy, compId);
    this.resetJm(true);
    // store监听移除
    store.reset();
    window.simpleMindMap = null;
  }
  initMindMapData = (props = this.props) => {
    const { store, compId, pageId, config, isMobile, isDesign, urlParams, comServicePath, isDoc, pluginCenter, events } = props;
    const _config = formatConfig(config);
    store.init(
      {
        compId,
        pageId,
        config: _config,
        isMobile,
        isDesign,
        comServicePath,
        isDoc,
        pluginCenter,
        events,
      },
      urlParams,
      this.onMindDataUpdate
    );
  };
  onMindDataByEvent = (data: any) => {
    const { store } = this.props;
    store.setMindMapData(data, [], (payload: any) => {
      this.setState({ mindMapDatas: toJS(payload.mindMapDatas), mindMapList: toJS(payload.mindMapList) }, () => {
        this.refreshView();
      });
    });
  };
  onMindDataUpdate = (data: any, config: any, cb?: Function) => {
    const { store } = this.props;
    // !先释放上一次内存占用
    this.resetJm(true, () => {
      const { mindMapList = [], mindMapDatas = this.state.mindMapDatas } = data;
      const appId = mindMapDatas[0]?.appid || '';
      const appTitle = mindMapDatas[0]?.title || config?.title || config?.name || '';
      this.setState({ ...initial_data(), appId, appTitle, mindMapDatas, mindMapList });
      runInAction(() => {
        store.setState({ loading: false });
        setTimeout(() => {
          this.contentSizeObserver();
        }, 300);
      });
    });
  };
  resetJm = (clearData: boolean, cb?: Function) => {
    this.setState({ jmLoaded: false, isFirstMount: true }, () => {
      const { store } = this.props;
      this.catchStyle = {
        width: 0,
        height: 0,
      };
      if (this.resizeObserver) this.resizeObserver.disconnect();
      // 先移除外部对导图插件监听事件
      this.unBindMindEvent();
      // 移除全局event事件监听
      handleOffEvent(this, true);
      // !节点释放 不然用户设计器下会越用越卡
      // 性能时间轴移除
      window.performance && window.performance.clearMarks();
      if (!this.nodes) this.nodes = [];
      for (let i = 0; i < this.nodes.length; i++) {
        const unmount = window.ReactDOM.unmountComponentAtNode(this.nodes[i]);
        if (unmount && this.nodes[i].parentNode) {
          this.nodes[i]?.parentNode?.parentNode?.removeChild(this.nodes[i]?.parentNode);
        }
      }
      // 再卸载子页面
      store.setState({ loading: true });
      setTimeout(() => {
        this.jmInstance = null;
        this.nodes = null;
        if (clearData) {
          this.setState({ ...initial_data() }, () => cb && cb());
        } else {
          cb && cb();
        }
      }, 100);
    });
  };
  resetLocalData = () => {
    sessionStorage.removeItem(mindMapUrlParamsCache);
  };
  // 更新视图-整个导图重新渲染
  refreshView = (config?: any) => {
    const { store } = this.props;
    if (store.loading) {
      return;
    }
    this.resetJm(false, () => {
      if (!isEmpty(config)) {
        const payload = {
          datas: this.state.mindMapDatas as any,
          config,
          direction: store.direction,
          isDesign: store.isDesign,
          showFieldKeys: store.showFieldKeys,
        }
        let mindList = formatMindMapData(payload);
        this.setState({ mindMapList: mindList });
      }
      store.setState({ loading: false });
      this.contentSizeObserver();
    });
  };
  // 只更新大小 不重新渲染
  onMapContentResize = () => {
    if (!this.jmInstance) return;
    const { compId, pluginCenter, events } = this.props;
    const sizeInfo = this.getContentStyle();
    this.jmInstance.resize();
    invoke(pluginCenter, MindDefaultPluginName, EbdMindMapEventKeys.onResize, { args: [sizeInfo] });
    events && events.emit && events.emit(`${EbdMindMapEventKeys.onResize}_${compId}`, sizeInfo);
  };
  debounceOnMapContentResize = debounce(this.onMapContentResize, 300);
  contentSizeObserver = () => {
    const { config, compId } = this.props;
    const isInFlow = !isEmpty(config.flow) && config.flow.parent && config.flow.parent !== 'ROOT';
    // 容器内不适应
    if (isInFlow) return;
    // const element = document.querySelector(`.ebcom.ebcom-mindmap`);
    const element = document.querySelector(`#${compId} .${mindMapViewClsPrefix}-container-content`);
    if (!element) {
      return;
    }
    if (this.resizeObserver) this.resizeObserver.disconnect();
    // 动态引入
    if (typeof ResizeObserver === 'undefined') {
      import('resize-observer-polyfill')
        .then(({ default: ResizeObserver }) => {
          // 使用 polyfill
          this.resizeObserver = new ResizeObserver(() => {
            if (element.clientWidth !== this.catchStyle.width || element.clientHeight !== this.catchStyle.height) {
              this.debounceOnMapContentResize();
            }
          });
          // 开始观察指定的元素
          this.resizeObserver.observe(element);
        })
        .catch((error) => {
          console.error('Failed to load ResizeObserver polyfill:', error);
        });
    } else {
      this.resizeObserver = new ResizeObserver(() => {
        if (element.clientWidth !== this.catchStyle.width || element.clientHeight !== this.catchStyle.height) {
          this.debounceOnMapContentResize();
        }
      });
      // 开始观察指定的元素
      this.resizeObserver.observe(element);
    }
  };

  onNodeClick = (node: JmNode) => (e: React.SyntheticEvent) => {
    e && e.stopPropagation();
    e && e.preventDefault();
    const { customParameters } = node;
    const { config, events, compId, pluginCenter } = this.props;
    if (customParameters?.actions) {
      let objId = '';
      const targetNode = getCurrentNode(config, node);
      if (targetNode) {
        objId = targetNode?.dataset?.id;
      }
      if (!config?.fromEbuilder) {
        const eventGroup = targetNode && targetNode.actions && targetNode.actions.eventGroup ? targetNode.actions.eventGroup : customParameters.actions?.eventGroup;
        if (!isEmpty(eventGroup)) {
          runComEventsAction(this.props, eventGroup, node, e, targetNode);
        }
      } else if (isArray(toJS(customParameters?.actions))) {
        const contents = JSON.parse(node.content);
        const { buttonFields, ...fields } = contents;
        onClickAction('', customParameters?.actions, {
          ...this.props,
          node,
          objId,
          fields: {
            ...fields,
          },
        });
      }
    }
    events && events.emit(`${EbdMindMapEventKeys.onNodeClick}_${compId}`, { node, e });
    invoke(pluginCenter, MindDefaultPluginName, EbdMindMapEventKeys.onNodeClick, { args: [{ node, e }] });
  };

  onMouseEnterNode = (_this: any) => {
    const node: JmNode = _this.nodeData?.data;
    // 有自定义渲染的 不走
    if (hasRegisterFuncInPlugin(this.props.pluginCenter, MindDefaultPluginName, EbdMindMapEventKeys.renderCustomMindMap)) {
      return;
    }
    const styleInfo = getNodeStyleInfo(this.props.config, node);
    let target = getTargetNode(node, this.jmInstance);
    if (target) {
      if (styleInfo.mouseEnterBackground) target.style.background = styleInfo.mouseEnterBackground;
      if (styleInfo.mouseEnterBorderColor) target.style.borderColor = styleInfo.mouseEnterBorderColor;
    }
  };

  onMouseLeaveNode = (_this: any) => {
    const node: JmNode = _this.nodeData?.data;
    // 有自定义渲染的 不走
    if (hasRegisterFuncInPlugin(this.props.pluginCenter, MindDefaultPluginName, EbdMindMapEventKeys.renderCustomMindMap)) {
      return;
    }
    const styleInfo = getNodeStyleInfo(this.props.config, node);
    let target = getTargetNode(node, this.jmInstance);
    if (target) {
      if (styleInfo.background) target.style.background = styleInfo.background;
      if (styleInfo.borderColor) target.style.borderColor = styleInfo.borderColor;
    }
  };
  onFieldVisibleChange = () => {
    const { events, compId, store } = this.props;
    if (!events || !events.on) {
      return;
    }
    events.on(EbdMindMapEventKeys.controlFieldsVisible, compId, (ids: string[]) => {
      if (isEqual(toJS(store.showFieldKeys), ids)) {
        return;
      }
      store.setState({ showFieldKeys: ids });
      this.refreshView();
    });
  };
  nodeRenderEngine = (node: any) => {
    if (node.nodeData.data.isroot) {
      node.setData({});
    }
    const newEle = this.nodeRenderCustom(node.nodeData.data, this.props);
    // 包装一层路由信息
    const BrowserRouter = window.ReactRouterDOM.BrowserRouter;
    const router = React.createElement(BrowserRouter, null, newEle);
    const nodeChild = React.createElement('div', null, router);
    const wrapEle = document.createElement('div');
    // 必须这么写 否则点击事件不生效
    if (!this.nodes) {
      this.nodes = [];
    }
    this.nodes = [...this.nodes, wrapEle];
    window.ReactDOM.render(nodeChild, wrapEle);
    return wrapEle;
  };
  nodeRenderCustom = (node: JmNode, props = this.props) => {
    const { topic, floor } = node;
    const { pluginCenter, config, store } = props;
    const { isDesign, showFieldKeys } = store;
    const hasActions: boolean = !isEmpty(node?.customParameters?.actions);
    const info = getCurrentNode(config, node);
    const sizeInfo = getSizeInfo(config, node);
    const styleInfo = getNodeStyleInfo(config, node);
    let showFields = getShowFields(showFieldKeys, info, config, isDesign);
    const { cardLayout = {} } = showFields || {};
    const isCustomRenderBefore = hasRegisterFuncInPlugin(pluginCenter, MindDefaultPluginName, EbdMindMapEventKeys.renderCardBefore);
    const isCustomRenderAfter = hasRegisterFuncInPlugin(pluginCenter, MindDefaultPluginName, EbdMindMapEventKeys.renderCardAfter);
    const cls = classnames({
      [`${mindMapViewClsPrefix}-card`]: true,
      [`${mindMapViewClsPrefix}-card-sm`]: !hasGridContent(cardLayout.grid || []) && !node.isroot,
      [`${mindMapViewClsPrefix}-card-active`]: hasActions,
      [`${mindMapViewClsPrefix}-card-customRender`]: isCustomRenderBefore || isCustomRenderAfter,
      [`${mindMapViewClsPrefix}-card-root`]: node.isroot,
      [`${mindMapViewClsPrefix}-card-second`]: node.floor === 2,
      [`${mindMapViewClsPrefix}-card-third`]: node.floor === 3,
      [`${mindMapViewClsPrefix}-card-adaptive`]: sizeInfo.width.type === 'adapt',
    });
    if (node.isroot) {
      const topic = isObject(info.name) ? info?.name?.nameAlias : info.name;
      if (!info) {
        return (
          <span key={node.id} className={cls} onClick={this.onNodeClick(node)}>
            {topic}
          </span>
        );
      } else {
        const { isShowName = true, iconInfo = {} } = info;
        let iconStyle = {
          ...(iconInfo?.style || {}),
          path: iconInfo.path,
          fontSize: info?.iconWidth || 60,
        };
        if (iconInfo.path && !(iconInfo.path.indexOf('#icon') === 0)) {
          //素材图片，非图标时，高度自适应
          iconStyle = { ...iconStyle, height: Number(info.iconHeight) || 60, width: Number(info.iconWidth) || 60 };
        }
        const { width, originWidth } = getAdaptiveWidth({ ...node, topic }, sizeInfo, {}, iconStyle, false);
        let nodeStyle = {
          width,
          height: +getAdaptiveHeight({ ...node, topic }, sizeInfo, null, iconStyle, originWidth),
          background: styleInfo.background,
          borderColor: `${styleInfo.borderColor}`,
          color: styleInfo.color,
        };
        return (
          <div key={node.id} className={cls} onClick={this.onNodeClick(node)} style={nodeStyle}>
            <div className={`${mindMapViewClsPrefix}-card-root-iconContainer`} style={{ width: iconStyle.width, height: iconStyle.height }}>
              {iconInfo.path && <CorsComponent weId={`${this.props.weId || ''}_rcsrvf`} app='@weapp/ebdcoms' compName='AssetsItem' path={iconInfo.path} style={{ ...iconStyle }} />}
            </div>

            {isShowName && <span className={classnames({ [`${mindMapViewClsPrefix}-card-root-topic-top`]: iconInfo.path })}>{topic}</span>}
          </div>
        );
      }
    }
    const layouts = flattenDeep(toJS(showFields?.cardLayout?.grid));
    let cardData = topic ? JSON.parse(topic) : {};
    const isTitle = node.floor === 2;
    const { width, originWidth } = getAdaptiveWidth(node, sizeInfo, cardLayout, {}, isTitle);
    // 空节点（没配置显示字段的）内边距为6px
    const height = +getAdaptiveHeight(node, sizeInfo, cardLayout, {}, originWidth);
    const background = styleInfo.background;
    const color = styleInfo.color;
    // 针对二三级节点
    // const borderColor = floor <= 3 ? styleInfo.borderColor || getPrimaryColorWithOpacity(0.2) : styleInfo.borderColor;
    const borderColor = floor <= 3 ? styleInfo.borderColor || '#CADDFF' : styleInfo.borderColor;
    const style = { width, height, background, borderColor, color };
    if (isEmptyShowFields(showFields) && isEmpty(layouts)) {
      return (
        <div key={node.id} className={`${cls} ${mindMapViewClsPrefix}-card-unShowFields`} onClick={this.onNodeClick(node)} style={{...style, width: 100, height: 26}}>
          {getLabel('129002', '请设置显示字段')}
        </div>
      );
    }
    const renderCard = () => {
      return (
        <>
          {invoke(pluginCenter, MindDefaultPluginName, EbdMindMapEventKeys.renderCardBefore, {
            args: [node],
          })}
          <LayoutCardWithRouter weId={`${this.props.weId || ''}_nhw9cr`} {...this.props} dataset={info?.dataset} data={cardData} config={showFields} customRenderCellRewrite={customRenderCell} useFieldCustomRender sort={`${node.floor}-${node.index}`} client='PC' />
          {invoke(pluginCenter, MindDefaultPluginName, EbdMindMapEventKeys.renderCardAfter, {
            args: [node],
          })}
        </>
      );
    };
    const renderMain = () => {
      return (
        <div id={node.id} style={style} className={cls} onClick={this.onNodeClick(node)} key={node.id}>
          {renderCard()}
        </div>
      );
    };
    if (hasRegisterFuncInPlugin(pluginCenter, MindDefaultPluginName, EbdMindMapEventKeys.renderCardContent)) {
      return (
        <div>
          {invoke(pluginCenter, MindDefaultPluginName, EbdMindMapEventKeys.renderCardContent, {
            hook: { style: { ...style, ...styleInfo }, renderMain },
          })}
        </div>
      );
    }
    return renderMain();
  };
  bindMindEvent = () => {
    if (!this.jmInstance) return;
    // * 绑定鼠标移入移出事件
    this.jmInstance.on('node_mouseenter', this.onMouseEnterNode);
    this.jmInstance.on('node_mouseleave', this.onMouseLeaveNode);
    this.jmInstance.on('showScrollBar', this.handleShowScrollBar);
  };
  unBindMindEvent = () => {
    if (!this.jmInstance) return;
    this.jmInstance.off('node_mouseenter', this.onMouseEnterNode);
    this.jmInstance.off('node_mouseleave', this.onMouseLeaveNode);
    this.jmInstance.off('showScrollBar', this.handleShowScrollBar);
  };
  handleShowScrollBar = (show: boolean) => {
    this.setState({ hideScrollBar: !show });
  };
  showScrollBar = () => {
    const { config, store } = this.props;
    const { jmLoaded, mindMapList } = this.state;
    const funcSetting = getFuncSetting(config);
    const { useScrollBar } = funcSetting.base;
    if (!jmLoaded || isEmpty(this.jmInstance)) {
      return false;
    }
    // 默认使用滚动条
    let _useScrollBar = useScrollBar;
    if (!('useScrollBar' in funcSetting.base)) {
      _useScrollBar = true;
    }
    // 设计器内默认不显示滚动条
    if (store?.isDesign) {
      _useScrollBar = false;
    }
    const isDataEmpty = !mindMapList.filter((i) => !i.isroot).length;
    return _useScrollBar && !isDataEmpty;
  };
  getInstance = (jm: any) => {
    this.jmInstance = jm;
    const { isFirstMount } = this.state;
    if (isFirstMount) {
      this.setState({ isFirstMount: false });
    }
    this.setState({ jmLoaded: true }, this.bindMindEvent);
  };

  getTitle = () => {
    return <span dangerouslySetInnerHTML={{ __html: this.state.appTitle }} />;
  };
  // 处理设计器容器内大小可拖动并适配
  getContentStyle = () => {
    const { config, store, page } = this.props;
    const ele = document.querySelector(`.ebpage.ebpage-pc`);
    const fullEle = document.fullscreenElement;
    let height = '100%';
    let width = '100%';
    // 新版本全屏
    if (fullEle) {
      height = `${fullEle?.clientHeight}px`;
      width = `${fullEle?.clientWidth}px`;
    } else {
      // 如果在容器内 只给默认高度 容器内高度会不停闪烁
      if (!isEmpty(config.flow) && config.flow.parent && config.flow.parent !== 'ROOT') {
        // @ts-ignore
        // let eleHeight = ele?.clientHeight - 70 - (page.module === 'EB_FORM_VIEW' ? 50 : 0);
        // height = +eleHeight < InContainerHeight ? `${InContainerHeight}px` : `${eleHeight}px`;
        height = `${InContainerHeight}px`;
      }
    }
    // 打开侧滑窗口需要父级宽度计算
    const sizeInfo = { wrapperHeight: height, wrapperWidth: width, ele };
    store.setState(sizeInfo);
    // 缓存处理下浏览器窗口变化
    const element = document.querySelector(`.scrollbar-standalone`);
    this.catchStyle = {
      width: element?.clientWidth || 0,
      height: element?.clientHeight || 0,
    };
    return sizeInfo;
  };
  renderMindContent = () => {
    const { mindMapList, isFirstMount } = this.state;
    const { store, onRef, config, pluginCenter } = this.props;
    const { loading, defaultExpendDepth } = store;
    let { isHideRoot } = config;
    if (loading) {
      return <div />;
    }
    const funcSetting = getFuncSetting(config);
    const mindData: MindData = {
      meta: {
        name: 'MindMap',
      },
      format: 'node_array',
      data: mindMapList,
      supportHtml: true,
    };
    const renderMindMap = () => (
      <MindMap
        weId={`${this.props.weId || ''}_0h6j2a`}
        onRef={onRef}
        datas={mindData}
        nodeRenderCustom={this.nodeRenderEngine}
        funcSetting={funcSetting}
        isHideRoot={!!isHideRoot}
        isFirstMount={isFirstMount}
        getInstance={this.getInstance}
        defaultExpendDepth={defaultExpendDepth}
        refreshView={this.refreshView}
        parentProps={this.props}
      />
    );
    if (hasRegisterFuncInPlugin(pluginCenter, MindDefaultPluginName, EbdMindMapEventKeys.renderCustomMindMap)) {
      return (
        <>
          {invoke(pluginCenter, MindDefaultPluginName, EbdMindMapEventKeys.renderCustomMindMap, {
            args: [mindData, renderMindMap()],
          })}
        </>
      );
    }
    return renderMindMap();
  };
  renderContent = () => {
    const { appId, updateKey } = this.state;
    const { store, isDesign, page, config, pluginCenter } = this.props;
    const { wrapperHeight, wrapperWidth } = store;
    const { fromEbuilder } = config;
    const isFromEbuilder = fromEbuilder && !isDesign;
    let sizeInfo = { height: wrapperHeight, width: wrapperWidth };
    if (hasRegisterFuncInPlugin(pluginCenter, MindDefaultPluginName, EbdMindMapEventKeys.getMindSize)) {
      sizeInfo = invoke(pluginCenter, MindDefaultPluginName, EbdMindMapEventKeys.getMindSize);
    }
    return (
      <div className={`${mindMapViewClsPrefix}-container-content`} key={updateKey}>
        {invoke(pluginCenter, MindDefaultPluginName, EbdMindMapEventKeys.renderContentTop)}
        {isFromEbuilder && (
          <div className={`${mindMapViewClsPrefix}-header`}>
            <TitleNew weId={`${this.props.weId || ''}_1z0igb`} getTitle={this.getTitle} appId={page?.appid || appId} />
          </div>
        )}
        <div className={`${mindMapViewClsPrefix}`}>
          <div style={{ width: sizeInfo.width, height: sizeInfo.height }}>
            {invoke(pluginCenter, MindDefaultPluginName, EbdMindMapEventKeys.renderContentBefore)}
            {this.renderMindContent()}
            {invoke(pluginCenter, MindDefaultPluginName, EbdMindMapEventKeys.renderContentAfter)}
          </div>
        </div>
        {invoke(pluginCenter, MindDefaultPluginName, EbdMindMapEventKeys.renderContentBottom)}
      </div>
    );
  };

  render() {
    const { store, config, isDesign } = this.props;
    const { hideScrollBar } = this.state;
    const { loading, onClose, slideModalUrl, visible, dialogWidthParams } = store;
    const { fieldFilterSet, fromEbuilder } = config;
    const isFromEbuilder = fromEbuilder && !isDesign;
    const urlDialogParams = {
      visible,
      onClose,
      wrapClassName: `${mindMapViewClsPrefix}-listview-drawer`,
      ...dialogWidthParams,
    };
    const cacheExpand = sessionStorage.getItem('cacheExpand');
    const _Spin = cacheExpand ? null : <Spin spinning={loading} weId={`${this.props.weId || ''}_xrmzqc`} className={`${mindMapViewClsPrefix}-ctLoading`} />;
    return (
      <>
        {/* <Button onClick={() => this.resetJm(true, () => {
          store.setState({ loading: false })
        })}>重置</Button> */}
        <div className={`${mindMapViewClsPrefix}-container ${isFromEbuilder ? `fromEbuilder` : ''}`}>
          <FPSMonitor weId={`${this.props.weId || ''}_duszy4`} />
          <When weId={`${this.props.weId || ''}_43tc7o`} condition={!!fieldFilterSet?.enable}>
            <FieldsVisibleSet weId={`${this.props.weId || ''}_t7xysk`} {...this.props} disabled={loading} />
          </When>
          {loading ? _Spin : this.renderContent()}
          {this.showScrollBar() ? <ScrollBar weId={`${this.props.weId || ''}_bnlom5`} mindMapInstance={this.jmInstance ?? {}} hideScrollBar={hideScrollBar} /> : null}
        </div>
        {/* 加载其余视图（列表、日历等）和iframe */}
        <UrlDockerDialog weId={`${this.props.weId || ''}_97be62`} dialogParams={urlDialogParams} url={slideModalUrl} type='href' />
      </>
    );
  }
}

export default withRouter(MindMapContent) as unknown as ComponentType<any>;
