@import (reference) '../../../../style/index.less';

.ui-mind-map {
  height: 100%;
  width: 100%;
  position: relative;
  &-container {
    height: 100%;
    width: 100%;
    * {
      margin: 0;
      padding: 0;
    }
    &.hide{
      opacity: 0;
    }
    .smm-node-container{
      // 走自定义卡片配置的样式
      .smm-node > path {
        display: none;
      }
      .smm-node:not(.smm-node-dragging):hover .smm-hover-node{
        display: none !important;
      }
    }
    &.fade-container {
      animation-duration: 1s;
      animation-fill-mode: both;
      width: 100%;
      height: 100%;
    }
    &.fade-in {
      animation-name: fadeIn;
    }
    @keyframes fadeIn {
      from {
        opacity: 0;
      }
      to {
        opacity: 1;
      }
    }
  }
  .@{mindMapViewPrefix}-footer {
    position: absolute;
    bottom: 60px;
    right: 30px;
    z-index: 3;
    box-shadow: 1px 2px 4px rgba(0,0,0,0.15);
    & > div{
      display: inline-block;
      &>span {
        display: inline-block;
        height: 35px;
        padding: 0 8px;
        text-align: center;
        background-color: #fff;
        color: #999;
        line-height: 35px;
        cursor: pointer;
        font-size: var(--font-size-14)
      }
    }
    .mind-zoom-percent{
      user-select: none;
    }
  }
  .@{mindMapViewPrefix}-legend-box {
    position: absolute;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    width: 40px;
    height: 40px;
    font-size: 12px;
    display: flex;
    align-items: center;
    border-radius: 50%;
    justify-content: center;
    box-shadow: 0 0 9px 0 rgb(0 0 0 / 12%);
    background: var(--bg-white);
    right: 106px;//75+16padding+15
    top: 25px;
    color: var(--primary);
    z-index: 12;
    .text{
      cursor: pointer;
    }
   
  }
  .@{mindMapViewPrefix}-legend-container {
    position: absolute;
    top: 25px;
    right: 45px;
    z-index: 999;
    box-shadow: 1px 2px 4px rgba(0,0,0,0.15);
    width: 156px;//150 + 16padding
    max-height: calc(100% - 130px);//60+35+10+25
    background-color: white;
    .legend-container-content {
      height: 100%;
      // overflow-y: auto;
      .folder-arrow {
          text-align: center;
          cursor: pointer;
          padding: 4px;
      }
      .all-item{
        padding: 0 16px;
        overflow-y: auto;
        max-height: calc(100% - 26px);
        .weapp-ebdmindmap-mindmapv-lengend-item{
          color: white;
          display: flex;
          justify-content: space-between;
          align-items: center;
          height: 35px;
          font-size: 12px;
          cursor: default;
          &-name{
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            max-width: 80px;
            padding: 4px;
            border-radius: 5px;
          }
          &-time{
            color: var(--main-fc);
            max-width: 30px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            padding-left: 4px;
          }
        }
        .weapp-ebdmindmap-mindmapv-lengend-item:not(:last-child){
          border-bottom: 1px solid rgb(229, 229, 229);
        }
      }
  }
  }
  &.ui-mind-map-full-screen {
    position: fixed;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
    margin: auto;
    z-index: 1005;
    background-color: #fff;
  }
  jmexpander {
    line-height: 13px;
  }
}
.@{mindMapViewPrefix}-quick-menu{
  background: #900;
  .ui-m-quick-menu-start-btn{
    z-index: 99;
    position: absolute;
    bottom: 30px;
    cursor: pointer;
  }
}
#mindMapContainer{
  height: 100%;
  width: 100%;
  * {
    margin: 0;
    padding: 0;
  }
}