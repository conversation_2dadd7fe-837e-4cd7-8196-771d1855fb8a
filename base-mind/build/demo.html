<meta charset="utf-8" />
<title>simpleMindMap demo</title>
<!-- <script src="https://cdn.polyfill.io/v3/polyfill.min.js?features=es2015"></script>  -->
<script src="./build/polyfill.es5.min.js"></script>
<script src="./build/polyfill.svg.min.js"></script>
<script src="./build/mind.js"></script>

<body>
  <div id="mindMapContainer" style="width: 1000px; height: 700px; overflow: hidden;"></div>
</body>
<script>
  var _MindMap = window.simpleMindMap;
  var json = {"layout":"logicalStructure","root":{"data":{"text":"根节点","richText":false,"expand":true,"isActive":false,"uid":"a047b11b-ec9b-4d82-b4ca-bdb153670276","shape":"rectangle","lineDasharray":"none"},"children":[{"data":{"text":"二级节点","uid":"5a052c0e-6490-41b6-a100-7ae6a238f56d","expand":true,"richText":false,"isActive":false},"children":[{"data":{"text":"二级节点-分支主题1","uid":"4188c37a-b945-4b2d-ac4b-2f6f52c37a3c","expand":true,"richText":false,"isActive":false},"children":[{"data":{"text":"二级节点-分支主题2","uid":"e42d7c67-1e17-48ce-9390-b89bdd5990e5","expand":true,"richText":false,"isActive":false},"children":[]}]}]},{"data":{"text":"四级节点","uid":"350d1dab-80d7-4731-9d19-e43b857a629a","expand":true,"richText":false,"isActive":false},"children":[{"data":{"text":"分支主题","uid":"e2861b52-4bd8-4770-a20b-312c182affdf","expand":true,"richText":false,"isActive":false},"children":[]}]}]},"theme":{"template":"cactus","config":{"lineStyle":"curve","rootLineKeepSameInCurve":true,"rootLineStartPositionKeepSameInCurve":true,"showLineMarker":false,"nodeUseLineStyle":false}},"view":{"transform":{"scaleX":1,"scaleY":1,"shear":0,"rotate":0,"translateX":-257,"translateY":-100.5,"originX":0,"originY":0,"a":1,"b":0,"c":0,"d":1,"e":-257,"f":-100.5},"state":{"scale":1,"x":-257,"y":-100.5,"sx":-360,"sy":10}}}
  var mindMap = new _MindMap({
    el: document.getElementById('mindMapContainer'),
    data: json.root,
    isUseCustomNodeContent: true,
    disableMouseWheelZoom: true,
    customCreateNodeContent: function (node) {
      if (node.nodeData.data.isroot) {
        node.setData({});
      }
      var s_div = document.createElement('div');   // 创建节点
      s_div.innerHTML = node.nodeData.data.text;
      // 渲染卡片
      return s_div
    },
  });
</script>