/**
 * 思维导图-图例设置
 */
import { Button, Dialog, Table, ColorPicker, Checkbox, Icon, CorsComponent } from '@weapp/ui';
import { getLabel, isEmpty, cloneDeep, isEqual } from '@weapp/utils';
import { observer } from 'mobx-react';
// import { isEmpty, cloneDeep, isEqual } from 'lodash-es';
import React from 'react';
import { ebdfClsPrefix, dlgIconName } from '../../../../../constants';
import utils from '../../../../../utils/index';
import { NodeKeyDesignComProps } from '../../../types';
import LocaleInput from '../../../../../components/common/locale/LocaleInput';
import { LocaleExValueDataType } from '@weapp/ebdcoms/lib/common/locale/types';
import configStore from '../store';
import { MarkItemProps } from './types';
import NodeKeyStyle from './nodeKeyStyle';
import ebdcoms from '../../../../../utils/ebdcoms';
import { toJS } from 'mobx';
import './index.less';

const cls = `${ebdfClsPrefix}-lineMarkSet`;
const { LocaleEx } = ebdcoms.get();

const INITIAL_DATA = () => ({
  id: utils?.UUID(),
  name: '',
  styleSetting:{
    color: '#666',
    textColor:'#fff',
    countColor:"#333",
  },
  filter: {},
});

const INITIAL_STATE = () => ({
  visible: false,
  legend: [] as MarkItemProps[],
  selectedRowKeys: [] as string[],
  currentSelectedFilterId: -1,
  visbleFilterDialog:false,
  refreshKey: null,// legend层级太深，变化不会触发Content的didupdate 
})
@observer
class LineMarkSet extends React.PureComponent<NodeKeyDesignComProps,any> {
  state = INITIAL_STATE()

  componentDidMount() {
    this.init();
  }
  componentWillReceiveProps(nextProps: Readonly<NodeKeyDesignComProps>): void {
    const { config: _config } = nextProps
    const { config } = this.props
    if (!isEqual(_config.dataset, config.dataset) && _config.dataset && config.dataset) {
      this.setState({ ...INITIAL_STATE() });
    }
  }
  init = () => {
    const { nodeKeySet = {} } = this.props?.form?.datas;
    const { dataset } = this.props.config;
    if (dataset) {
      configStore.getDataFields(dataset);
    }
    if (!isEmpty(nodeKeySet)) {
      const { legend, refreshKey } = nodeKeySet;
      this.setState({ legend:toJS(legend), refreshKey });
    }
  };

  triggerShow = () => {
    this.setState((prevState: any) => ({
      visible: !prevState.visible,
    }));
  };

  onMainDialogCancel = () => {
    const { nodeKeySet = {} } = this.props?.form?.datas;
    const { legend = {}, refreshKey = '' } = nodeKeySet;
    const { legend:_legend = {} } = this.state;
    if (!isEmpty(legend) && !isEqual(legend, _legend)) {
      this.setState({ legend:toJS(legend), refreshKey,visible: false, selectedRowKeys:[]});
    }else{
      this.setState({ visible: false, selectedRowKeys:[] });
    }
  };
  onMainDialogSave = () => {
    const { legend,refreshKey  } = this.state;
    const { onChange } = this.props;
    const payload = {
      legend:toJS(legend),
      refreshKey,
    };
    onChange && onChange(payload);
    this.setState({ visible: false });
  };

  onItemChange = (rowData: MarkItemProps, changeType: keyof MarkItemProps, changeValue: any) => {
    const { nodeKeySet = {} } = this.props?.form?.datas;
    const {  legend:_legend = []} = this.state;
    // let legend = toJS(_legend);
    let legend = this.cloneDeepAndtoJS([..._legend]);
    const idx = legend.findIndex((i:any) => i.id === rowData.id);
    const isChange = !isEqual(nodeKeySet?.legend?.[idx]?.[changeType], changeValue);
    legend[idx][changeType] = changeValue;
    this.setState({ 
      legend:legend, 
      refreshKey:isChange? Math.random():nodeKeySet?.refreshKey
    });
  };
  showDetailSet = (data: MarkItemProps) => {
    const { legend =  [] } = this.state;
    const idx = legend.findIndex(i => i.id === data.id) ?? -1;
    this.setState({currentSelectedFilterId: idx,visbleFilterDialog : true});
  };
  addData = () => {
    this.setState((prevState: any) => ({
        legend:toJS([...prevState['legend'], INITIAL_DATA()]),
    }));
  };
  delData = () => {
    const { selectedRowKeys, legend = [] } = this.state;
    const newData = legend.filter((item: MarkItemProps) => !selectedRowKeys.includes(item.id));
    this.setState({ legend: toJS(newData), selectedRowKeys: [] });
  };
  onSelect = (selectedRowKeys: string[]) => {
    this.setState({ selectedRowKeys });
  };

  renderMainDialog = () => {
    const { visible, selectedRowKeys, legend = [] } = this.state;
    const mainCls = `${cls}-mainDialog`;
    const ebBusinessId = '';
    const tableData = toJS(legend);
    const buttons = [
      <Button weId={`${this.props.weId || ''}_952mww`} key="save" type="primary" onClick={this.onMainDialogSave}>
        {getLabel('40496', '保存')}
      </Button>,
      <Button weId={`${this.props.weId || ''}_6xrmqg`} key="onCancel" onClick={this.onMainDialogCancel}>
        {getLabel('53937', '取消')}
      </Button>,
    ];

    const columns = [
      {
        dataIndex: 'name',
        title: getLabel('53866', '名称'),
        bodyRender: (data: MarkItemProps) => {
          return (
            <LocaleEx weId={`${this.props.weId || ''}_v2882x`}
              targetId={this.props.pageId} 
              value={toJS(data.name)}
              // ebBusinessId={ebBusinessId}
              placeholder={getLabel('56234', '请输入')}
              disabled={undefined}
              onChange={(inputValue: LocaleExValueDataType) => this.onItemChange(data, 'name', inputValue)}
              inputProps={{
                maxLength: 50
              }}
            />
          );
        },
      },
      {
        dataIndex: 'styleSetting',
        title: getLabel('56341', '样式设置'),
        bodyRender: (data: MarkItemProps) => {
          return (
            <NodeKeyStyle weId={`${this.props.weId || ''}_79124l`}
            rowData={data}
            onItemChange = {this.onItemChange}
            />
          );
        },
      },                 
      {
        dataIndex: 'filter',
        title: getLabel('109838', '条件设置'),
        bodyRender: (data: MarkItemProps) => {
          return (
            <div className={`${mainCls}-table-block`}>
              <Button weId={`${this.props.weId || ''}_848lca`} type="link" onClick={() => this.showDetailSet(data)}>
                {isEmpty(data.filter) ? getLabel('56776', '添加条件') : getLabel('56775', '编辑条件')}
              </Button>
            </div>
          );
        },
      },
    ];

    const renderTable = () => {
      return (
        <Table weId={`${this.props.weId || ''}_1encu2`}
          columns={columns}
          data={tableData}
          isShowIndex={true}
          isShowIndexInCheck
          selection={{
            selectedRowKeys,
            isBatchSelect: true,
            onSelect: this.onSelect,
          }}
        />
      );
    };
    const addContent = () => {
      return (
        <div className={`${mainCls}-add`}>
          <div className={`${mainCls}-add-left`}>
            {/* {getLabel('262663', '节点图例设置')} */}
          </div>
          <div className={`${mainCls}-right`}>
            {<Icon weId={`${this.props.weId || ''}_tzwkia`} name="Icon-add-to03" onClick={this.addData} size="md" />}
            {!isEmpty(selectedRowKeys) && <Icon weId={`${this.props.weId || ''}_l4sqc5`} name="Icon-Batch-delete" onClick={this.delData} size="md" />}
          </div>
        </div>
      );
    };
    return (
      <Dialog weId={`${this.props.weId || ''}_tuwl4y`}
        footer={buttons}
        width={700}
        destroyOnClose
        visible={visible}
        title={getLabel('262663', '节点图标设置')}
        mask
        closable
        onClose={this.onMainDialogCancel}
        icon={dlgIconName}
        wrapClassName={mainCls}
      >
        {addContent()}
        {renderTable()}
      </Dialog>
    );
  };

  cloneDeepAndtoJS = (data: any = []) =>{
    let _data:any = [];
    toJS(data).forEach((item: any = {}) => {
      let _item :any= {};
       Object.keys(item).forEach((key: string) => {
        _item[key] = toJS(item[key]);
       })
       _data.push(toJS(_item));
    })
    return cloneDeep(toJS(_data));
  }

  renderFilterDialog = () => {
    const { nodeKeySet = {} } = this.props?.form?.datas;
    let { legend = [], currentSelectedFilterId, visbleFilterDialog } = this.state;
    const { config } = this.props;
    const { dataset } = config;
    let _setDetailInfo = this.cloneDeepAndtoJS([...legend]);
    let currentData  = _setDetailInfo?.[currentSelectedFilterId]||{};
    const onFilterDialogCancel = () => {
        this.setState({ visbleFilterDialog: false,currentSelectedFilterId: -1 });
      };
    const onFilterDialogSave = (val: any) => {
        const isChange = !isEqual(nodeKeySet?.legend?.[currentSelectedFilterId]?.filter??{}, val);
        _setDetailInfo[currentSelectedFilterId].filter = val;
        this.setState({ legend: toJS(_setDetailInfo), refreshKey:isChange? Math.random():nodeKeySet?.refreshKey});
        onFilterDialogCancel()
    };

    return (
    visbleFilterDialog &&
      <CorsComponent
        weId={`${this.props.weId || ''}_rtv1f2`}
        app="@weapp/components"
        compName="ConditionSetDlg"
        value={currentData?.filter}
        onOk={onFilterDialogSave}
        onCancel={onFilterDialogCancel}
        onClear={() => onFilterDialogSave({})}
        visible
        title={getLabel('161820', '占用标识条件')}
        dataSet={dataset}
        showFilterType // 是否展示sql和自定义接口筛选类型
      />
    );
  };

  render() {
    return (
      <div className={cls}>
        <Button
          weId={`${this.props.weId || ''}_ge5nh8`}
          type="default"
          className={`${ebdfClsPrefix}-board-config-btn`}
          onClick={this.triggerShow}
        >
          <span>{getLabel('262663', '节点图例设置')}</span>
        </Button>
        {this.renderMainDialog()}
        {this.renderFilterDialog()}
      </div>
    );
  }
}
export default LineMarkSet;
