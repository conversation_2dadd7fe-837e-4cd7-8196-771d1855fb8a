import { PureComponent } from "react";
import { withRouter } from "react-router-dom";
import { CorsComponent } from "@weapp/ui";
import { MainProps } from "./types";
import { ebdMindMapComsClsPrefix } from "../constants";

class Main extends PureComponent<MainProps>{
  render() {
    console.log('[ main ] >',)
    return (
      <div className={`${ebdMindMapComsClsPrefix}-demo`}>
        <CorsComponent weId={`${this.props.weId || ''}_qoyb1k`}
          app="@weapp/designer-demo"
          compName="FlowDesignerDemo"
        />
      </div>
    )
  }
}

export default withRouter(Main);