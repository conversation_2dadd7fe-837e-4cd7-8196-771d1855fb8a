import { AnyObj } from '@weapp/ui';
import { Attributes } from 'react';
import { RouteComponentProps } from 'react-router-dom';

import { MindMapViewStore } from './store';
import { Direction, ViewComProps } from '../../../common/types';
import { MindPluginCenter } from '../../types';

export interface MindMapViewProps extends RouteComponentProps, ViewComProps {
  page: {
    appIconPath: string;
    appid: string;
    client: 'PC' | 'MOBILE';
    datasetVals: any[];
    id: string;
    layoutType: string;
    module: string;
  };
}

export interface MindMapContentProps extends Attributes, MindMapViewProps {
  urlParams: AnyObj;
  store: MindMapViewStore;
  events?: any;
  isDoc?: boolean;
}

export interface LinkInfo {
  name?: string;
  page: any;
  openMode: string;
  winSize?: string;
  params?: any[];
  layoutType?: any;
  ownParams?: any;
  linkUrl?: string;
  winSizeNew?: string;
  isUnitFill?: boolean;
}

export interface BaseParamsProps {
  compId: string;
  pageId: string;
  config: any;
  isMobile: boolean;
  isDesign: boolean;
  ebParams?: AnyObj; // eb参数
  pluginCenter?: MindPluginCenter;
  events?: any;
  comServicePath?: string;
}

export interface TreeDataItem {
  id: string;
  content: string;
  parentid: string;
  disabled?: boolean;
  disabledCheck?: boolean;
  isLeaf?: boolean;
  customParameters: AnyObj;
  children: TreeDataItem[];
  title?: string;
  icon?: any;
  floor: number;
  defaultExpendDepth?: number;
  appid?: string;
}

export interface MindMapDataItem {
  id: string;
  isroot: boolean;
  parentid: string;
  topic: string;
  expanded: boolean;
  direction: Direction;
  floor: number;
  nodeStyle?: any;
  commonNodeStyle?: any;
  'leading-line-color'?: string;
  expandColor?: string;
}

export interface JmNode extends MindMapDataItem {
  children?: JmNode[];
  data: TreeDataItem;
  index: number;
  parent: JmNode;
  uid?: string;
  _data: {
    layout: AnyObj;
    view: AnyObj;
  };
  [x: string]: any;
}
export interface MindMapDataPayload {
  mindMapDatas: TreeDataItem[];
  mindMapList?: MindMapDataItem[];
}
