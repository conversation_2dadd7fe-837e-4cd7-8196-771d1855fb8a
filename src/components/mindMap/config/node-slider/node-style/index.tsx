import React from 'react';
import { observer } from 'mobx-react';
import { Form, Button, Dialog } from '@weapp/ui';
import { getLabel, isEmpty } from '@weapp/utils';
// import isEmpty from 'lodash-es/isEmpty';
import { NodeStyleStore } from './store';
import { NodeStyleDataType } from './types';
import { INIT_NODE_STYLE, ebdfClsPrefix } from '../../../../../constants';
const cls = `${ebdfClsPrefix}-mindmap-node-slider-node-style`;
interface Props {
  value?: NodeStyleDataType;
  config?: any;
  rootProps?: any;
  onChange?: (changeData: NodeStyleDataType) => void;
  onConfigChange?: (changeData: any) => void;
  isRoot?: boolean;
  isCommon?: boolean;
}
@observer
class NodeStyleSet extends React.PureComponent<Props> {
  state = {
    visible: false,
  };
  store = new NodeStyleStore();
  componentDidMount() {
    this.init();
  }
  // 是否为通用设置
  isCommonConfig = () => {
    return this.props.isCommon;
  };
  init = () => {
    try {
      const { value, config, isRoot = false, rootProps } = this.props;
      const { commonConfig, commonNodeStyleSet } = config;
      if (this.isCommonConfig()) {
        const { lineStyle } = config;
        let styleValue = INIT_NODE_STYLE() as NodeStyleDataType;
        // 判断是否同步标识 兼容低版本 同步过就不再取这里的值
        if (!isEmpty(commonConfig?.nodeStyle) && isEmpty(commonNodeStyleSet)) {
          styleValue = commonConfig?.nodeStyle;
        } else if (!isEmpty(value)) {
          styleValue = value;
        }
        this.store.init(styleValue, isRoot, true, lineStyle);
      } else {
        const {lineStyle} = rootProps?.config;
        const styleValue = isEmpty(value) ? INIT_NODE_STYLE() : value;
        this.store.init(styleValue, isRoot, false, lineStyle);
      }
    } catch (e) {}
  };
  changeVisible = (visible: boolean) => {
    this.setState({ visible }, () => {
      if (visible) {
        this.init();
      }
    });
  };
  reset = () => {
    const { isRoot = false } = this.props;
    const icCommon = this.isCommonConfig();
    const styleValue = INIT_NODE_STYLE(!icCommon);
    this.store.init(styleValue, isRoot, icCommon);
  };
  onSure = () => {
    this.changeVisible(false);
    const { onChange } = this.props;
    const { formData } = this.store;
    let newValues = {
      styleType: formData.styleType,
      background: formData.background || '',
      mouseEnterBackground: formData.mouseEnterBackground || '',
      borderColor: formData.borderColor || '',
      mouseEnterBorderColor: formData.mouseEnterBorderColor || '',
      lineColor: formData.lineColor || '',
      expandColor: formData.expandColor || '',
    };
    // * 去掉默认的透明
    for (let i in newValues) {
      const key = i as keyof typeof newValues;
      if (newValues[key] === 'transparent') {
        newValues[key] = '';
      }
    }
    // 更新value
    onChange && onChange(newValues);
  };
  renderForm = () => {
    const { visible } = this.state;
    const { formStore } = this.store;
    const formData = formStore.getFormDatas();
    const title = `${getLabel('247790', '节点样式设置')}`;
    if (!visible) {
      return null;
    }
    const footer = () => [
      <Button weId={`${this.props.weId || ''}_9le4rz`} key='sure' type='primary' onClick={this.onSure}>
        {getLabel('40565', '确定')}
      </Button>,
      <Button weId={`${this.props.weId || ''}_4assuu`} key='cancel' onClick={() => this.changeVisible(false)}>
        {getLabel('53937', '取消')}
      </Button>,
      <Button weId={`${this.props.weId || ''}_4assuu`} key='cancel' onClick={() => this.reset()}>
        {getLabel('56004', '重置')}
      </Button>,
    ];
    return (
      <Dialog weId={`${this.props.weId || ''}_p401ie`} title={title} closable width={400} onClose={() => this.changeVisible(false)} destroyOnClose visible={visible} mask footer={footer()}>
        <Form weId={`${this.props.weId || ''}_kofeue`} className={formData.styleType === 'common' ? `${cls}-formHide` : ''} store={this.store.formStore} onChange={this.store.omFormChange} />
      </Dialog>
    );
  };
  render() {
    return (
      <div className={cls}>
        <Button weId={`${this.props.weId || ''}_iv03jz`} onClick={() => this.changeVisible(true)} size={this.isCommonConfig() ? 'small' : 'middle'}>
          {getLabel('54205', '设置')}
        </Button>
        {this.renderForm()}
      </div>
    );
  }
}
export default NodeStyleSet;
