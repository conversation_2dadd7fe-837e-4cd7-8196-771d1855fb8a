import { qs } from '@weapp/utils';

/**
 * @function getSearchParams
 * @desc 获取url参数
 * @return object [key:value]
 */
export const getSearchParams = () => {
  const location = window.__location__ || window.location;
  if (!location.href.includes('?')) return;
  const hrefArr = location.href.split('?');
  const queryParams = hrefArr.length > 0 ? hrefArr[hrefArr.length - 1] : '';
  // 重置掉__location__ ,放置影响关联页面参数取值
  // ios下页面url参数取参存在问题
  window.__location__ = null;
  return qs.parse(queryParams);
};

const parseURL = {
  getSearchParams,
};

export type CutRule = (path: string, word: string) => boolean

export default parseURL;
