/**
 * 思维导图-图例设置
 */
import { Button, Dialog, Table, ColorPicker, Checkbox, Icon, CorsComponent } from '@weapp/ui';
import { getLabel, isEmpty, cloneDeep, isEqual } from '@weapp/utils';
import { observer } from 'mobx-react';
// import { isEmpty, cloneDeep, isEqual } from 'lodash-es';
import React from 'react';
import { ebdfClsPrefix, dlgIconName } from '../../../../../constants';
import utils from '../../../../../utils/index';
import { NodeKeyDesignComProps } from '../../../types';
import { styleSettingProps } from './types';
import './index.less';

const cls = `${ebdfClsPrefix}-lineMarkSet-mainDialog`;

const INITIAL_STATE = () => ({
  visible: false,
  color: '#666',
  textColor:'#fff',
  countColor:"#333",
})
@observer
class NodeKeyStyle extends React.PureComponent<any,any> {
  state = INITIAL_STATE()

  componentDidMount() {
    const { rowData } = this.props;
    const { color, textColor, countColor } = rowData?.styleSetting||{};
    if (color!=='#666' || textColor !== '#fff' ||countColor !== "#333") {
      this.setState({ color, textColor, countColor });
    }
  }

  // componentDidUpdate(prevProps: Readonly<any>, prevState: Readonly<any>, snapshot?: any): void {
  //   const { rowData: _RowData } = prevProps
  //   const { color:_color, textColor:_textColor, countColor:_countColor } = _RowData?.styleSetting||{};
  //   const { rowData } = this.props;
  //   const { color, textColor, countColor } = rowData?.styleSetting||{};
  //   if (color!==_color || textColor !== _textColor ||countColor !== _countColor) {
  //     this.setState({ color:_color, textColor:_textColor, countColor:_countColor });
  //   }
  // }

  triggerShow = () => {
    this.setState((prevState: any) => ({
      visible: !prevState.visible,
    }));
  };

  onMainDialogCancel = () => {
    this.setState({ visible: false });
  };
  onMainDialogSave = () => {
    const { rowData,onItemChange,  } = this.props;
    const {  color, textColor, countColor} = this.state;
    onItemChange(rowData,'styleSetting',{color,textColor,countColor})
    this.onMainDialogCancel();
  };

  onStateChange = (changeType: keyof styleSettingProps, changeValue: any) => {
    this.setState({ [changeType]: changeValue });
  };

  renderMainDialog = () => {
    const { visible, color, textColor, countColor} = this.state;
    const mainCls = `${cls}-nodeKeyStyle-dialog`;
    const buttons = [
      <Button weId={`${this.props.weId || ''}_9jcdqm`} key="save" type="primary" onClick={this.onMainDialogSave}>
        {getLabel('40496', '保存')}
      </Button>,
      <Button weId={`${this.props.weId || ''}_qicnnh`} key="onCancel" onClick={this.onMainDialogCancel}>
        {getLabel('53937', '取消')}
      </Button>,
    ];

    return (
      <Dialog weId={`${this.props.weId || ''}_mriakl`}
        footer={buttons}
        width={500}
        destroyOnClose
        visible={visible}
        title={getLabel('270855', '图例样式设置')}
        mask
        closable
        onClose={this.onMainDialogCancel}
        icon={dlgIconName}
        wrapClassName={mainCls}
      >
        <div className='content'>
          <div className='set-main'>
            <div className={`name`}>{getLabel('54606', '背景色')}</div>
            <div className={`setting`}>
              <ColorPicker weId={`${this.props.weId || ''}_uo7hlw`}
                onOk={(value: string) => this.onStateChange('color', value)}
                value={color}
              />
            </div>
          </div>
          <div className='set-main'>
            <div className={`name`}>{getLabel('106972', '文字颜色')}</div>
            <div className={`setting`}>
              <ColorPicker weId={`${this.props.weId || ''}_vew1kz`}
                onOk={(value: string) => this.onStateChange('textColor', value)}
                value={textColor}
              />
            </div>
          </div>
          <div className='set-main'>
            <div className={`name`}>{getLabel('270856', '统计数字颜色')}</div>
            <div className={`setting`}>
              <ColorPicker weId={`${this.props.weId || ''}_883b96`}
                onOk={(value: string) => this.onStateChange('countColor', value)}
                value={countColor}
              />
            </div>
          </div>                            
        </div>
      </Dialog>
    );
  };


  render() {
    const { color, textColor, countColor} = this.state;
    const { id = '' } = this.props?.rowData
    return (
      <div className={`${cls}-nodeKeyStyle`} key = {id+'nodeKeyStyle'}>
        <div className={`example`} >
          <div className={`text`}  style={{ background: color, color: textColor}}>{getLabel('132613', '示例')}</div>
          <div className={`number`} style={{ color: countColor }}>1</div>
        </div>
        <Icon weId={`${this.props.weId || ''}_67qg72`} className={`iconSet`} name='Icon-set-up-o' onClick={this.triggerShow}></Icon>
        {this.renderMainDialog()}
      </div>
    );
  }
}
export default NodeKeyStyle;
