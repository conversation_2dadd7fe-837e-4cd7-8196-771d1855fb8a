/**
 * <AUTHOR>
 * @createTime 2022-06-11
 */
import { toJS } from 'mobx';
import { ActionType, } from '../../../../common/types';
import { triggerActions } from '../../../../common/utils/action/useAction';
import { ActionException } from '../../../../common/types/action/types';
import { corsImport } from '@weapp/utils';

const btnHandleRegister = (key: string, routerProps?: any) => {
  switch (key) {
    case ActionType.OpenPage:
      return (config: any) => new Promise((resolve: any) => {
        corsImport('@weapp/ebdcoms').then(({JumpLink}) => {
          JumpLink(config.url, routerProps);
          resolve();
        })
      });
  }
};

export const onClickAction = (key: string, actions: any, routerProps?: any) => {
  triggerActions(toJS(actions), key, btnHandleRegister, routerProps)
    .catch((e) => {
      if (e !== ActionException.Break) {
        // eslint-disable-next-line no-console
        console && console.error(e);
      }
    });
};
