import { FormDatas, Icon, ITreeData, List, ListData, Tree, TreeRootIdArray, Dialog, AnyObj } from '@weapp/ui';
import { getLabel, uniq, cloneDeep } from '@weapp/utils';
import { computed, toJS } from 'mobx';
import { observer } from 'mobx-react';
import React, { PureComponent } from 'react';
import { When } from 'react-if';
import { formatConfig } from '../comp/mindMap-view/utils';
import utils, { addTreeNodeByBroId, deleteTreeNodeById, getAllKeys, getDefautDatasShow, treeToList } from '../../../utils';
import { transferToUITreeData } from '../engine/drag-tree/utils';
import { MindMapConfigData } from '../types';
import { changeNumToHan } from '../utils';
import NodeSlider from './node-slider';
import { MIND_VIEW } from '../../../constants/enum';
import './index.less';

const { message } = Dialog;
interface MindMapNodeTreeProps extends React.Attributes {
  value: any;
  config: MindMapConfigData;
  onChange: (value: any, action?: any) => void;
  onConfigChange: (value?: FormDatas) => void;
}
@observer
export default class MindMapNodeTree extends PureComponent<MindMapNodeTreeProps, any> {
  constructor(props: MindMapNodeTreeProps) {
    super(props);
    this.state = {
      visible: false,
      expandedKeys: [],
      nowNode: {},
      nowIndex: 0,
      key: utils.UUID(),
    };
  }

  @computed
  get getTreeData() {
    const {
      config: { nodeTreeInfo },
      value,
    } = this.props;
    return toJS(transferToUITreeData(toJS(value), nodeTreeInfo));
  }
  @computed
  get isHideRoot() {
    const { value = [], config } = this.props;
    const { mindLayout } = config;
    if (mindLayout === MIND_VIEW.FISHBONE || mindLayout === MIND_VIEW.MIND_MAP) {
      return false;
    }
    const rootInfo = value.find((i: any) => i.id === 'root_');
    return rootInfo.isHideRoot;
  }
  componentDidMount() {
    const { config = {}, onConfigChange } = this.props;
    // 检查配置 去除不合法节点并更新配置
    formatConfig(config, onConfigChange);
  }

  onRemove = (node: ITreeData) => () => {
    const {
      value = [],
      config: { nodeTreeInfo },
    } = this.props;
    const dmTables = toJS(value).filter((t: any) => t.id !== node.id);
    // 同步更新nodeTreeInfo
    if (nodeTreeInfo[node.id]) {
      delete nodeTreeInfo[node.id];
    }
    this.props.onConfigChange({ nodeTreeList: toJS(dmTables), nodeTreeInfo: toJS(nodeTreeInfo) });
  };

  onNodeOptClick = (node: any) => () => {
    const { value = [] } = this.props;
    const nowIndex = value.findIndex((t: any) => t.id === node.id);
    this.setState({
      visible: true,
      nowNode: node,
      nowIndex,
    });
    return false;
  };

  /**
   * 根据当前节点获取上级，上上级，上上上级，上上...级节点的id
   * */
  getSupNodeIds = (listArr: any[], target: any) => {
    const ids: string[] = [];
    const getSupNode = (list: any[], node: any) => {
      if (node.pid) {
        const father = list.find((p: any) => p.id === node.pid);
        getSupNode(list, father);
      }
      ids.push(node.id);
    };
    getSupNode(listArr, target);
    return ids;
  };

  onCreate = (node: ITreeData) => () => {
    const errText = this.hasStandardBusinessDataSource(node);
    if (errText) {
      return message({ type: 'info', content: `${errText}！` });
    }
    const { onConfigChange, config, value = [] } = this.props;
    const { nodeTreeInfo = {} } = config;
    const len = value.filter((n: any) => n.floor === node.floor + 1 && n.pid === node.id);
    const tempId = utils.UUID(16);
    // 这里要用1-1-1,1-1-2这种形式
    const lenLen = len ? len.length : 0;
    const suffix = node.floorStr ? `${node.floorStr}-${lenLen + 1}` : `${lenLen + 1}`;
    // const name = `${changeNumToHan(node.floor + 1)}${getLabel('129034', '级')}${getLabel('115351', '节点')}${len.length + 1}`;
    const name = `${changeNumToHan(node.floor + 1)}${getLabel('129034', '级')}${getLabel('115351', '节点')}${suffix}`;
    const _nodeTreeLists = [
      ...toJS(value),
      {
        id: tempId,
        floor: node.floor + 1,
        floorStr: suffix,
        name,
        pid: node.id,
        children: [],
        canDelete: true,
      },
    ];
    const _nodeTreeInfo = {
      ...nodeTreeInfo,
      [tempId]: {
        name,
      },
    };

    const ids = this.getSupNodeIds(value, node);
    this.setState((pre: any) => {
      const _expandedKeys = uniq([...pre.expandedKeys, ...ids]);
      return { expandedKeys: _expandedKeys };
    });
    onConfigChange({ nodeTreeList: toJS(_nodeTreeLists), nodeTreeInfo: toJS(_nodeTreeInfo) });
  };

  closeDialog = () => {
    this.setState({ visible: false });
  };

  changeNode = (node: any) => {
    const { value, onChange } = this.props;
    const nodeTreeList = toJS(value) || [];
    const { nowIndex } = this.state;
    // 历史代码 放开会有问题 暂时去掉 后续反馈无影响后remove
    // if (_node.children[0]) _node.children[0].actions = [];
    nodeTreeList[nowIndex] = node;
    onChange([...nodeTreeList], {
      action: 'update',
      args: [node, nowIndex],
    });
    this.setState({ nowNode: node, key: utils.UUID() });
  };

  onExpand = (expandedKeys: string[] | ListData[]) => {
    this.setState({ expandedKeys });
  };
  sortStartCustom = (evt: any) => {
    const { oldIndex, newIndex } = evt;
    const activeRows = treeToList(toJS(this.getTreeData), []);
    const exactDataRows = treeToList(this.filterCollpasedData(cloneDeep(toJS(this.getTreeData))), []); //过滤掉折叠数据的数组
    let oldData = activeRows.find((t: any) => t.id === exactDataRows[oldIndex].id); //移动节点
    const needCollpasedData = exactDataRows.filter((o: any) => o.pid === oldData.pid);
    //所有兄弟节点折叠
    const newExpendKeys = this.getDefaultExpandKeys().filter((key: string) => key !== oldData.id && !needCollpasedData.find((it: any) => it.id === key));
    this.setState({ expandedKeys: newExpendKeys, sortStartData: oldData });
  };
  filterCollpasedData = (data: any) => {
    const expandedKeys = this.getDefaultExpandKeys();
    data = data.map((o: any) => {
      if (!expandedKeys.includes(o.id)) {
        o.children = [];
      }
      if (o?.children && o?.children?.length) {
        o.children = this.filterCollpasedData(o.children);
      }
      return o;
    });
    return data;
  };
  sortEndCustom = (evt: any) => {
    const { onConfigChange } = this.props;
    const { newIndex, oldIndex } = evt;
    const activeRows = treeToList(toJS(this.getTreeData), []);
    const exactDataRows = treeToList(this.filterCollpasedData(cloneDeep(toJS(this.getTreeData))), []); //过滤掉折叠数据的数组
    if (newIndex === oldIndex) return;
    // evt.to;    // target list
    // evt.from;  // previous list
    // evt.oldIndex;  // element's old index within old parent
    // evt.newIndex;  // element's new index within new parent
    // evt.clone // the clone element
    // evt.pullMode;
    const oldData = this.state.sortStartData; //移动节点
    // let newData = exactDataRows[newIndex];//拖拽后相邻节点
    // const oldData = exactDataRows[oldIndex];//移动节点
    // const newData = exactDataRows[newIndex];//拖拽后相邻节点
    //基于exactData找到对应的数据，再转换回去，不然折叠后数据无法计算
    console.log(activeRows, oldIndex, newIndex, evt, exactDataRows, 'exactDataRows');
    // let oldData = activeRows.find((t: any) => t.id === exactDataRows[oldIndex].id);//移动节点
    let newData = activeRows.find((t: any) => t.id === exactDataRows[newIndex].id); //拖拽后相邻节点
    let brotherInfo: any = undefined;
    if (oldIndex < newIndex && oldData.floor !== newData.floor) {
      //如果newdata为子级，查找他的父级节点是否和olddata是兄弟节点
      brotherInfo = activeRows.find((p: any) => p.id === newData.pid);
      if (brotherInfo.pid === oldData.pid) {
        newData = brotherInfo;
      }
    }
    if (oldData.floor !== newData.floor || oldData.pid !== newData.pid) {
      message({ type: 'info', content: `${getLabel('248709', '目前只允许同级节点拖拽')}` });
      this.setState({ key: Math.random() });
      return;
    }

    let newTree = deleteTreeNodeById(this.getTreeData, oldData.id); //删除原始元素及子级节点

    if (newIndex === 0 || newIndex === 0) {
      //放到第一位置，暂时业务不允许
      return;
    } else {
      if (oldIndex > newIndex) {
        //从下往上拖拽，newData为拖拽节点下方的节点，新节点push到newData上方
        newTree = addTreeNodeByBroId(newTree, newData.id, oldData, 'top');
      } else {
        //从上往下拖拽，newData为拖拽节点上方的节点，新节点push到newData下方
        newTree = addTreeNodeByBroId(newTree, newData.id, oldData, 'bottom');
      }
    }
    onConfigChange({ nodeTreeList: treeToList(newTree, []) });
    this.setState({ key: Math.random() });
  };
  getDefaultExpandKeys = () => {
    //当未设置expandkeys时默认返回4层展示
    let { expandedKeys } = this.state;
    if (!expandedKeys.length) {
      const expandLevelData = getDefautDatasShow(toJS(this.getTreeData), 4);
      expandedKeys = getAllKeys(expandLevelData || []);
    }
    return expandedKeys;
  };
  // 当前节点是否有标准业务数据源
  // * 新增子级节点有标准业务数据源判断
  hasStandardBusinessDataSource = (node: any) => {
    const {
      config: { nodeTreeInfo = {}, nodeTreeList = [] },
    } = this.props;
    for (let i in nodeTreeInfo) {
      const item = nodeTreeInfo[i];
      // 当前节点是否为标准业务数据源
      if (item.dataset?.type === 'BIZ' && i === node.id) {
        return getLabel('246759', '当前节点数据源为标准业务数据源，子节点无法与其建立上下级关系');
      }
      const childrenItems = nodeTreeList.filter((k) => k.pid === node.id);
      // 子节点是否有标准业务数据源
      if (childrenItems.some((k) => k.dataset?.type === 'BIZ')) {
        // mark 标准业务数据源和别的数据源一起显示时，无法排序，故先限制
        return getLabel('261998', '当前节点子节点数据源有标准业务数据源，暂无法新建子节点');
      }
    }
    return '';
  };
  customRenderNode = (node: ITreeData) => {
    return (
      <div className='mindmap-tree-warp-node'>
        <div className='node-left' onClick={this.onNodeOptClick(node)}>
          {node.id !== 'root_' && <Icon weId={`${this.props.weId || ''}_b2tqi6`} name='Icon-move' className={`mindmap-tree-warp-sort-handle`} />}
          <span className='node-left-text'>{node.content}</span>
          {node.id === 'root_' && this.isHideRoot ? <span>（{getLabel('217407', '已隐藏')}）</span> : ''}
        </div>
        <div className='node-right'>
          <span className='treenode-operate-add' onClick={this.onCreate(node)}>
            <Icon weId={`${this.props.weId || ''}_oc9ok8`} name='Icon-add-to01' />
          </span>
          <When weId={`${this.props.weId || ''}_rp5ujs`} condition={node.pid && node.canDelete}>
            <span className='treenode-operate-del' onClick={this.onRemove(node)}>
              <Icon weId={`${this.props.weId || ''}_4nti0z`} name='Icon-delete' />
            </span>
          </When>
        </div>
      </div>
    );
  };
  render() {
    let { visible, expandedKeys, nowNode } = this.state;
    let props: any = this.props;
    expandedKeys = this.getDefaultExpandKeys();
    if (!props?.layoutInfo) {
      props = {
        ...props,
        layoutInfo: {
          comLayoutScope: '',
          layoutType: 'FLOW',
          datasetVals: [],
        },
      };
    }
    return (
      <>
        <List
          weId={`${this.props.weId || ''}_t66e8b`}
          data={this.getTreeData}
          className='mindmap-tree-warp'
          customRenderContent={this.customRenderNode}
          sortable={true}
          sortableOptions={{
            handle: '.mindmap-tree-warp-sort-handle',
            onEnd: this.sortEndCustom,
            onStart: this.sortStartCustom,
            delay: 50,
          }}
          key={this.state.key}
          onExpand={this.onExpand}
          expandRowKeys={expandedKeys}
        />
        <NodeSlider
          weId={`${this.props.weId || ''}_dhr5wg`}
          // fatherProps={this.props}
          fatherProps={props}
          data={nowNode}
          visible={visible}
          onClose={this.closeDialog}
          onChange={this.changeNode}
        />
      </>
    );
  }
}
