import { middleware, noop } from '@weapp/utils';
// import noop from 'lodash-es/noop';
import React, {
  ComponentType, forwardRef, ForwardRefRenderFunction, Suspense,
} from 'react';
import { appName as EbdfAppName } from '../../../constants';
export type LoaderType = () => Promise<{
  default: React.ComponentType<any>;
}>;

export interface LoadableComponentProps {
  appName?: string;
  name: string;
  loader: LoaderType;
  loading?: ComponentType<any>;
  // render?: FunctionComponent;
}

function Loadable(opts: LoadableComponentProps): ComponentType<any> {
  const {
    appName = EbdfAppName, name = 'Test', loader, loading,
  } = opts;

  const Com = React.lazy(loader);

  const LoadableComponent: ForwardRefRenderFunction<unknown, any> = (props, ref) => (
    <Suspense weId={`${props.weId || ''}_m78hln`} fallback={loading || noop}>
      <Com weId={`${props.weId || ''}_llclyz`} {...props} ref={ref} />
    </Suspense>
  );

  LoadableComponent.displayName = 'LoadableComponent';

  return middleware(appName, name)(forwardRef(LoadableComponent));
}

export default Loadable;
