import { BaseProps } from '@weapp/ui';
import { ComponentType } from 'react';
import { AnyObj } from '../../../types/common';
import { MindMapContentProps } from '../../mindMap/comp/mindMap-view/types';
import { MindFuncProps } from '../../mindMap/config/comps/func-setting';

export type MindFormatType = 'node_array' | 'node_tree' | 'freemind';

export interface MindMapProps extends BaseProps {
  datas: MindData;
  getInstance?: (jm: any) => void;
  nodeRenderCustom?: (node: any) => void;
  draggable?: boolean;
  callback?: () => void;
  isHideRoot?: boolean; // 是否隐藏根节点
  funcSetting?: MindFuncProps;
  onRef?: (ref: any) => void;
  defaultExpendDepth?: number; // 默认展开的层级
  isFirstMount?: boolean // 是否是第一次渲染
  showFieldKeys?: string[]
  refreshView?: (cb?: Function) => void
  parentProps: MindMapContentProps
}
export interface MindData {
  /* 元数据，定义思维导图的名称、作者、版本等信息 */
  meta: {
    name: string;
    author?: string;
    version?: string;
  };
  /* 数据格式声明 */
  format: MindFormatType;
  data: Array<object> | AnyObj;
  supportHtml?: boolean;
}
// v2版本思维导图数据
export interface MindDataV2Data {
  id?: string
  // 节点文本
  text?: string;
  // 图片
  image?: string;
  imageTitle?: string;
  imageSize?: {
    width: number;
    height: number;
  };
  // 图标
  icon?: string[];
  // 标签
  tag?: string[];
  // 链接
  hyperlink?: string;
  hyperlinkTitle?: string;
  // 备注内容
  note?: string;
  // 概要
  generalization?: {
    text: string;
  };
  // 节点是否展开
  expand?: boolean;
  [x: string]: any
}
export interface MindDataV2 {
  data?: MindDataV2Data;
  children?: MindDataV2Data[]; // 子节点
}
export interface MindOptions {
  editable?: boolean; // 是否启用编辑
  theme?: string; // 主题
  mode?: string; // 显示模式 'full' |'side' ,
  supportHtml?: boolean; // 是否支持节点里的HTML元素
  view?: {
    engine: string; // 思维导图各节点之间线条的绘制引擎
    hmargin: number; // 思维导图距容器外框的最小水平距离
    vmargin: number; // 思维导图距容器外框的最小垂直距离
    // line_width: number; // 思维导图线条的粗细
    // line_color: string; // 思维导图线条的颜色
    hoverNodeId?: string;
  };
  layout?: {
    hspace: number; // 节点之间的水平间距
    vspace: number; // 节点之间的垂直间距
    pspace: number; // 节点与连接线之间的水平间距（用于容纳节点收缩/展开控制器）
  };
  shortcut?: {
    enable: boolean; // 是否启用快捷键
    handles: object; // 命名的快捷键事件处理器
    mapping: {
      // 快捷键映射
      addchild: number; // <Insert>
      addbrother: number; // <Enter>
      editnode: number; // <F2>
      delnode: number; // <Delete>
      toggle: number; // <Space>
      left: number; // <Left>
      up: number; // <Up>
      right: number; // <Right>
      down: number; // <Down>
    };
  };
  nodeRenderCustom?: Function
}

export enum Direction {
  Left = 'left',
  Right = 'right',
  Down = 'down',
  Up = 'up',
}

export type MindMapType = ComponentType<MindMapProps>;
