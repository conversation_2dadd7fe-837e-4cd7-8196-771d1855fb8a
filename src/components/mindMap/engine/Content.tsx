import { inject, observer } from 'mobx-react';
import React, { PureComponent } from 'react';
import { ebdfClsPrefix } from '../../../constants';
// import { ebdfClsPrefix } from '../../../../constants';
import TreeBox from './drag-tree';
import './index.less';
import MindMapNodeConfig from './node-config';
@inject('store')
@observer
class MindMapEngineContent extends PureComponent<any> {
  componentDidMount() {
    const { store, data } = this.props;
    console.log('%c [engine data ]-13', 'font-size:13px; background:pink; color:#bf2c9f;', data)
    store.init(data);
  }

  render() {
    const {
      treeData,
      onTableRemove,
      onNodeOptClick,
      onNodeCreate,
      selector,
      visible,
      closeNodeConfig,
      doSave,
      afterFormInit,
      fieldChange,
      setActions,
      actions,
      appId,
      dataset,
      isSave,
    } = this.props.store;
    return (
      <div className={`${ebdfClsPrefix}-view-mindmap-content`}>
        <TreeBox
          weId={`${this.props.weId || ''}_1yjsjn`}
          data={treeData}
          onCreate={onNodeCreate}
          onRemove={onTableRemove}
          onNodeOptClick={onNodeOptClick}
          selector={selector}
        />
        <MindMapNodeConfig
          weId={`${this.props.weId || ''}_hp0f6w`}
          visible={visible}
          onCancel={closeNodeConfig}
          selector={selector}
          doSave={doSave}
          afterFormInit={afterFormInit}
          fieldChange={fieldChange}
          actions={actions}
          setActions={setActions}
          appId={appId}
          dataset={dataset}
          isSave={isSave}
          isEngine
        />
      </div>
    );
  }
}

export default MindMapEngineContent;
