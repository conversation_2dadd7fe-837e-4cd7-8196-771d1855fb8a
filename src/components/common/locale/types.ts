import { SingleFieldLocaleProps, AnyObj } from '@weapp/ui';

export type LocaleExDataType = {
  nameAlias?: string;
  nameAliasLabelId?: string | -1;
};

export type LocaleExValueDataType =
  | string
  | (LocaleExDataType & {
  [key: string]: any;
});

export interface LocaleExProps extends Omit<SingleFieldLocaleProps, 'value' | 'onChange'>,
  React.Attributes {
  value?: LocaleExValueDataType;
  /** 作为请求参数targetId的值 */
  targetId?: string;
  /** 模块名，同时也是网关的路由标识，Locale组件请求需要的module参数 */
  module?: string;
  /** 兼容老数据，改造多语言前文本内容存储的key */
  nameAliasKey?: string;
  onChange?: (data: LocaleExValueDataType) => void;
  onSave?: (data?: LocaleExValueDataType) => void;
  params?: AnyObj;
  ebBusinessId: string;
}
