# Getting Started

[Document](https://e-cloudstore.com/doc.html?appId=55f7cd63141a4a029e9259e5abaf737f)

### 文件目录说明

1.components: EB 组件
2.components-share: 公共组件
3.constants: 公共常量
4.mock:mock 数据
5.pages: Demo 相关代码
6.routes: 项目路由
7.style: 公共样式（单个组件样式代码放在对应的 EB 组件内）
8.types：公共声明（单个组件声明代码放在对应的 EB 组件内）
9.utils：公共方法
10.ebcoms.ts: 导出 EB 组件
11.ebComStyleConfigs.ts: 导出样式

### 热调试说明

-   [1] 本项目主要依赖（ebdcoms|ebdpage|ebddesigner|designer|designer-demo|ebdapp|ui|utils)，具体参见 setupProxy 文件（也可改用 nginx 等其他方式处理）

setupProxy 配置

```
const { createProxyMiddleware } = require('http-proxy-middleware');

module.exports = function (app) {
  app.use(
    '/+(api|papi)/',
    createProxyMiddleware({
      target: 'https://weapp.mulinquan.cn/',
      changeOrigin: true,
      secure: false,
      headers: {
        referer: 'https://weapp.mulinquan.cn/',
      },
      logLevel: 'debug'
    })
  );
  /**
   * 默认请求和模块库走测试环境
   */
  app.use(
    '/build/+(ebdcoms|ebdpage|ebddesigner|designer|designer-demo|ebdapp|ui|utils)/',
    createProxyMiddleware({
      target: 'https://weapp.mulinquan.cn/',
      changeOrigin: true,
      secure: false,
      headers: {
        referer: 'https://weapp.mulinquan.cn/',
      },
      logLevel: 'debug'
    })
  );
};
```
