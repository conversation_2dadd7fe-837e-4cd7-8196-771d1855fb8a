import React, { PureComponent } from 'react';
import MindMapViewCom from '../comp/mindMap-view';
import { ViewProps } from '../types';


export default class MindMapView extends PureComponent<ViewProps> {
  render() {
    const {
      isDesign, id = '', page = {}, config, comServicePath, pageId, onRef, isDoc, store, events, pluginCenter, coms
    } = this.props;
    return (
      <MindMapViewCom
        weId={`${this.props.weId || ''}_q144p0`}
        onRef={onRef}
        comServicePath={comServicePath}
        compId={id}
        pageId={page?.id || pageId}
        page={page}
        isDesign={isDesign}
        config={config}
        isMobile={page.client === 'MOBILE'}
        isDoc={isDoc}
        ebStore={store}
        events={events}
        pluginCenter={pluginCenter}
        coms={coms}
      />
    );
  }
}
