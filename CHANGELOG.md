## [0.1.23](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/compare/v0.1.22...v0.1.23) (2025-06-06)


### Performance Improvements

* no.3699225 优化思维导图移动端支持触摸移动事件 ([8394b9f](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/8394b9f3e8f4bd94aa02c33fd82cd87e8ad1fc73))
* no.3699225 优化思维导图自适应节点为进度高度的功能 ([ab1f526](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/ab1f526ca18144eae4ecce43d5dd59ca3a967031))
* no.3699225 优化思维导图自适应节点显示问题 ([9a3b13f](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/9a3b13f3164c590b28160aea1fa6437b041d6ff6))
* no.3699225 优化思维导图自适应节点自定义文本高度问题 ([7e6e1ba](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/7e6e1ba50f3fe4af852dacda9637992879927fb6))
* no.3699225 优化思维导图默认根节点配置的功能 ([cfb779f](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/cfb779f112b02df437170c7dcbe89c132fa3198c))



## [0.1.22](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/compare/v0.1.21...v0.1.22) (2025-05-07)


### Performance Improvements

* no.3629524 优化思维导图最大节点数计算兜底 ([7f818ed](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/7f818edd02932e45da8398cfc5eb85897382a757))
* no.3629524 优化思维导图底层插件支持customDisableDarg禁用拖拽 ([a3e02a7](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/a3e02a7e56fa0fd96c47a58b07c8f18d8e20e929))
* no.3629524 优化思维导图支持传入customNodeClick自定义点击事件 ([6b431eb](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/6b431ebdae99773f24b918fe316f0c7b50cd59d7))
* no.3629524 优化思维导图组织结构图隐藏根节点显示不居中问题 ([3ca40b3](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/3ca40b38b1bcacc4e3db6128abf580837a4f28d4))
* no.3629524 优化思维导图组织结构图隐藏根节点显示不居中问题 ([5469a30](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/5469a307eb6c93c016c59cfa87136861c1a5e659))
* no.3629524 优化思维导图组织结构图隐藏根节点显示不居中问题 ([d9bb0ca](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/d9bb0cafd8cba6c06cba051db9bdddb7afff0a6b))
* no.3629524 优化思维导图组织结构图隐藏根节点显示不居中问题 ([453f481](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/453f4816b198920822d90c957330ee0eb20a6d4b))



## [0.1.21](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/compare/v0.1.20...v0.1.21) (2025-04-03)


### Bug Fixes

* no.2025002677 解决节点未配置显示字段不显示未配置字段提示 ([659ac04](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/659ac04196d6eef7503be642a1b61f7185ced724))
* no.2025002677 解决节点未配置显示字段不显示未配置字段提示 ([9474f4e](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/9474f4ee7ffca6d22ade0783ce4ffed21d0780bc))
* no.2025002677 解决节点未配置显示字段不显示未配置字段提示 ([8c903b8](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/8c903b819617fa4ad103d7f66dcf42216c719c93))


### Features

* no.2025002677 新增【27274】EB思维导图支持左右双向布局 ([c75846b](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/c75846b651a6e30116bde001a62ec347b6f59a8b))
* no.2025002677 新增人员显示字段自适应显示 ([2a84870](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/2a8487016ba914c202bb82abf5c3d5e46669f6a6))


### Performance Improvements

* no.3576038 优化思维导图双向布局紧凑布局 ([9b85ad0](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/9b85ad0230c807c29994c84530b32d294fe6546b))
* no.3576038 优化思维导图节点，显示字段自适应显示问题 ([ada6017](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/ada601728fefb12859a703fc7db7ee0c34c24d0b))
* no.3607402 优化EB思维导图数仓数据源下上下级节点字段支持扩展的功能 ([6c8e425](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/6c8e425882be728d9ced5953277a1b283d1cbdbe))
* no.3610560 优化EB思维导图节点字体放大后自适应的功能 ([7b05c6f](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/7b05c6ffa03e34d1d2d80c45d2c3f52b73df9597))
* no.3610560 优化EB思维导图节点自适应下textarea最大宽度的功能 ([58357a0](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/58357a0991e33e84cebb1f674db6cb27a37caf4e))



## [0.1.20](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/compare/v0.1.19...v0.1.20) (2025-03-07)


### Bug Fixes

* no.3527905 解决鱼骨图根节点样式修改后不显示 ([83176f8](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/83176f8a97c1b727dd5919dc08e97b15ab5db9e8))
* no.3529611 解决思维导图节点样式不显示的问题 ([79728cf](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/79728cf4f29789a75b938c61d0e41f6ef8a8217f))
* no.3529777 解决思维导图其他布局设置隐藏根节点报错问题 ([ab8d801](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/ab8d80182a61bd3f1784150b7f0f0d323c5ebf26))


### Features

* no.2025002676 新增【27272】EB思维导图支持鱼骨图布局 ([e932849](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/e9328491ed92aaea4bb423cdf794cf6cfc69ef6b))
* no.2025002676 新增【27272】EB思维导图支持鱼骨图布局 ([4621a93](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/4621a9382fc211d467bb515c6dbf8b03fd779538))
* no.3455231 新增思维导图-各组件统一eb表单数据源【25307】 ([ecf2654](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/ecf265479e9e150bd143aeb01a37d0bfcda39533))
* no.3455231 新增思维导图-各组件统一eb表单数据源【25307】 ([0e0482a](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/0e0482a0f46e62c80abea045fd17dc6934baf561))
* no.3455231 新增表单数据源改造调试放开 ([b3ec147](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/b3ec147e3fa8c20d4f037bfc9263bbd32688f54d))


### Performance Improvements

* no.3435985 优化导图默认节点样式的功能 ([7c5ff63](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/7c5ff630cbdfe9e337f4925d24fe0499a75a5b07))
* no.3435985 优化导图默认节点样式的功能 ([53a6401](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/53a6401b677114db089b2c9e92957b649e22fbbf))
* no.3527905 优化逻辑结构图居中显示 ([ec57bcd](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/ec57bcdcf67bcae31f83ab7d81b9d2560483324f))
* no.3527905 优化逻辑结构图居中显示 ([d0e413b](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/d0e413b9619325ac789371c6d268abbde0a8ca1a))
* no.3527905 优化逻辑结构图居中显示 ([f4aa55f](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/f4aa55f14c1e50a4562cf0dc1e2da2846dd35771))
* no.3527905 优化逻辑结构图居中显示 ([2d88cdd](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/2d88cdda055fa61c3cb547919761ca0fdfc39e6a))
* no.3527905 优化逻辑结构图居中显示 ([2504448](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/2504448dfd60f902e4a7d5d68c813ef494c577e6))
* no.3527905 优化鱼骨图画布节点内容居中显示计算 ([de6a3a6](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/de6a3a6068697317bcc221deb14859b71254e1cc))
* no.3527905 优化鱼骨图画布节点内容居中显示计算 ([6bed45b](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/6bed45b36a97f3c001f86917a7b9bcd136cd7a95))
* no.3527905 优化鱼骨图画布节点内容居中显示计算 ([5882207](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/58822074f542d891145687f4773129e1bc5ead2c))
* no.3527905 优化鱼骨图画布节点内容居中显示计算 ([cb1cbbb](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/cb1cbbbde095906d9f07a11dd970be7afba2eaaf))
* no.3527905 优化鱼骨图节点过多卡顿问题 ([b6751b4](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/b6751b489d9bb9e7379a43c9e2ff30f4eb2659ca))



## [0.1.19](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/compare/v0.1.18...v0.1.19) (2025-02-07)


### Performance Improvements

* no.3455231 优化放开各组件统一eb表单数据源 ([80c3cba](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/80c3cba98885f1627466adf3b4a62a71bf6c4827))



## [0.1.18](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/compare/v0.1.17...v0.1.18) (2025-01-03)


### Bug Fixes

* no.2024044260 解决非曲线时根节点支持配置连线色 ([2cb0ff4](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/2cb0ff49c9dd720c88f8d3f12b85af1c160784ec))
* no.2024044260 解决非曲线时根节点支持配置连线色 ([45ec424](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/45ec424b40118ecd0408d5d357daf1ea43d90457))
* no.3435985 解决思维导图存在多个时滚动条无法拉到底打包问题 ([d43e75e](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/d43e75e275ad254d55e3721762d6f0896de9cbe8))
* no.3435985 解决思维导图存在多个时滚动条无法拉到底的问题 ([88a3ad9](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/88a3ad9e5ee9e5f39c69140145022b9b87c4f7d6))
* no.3435985 解决思维导图自定义文本显示为obj的问题 ([a71504e](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/a71504eaf6292bd56d87c4aefd03792c81f81dce))
* no.3435985 解决思维导图自定义文本显示为obj的问题 ([f14a72e](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/f14a72e313f1e77f90f36eaed38ff90b0d49df27))
* no.3435985 解决思维导图自定义文本自适应问题 ([265bee2](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/265bee2a26449bd94d316471f42983c7deb82375))
* no.3435985 解决思维导图自定义文本自适应问题 ([29020ed](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/29020ed7e12c9b766e1fe104d44e378587764a13))
* no.3435985 解决思维导图自定义文本配合节点尺寸自适应问题 ([c9b5740](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/c9b57405a7805d1925a795b270ef3d636e43cbe5))
* no.3435985 解决思维导图自定义文本配合节点尺寸自适应问题 ([8c37e5c](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/8c37e5cd48e94f52556edc9aba5a48f01cf61e95))
* no.3435985 解决思维导图自定义文本配合节点尺寸自适应问题 ([b2198d4](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/b2198d4f534581ccba49304450c6544cb39d15f6))
* no.3435985 解决思维导图自适应相关的问题 ([cbf60c9](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/cbf60c9cc1f156389aab2b37b58c2ab70cde7e46))
* no.3435985 解决思维导图表单设计器下默认高度问题 ([97a855b](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/97a855b345147d0925b68b1ad1a7ffdc61c2da0a))
* no.3435985 解决思维导图表单设计器下默认高度问题 ([da6c611](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/da6c611cee52dc304572731210698bd9d187ca5b))
* no.3455231 解决导图数据源表单改造 ([5c4e1c1](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/5c4e1c11364087b046a5eac4d909093ae6a6db1b))


### Features

* no.3455231 新增思维导图-各组件统一eb表单数据源【25307 ([e146865](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/e1468650f4c3edd5354d147e3b882f4263a10825))
* no.3455231 新增思维导图-各组件统一eb表单数据源【25307】 ([bcaa89b](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/bcaa89b31228a734c690799e570bd59442baa32f))
* no.3455231 新增思维导图-各组件统一eb表单数据源【25307】 ([8cc7c3b](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/8cc7c3b40d4b6f48e85a3f207c3b63ec8df76357))


### Performance Improvements

* no.3454643 优化思维导图高级视图界面默认撑开问题 ([3b93677](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/3b93677da378a8ba04db300db28f4a359c0557e5))
* no.3454643 优化思维导图高级视图界面默认撑开问题 ([6cabe1e](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/6cabe1e8c89a12022be22fb5b26b1445cfb46332))
* no.3454643 优化思维导图高级视图界面默认撑开问题 ([de77db2](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/de77db22e80d95cd2a9e903fc29b7f39f74b231a))
* no.3454643 优化思维导图高级视图界面默认撑开问题 ([226906f](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/226906f17b9612d35f8a1af3702a128048c5cc61))
* no.3454643 优化思维导图高级视图界面默认撑开问题 ([a3308b1](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/a3308b1ddbbdb3abac0004273173ac3684917b20))
* no.3454643 优化思维导图高级视图界面默认撑开问题 ([036412f](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/036412fb2a42ffa790e8b1edb6cacebbe100b37b))



## [0.1.17](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/compare/v0.1.16...v0.1.17) (2024-12-06)


### Bug Fixes

* no.2024037837 解决导图自定义大小二开报错问题 ([1127bdf](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/1127bdf8860abda14a5d295215bd2d5e550dcec5))
* no.2024044260 解决导图弹窗关闭效果 ([be5db43](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/be5db432f9891b231a5c5adc5f481d64a94837f1))
* no.2024044260 解决导图节点尺寸大小配置不生效问题 ([29bab81](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/29bab81333223f282088b92f43c57ca6fd3115c3))
* no.2024044260 解决导图连线样式配置不生效问题 ([976f1b9](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/976f1b90812cddaf5ded098edd8aa298a3953b0c))
* no.2024044260 解决导图连线配置问题 ([b6708b2](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/b6708b2c484cf249e7fd63cc39f222a58f29cf22))
* no.2024044260 解决导图连线配置问题 ([f9a58c6](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/f9a58c611f8cf56a66693dfe65837d034ba4be3f))
* no.2024044260 解决导图默认展开层级不生效 ([37211e9](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/37211e9223b554044f6bac59c1f418982ad0c4fd))
* no.3173078 解决导图ie下问题 ([1e1bd83](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/1e1bd8382a3ff47fba1f9b9cd6bb4274d682ea71))
* no.3257659 解决数据源权限改变事配置被清空的问题 ([deaccf3](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/deaccf38769ec671adfc46e467a6ac3fa519e758))
* no.3356092 解决思维导图节点设置刷新组件数据变化卡片不显示字段问题 ([31bc2f2](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/31bc2f2be9a3ac2140decbc80beae7306bdd8b3f))


### Features

* no.2024034142 新增EB思维导图样式优化 ([392b3c2](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/392b3c2ac8b3be1837e885127f8c7b6f0c70f7de))
* no.2024034142 新增EB思维导图样式优化 ([289e954](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/289e9541081bdf27351f08330891530359526a99))
* no.2024034142 新增EB思维导图样式优化 ([eb47dcb](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/eb47dcb109352d2d65cbc119caaea73ec28e13d3))
* no.2024037837 新增通用配置、单个节点样式配置新增重置按钮 ([7ae233d](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/7ae233d2d19bcc4e2154d76560df71de78defac2))
* no.2024044260 新增EB思维导图支持曲线直线配置以及左右布局 ([a2845d2](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/a2845d2729b6cbf292c929bd5d2dcfcaf2bf6ba4))
* no.2024044260 新增EB思维导图支持曲线直线配置以及左右布局 ([74b7a65](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/74b7a652417861359e00d82eea3e21fa44f840ce))
* no.2024044260 新增EB思维导图支持曲线直线配置以及左右布局 ([30f6877](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/30f68772716d842abd343dc2a4be8d5f65afb056))
* no.2024044260 新增组织结构图曲线配置 ([40f16e3](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/40f16e3eb70a2ee040b5235fc96d0628237747a7))
* no.3257659 新增表单数据源配置放开权限配置 ([8f023f0](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/8f023f0dd11b35de1fa618c591a00e9f4aaf6a42))


### Performance Improvements

*  no.3240930 优化仓库eslint校验规则(思维导图) ([7e95dce](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/7e95dce2946692a99005f0a377d4136132734a26))
* no.2024034142 优化MacBook触控板滚动效果 ([b9219d7](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/b9219d7bc1410b774d68aee6d4fdbedcdb30a39e))
* no.3225154 优化前端仓库开启资源多版本的功能 ([f065d27](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/f065d27bf37fb44f92e47ae2ed3c5b29da59a16d))
* no.3237987 优化EB组件仓库使用lodash引用调整到底层工具库上的功能 ([5c153d8](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/5c153d8b886c5d09a78cf7d6ac4d4056b730ee1b))
* no.3356092 优化根节点默认高度 ([5b1c407](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/5b1c407d9b574dde01109dc2722d989aec4d31af))
* no.3356092 优化获取指定节点方法 ([9f59526](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/9f595268640e393944037d7ffab18543f7d8149d))



## [0.1.16](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/compare/v0.1.15...v0.1.16) (2024-11-08)


### Bug Fixes

* no.2024037837 解决导图出现多滚动条问题 ([99e7498](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/99e7498e6a8fe16e17fcec9e281fb2f27d585bcc))
* no.2024037837 解决导图出现多滚动条问题 ([9db4f2a](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/9db4f2a5a7495562a999f03ccdca3b386347697b))
* no.2024037837 解决导图垂直方向不居中问题 ([2b0879a](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/2b0879ab529595160dbb3b23e54b114553b777b8))
* no.2024037837 解决导图基线相关问题 ([50b8ab0](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/50b8ab0d5a4b2dd6a7c7d070b85146a93b0c4cdc))
* no.2024037837 解决导图基线相关问题 ([2db4f25](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/2db4f25df27d69ba286ee264c9966cbf260659a2))
* no.2024037837 解决导图基线相关问题 ([f8b406a](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/f8b406a2e18e9e5fcb214c3eef375245af644369))
* no.2024037837 解决导图基线相关问题 ([5a97f4b](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/5a97f4baaa78dc80bb75521f892b2f79369c5ae3))
* no.2024037837 解决导图自适应节点大小偶先失效的问题 ([981af59](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/981af5909c91a6405fb55e40d415dbc88072b0c7))
* no.2024037837 解决特定场景下折叠展开连线渲染问题 ([9faf37c](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/9faf37cd14e5a5c0edd68cea2006513b760c5723))


### Features

* no.2024037837 新增刷新组件相关二开口子 ([568ceb4](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/568ceb4cb338b7e6532bf526e735b2847703afe5))
* no.2024037837 新增画布能展示完成不出现滚动条，超出画布范围出现滚动条 ([4465e46](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/4465e461d729059de1b6d7ca3f4bf07f07e77b6a))
* no.2024037837 新增画布能展示完成不出现滚动条，超出画布范围出现滚动条 ([720e21a](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/720e21af110d1cd6c160003fefb73512f5d14b6c))


### Performance Improvements

* no.2024037837 优化eb思维导图居中定位相关 ([13ba427](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/13ba427703af3100d03e0730df489c4b5ed5f5ed))
* no.2024037837 优化eb思维导图居中定位相关 ([ea87b40](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/ea87b40cde256e8ee4780cb81727bc982e985607))
* no.2024037837 优化eb思维导图居中定位相关 ([0ea3171](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/0ea3171104ec4fcb78124d7b621c6af08f064162))
* no.2024037837 优化导图大数据量下卡顿问题 ([b856de6](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/b856de6eb49fc0649a1d25240fd145f8865c6a3a))
* no.2024037837 优化导图大数据量下卡顿问题 ([6712fae](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/6712fae28de5142abb262606420a5e9f1b116b31))
* no.2024037837 优化导图大数据量下卡顿问题 ([68311d4](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/68311d41599b0b2c72381d411785e9e1d970eca9))
* no.3237987 优化EB组件仓库使用lodash引用调整到底层工具库上的功能 ([9ae80b4](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/9ae80b4d9f45aa92d0325e9f9f1f478d5e77224b))



## [0.1.15](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/compare/v0.1.13-hotfix2...v0.1.15) (2024-10-11)


### Bug Fixes

* no.3233043 解决思维导图高级视图选择字段-名称，无法跳转问题 ([3be47f9](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/3be47f9ed6f3466e9cb888608a8c98854938530c))


### Performance Improvements

* no.2024037837 优化eb思维导图节点数过多时卡顿问题 ([0cefb21](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/0cefb2102b306b7dca177ab612ae85547fdc136d))
* no.2024037837 优化eb思维导图节点数过多时卡顿问题 ([b8baf47](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/b8baf47c5bb569cb802e98c968e1b669b6d4d733))
* no.2024037837 优化eb思维导图节点数过多时卡顿问题 ([64a8517](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/64a85179d71ecfdcf4a06051c59e259c24f8b9d6))
* no.2024037837 优化eb思维导图节点数过多时卡顿问题 ([fa489ea](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/fa489eade8694a6a3603245afd8dbcace2c43201))
* no.2024037837 优化eb思维导图节点数过多时卡顿问题 ([d23443d](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/d23443d70fc3a7277d9b445a2dc6222fa7ee39e4))
* no.2024037837 优化eb思维导图节点数过多时卡顿问题 ([01aa177](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/01aa177a5c125898650d490bee8bd4b9deed4e52))
* no.2024037837 优化eb思维导图节点数过多时卡顿问题 ([b50dedf](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/b50dedf8437b4c98be17373b87e38a563237c515))
* no.2024037837 优化导图版本号 ([4702e6f](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/4702e6f2c4e44a1f9dbac5636ad6a7b4bd055cfd))
* no.3257659 优化自定义渲染导图二开口子样式值获取方式 ([2045312](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/2045312816dd14605d5a7981e7f9907218ef8ee8))



## [0.1.13-hotfix2](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/compare/v0.1.13-hotfix1...v0.1.13-hotfix2) (2024-10-08)


### Bug Fixes

* no.3257659 解决数据源权限改变事配置被清空的问题 ([c973310](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/c973310c6938dadceb371d46d3ac63044a1fabf8))


### Features

* no.3257659 新增刷新组件支持多选参数 ([e8bbfb8](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/e8bbfb8a42bd0d06711e9fe6d081a5767de48984))
* no.3257659 新增表单数据源配置放开权限配置 ([86d43f2](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/86d43f229830b84fcbe9b5d44a639e782274397c))



## [0.1.13-hotfix1](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/compare/v0.1.13...v0.1.13-hotfix1) (2024-09-20)


### Performance Improvements

* no.3257659 优化自定义渲染导图二开口子样式值获取方式 ([d0b0a6b](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/d0b0a6b31db4ef1ae1a60483660a0d6d77b880f6))



## [0.1.13](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/compare/v0.1.12...v0.1.13) (2024-09-06)


### Bug Fixes

* no.2834264 解决EB思维导图支持高级版位置优化 ([64cbabb](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/64cbabb8395ce66f9cf3f9a435532a5762ec020d))
* no.2834264 解决EB思维导图支持高级版全屏情况有部分遮挡的问题 ([3aeffef](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/3aeffefdac6b8e37087016f1a3fec87b3698a168))
* no.2834264 解决EB思维导图支持高级版居中问题 ([950c366](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/950c3668a14a96a3296db95e87dc6cebc7b96a19))
* no.2834264 解决EB思维导图支持高级版居中问题 ([fe7c69a](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/fe7c69a9aa9731b0fdf568df5f3cd4bbceee7003))
* no.2834264 解决EB思维导图支持高级版根节点隐藏，滚动条不能用 ([734b427](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/734b4277008b6eedbf4469f24695d4d3a5c90124))
* no.2834264 解决EB思维导图支持高级版测试问题 ([354dd6c](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/354dd6cd8a6ed55e0014c6e303abab56f95598bc))
* no.2834264 解决EB思维导图支持高级版测试问题 ([93d3576](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/93d3576ba22639121540af66d95fba7f9ed67953))
* no.2834264 解决EB思维导图支持高级版测试问题 ([9e359af](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/9e359af0170fdabc9fefd6935fc1918533f29f88))
* no.2834264 解决EB思维导图支持高级版测试问题 ([6ddad51](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/6ddad51ca67619df984c6cf52504364f4921793d))
* no.2834264 解决EB思维导图支持高级版相关问题 ([fc00e0c](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/fc00e0c03eb4a589b891a835a143ea24150f5e12))
* no.2834264 解决EB思维导图支持高级版相关问题 ([89d5758](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/89d575829b7329bc1f3a8730601df8c6cbaf89dd))
* no.2834264 解决EB思维导图支持高级版节点点击不生效 ([25492c4](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/25492c449c87d328d4f44c3e3a79584c99d2232b))
* no.2834264 解决EB思维导图支持高级版连线样式设置不生效 ([a876c8a](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/a876c8a88ead0d9e994cbed6aa88ba61f072dfa1))
* no.2834264 解决ie下偏移量问题 ([4d322f7](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/4d322f763b2a48aaaf4b27da5bbba3898617fbc6))
* no.2834264 解决ie下插件判断问题 ([9967177](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/99671776d6ec542704aa739f06afc6f20cb821df))
* no.2834264 解决ie下自定义节点兼容性测试 ([0e8a92d](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/0e8a92d1fffe65964a5da4f083641bb573a66233))
* no.2834264 解决ie下自定义节点兼容性测试 ([31a52c2](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/31a52c20af112a2eda5a62d64a1b98b59f750226))
* no.2834264 解决ie下自定义节点兼容性测试 ([d9f5893](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/d9f589385bbbef5d309868ab760ff5bae59d6420))
* no.2834264 解决ie下自定义节点兼容性测试 ([39cd2d8](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/39cd2d80564db46ed4afb0f61ccf30c538abfcca))
* no.2834264 解决ie下自定义节点兼容性测试 ([8ada217](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/8ada217a8d46305dd3ad10088cae3a56bc65f6a4))
* no.2834264 解决ie下自定义节点兼容性测试 ([76d491b](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/76d491bc579aa1e0384ddc40b3892b5465bd47dc))
* no.2834264 解决ie下自定义节点兼容性测试 ([415aa05](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/415aa050ed89850bc9c81fb0eb0f08791eafc24e))
* no.2834264 解决ie下自定义节点兼容性测试 ([94c643b](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/94c643b9622f55a6fe8d82e26556fba78ad66388))
* no.2834264 解决ie下自定义节点兼容性测试 ([ec7c0ed](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/ec7c0eda1b281766cd441ee1da258dcfb9c12ddc))
* no.2834264 解决ie兼容性调试 ([bddceae](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/bddceae6cd5e7012d24ce13b473a8118ab47f343))
* no.2834264 解决ie兼容性调试 ([1bae73a](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/1bae73a27b23a63395d2e031c9968b45600a822a))
* no.2834264 解决ie兼容性调试 ([c848b5b](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/c848b5b420956629e00d8bfdc4c2583db24d46e8))
* no.2834264 解决ie兼容性调试 ([5013ac8](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/5013ac829bd9e2dbe29080131cf4f6548d9d9e6e))
* no.2834264 解决ie兼容性调试 ([f05215f](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/f05215feb943ac85c7c2c89ffb5ab1f29f1f2073))
* no.2834264 解决ie兼容性调试 ([87bf640](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/87bf6408f1d21270c258ec491fa259670dee1204))
* no.2834264 解决ie兼容性调试 ([efc4f4e](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/efc4f4ecc51620839d0835db8a570c2c80ef21f7))
* no.2834264 解决safari兼容 ([6d33771](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/6d337712d8f6a9f8ea81852f1d653b05455b1f7a))
* no.2834264 解决偏移量问题 ([07331f7](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/07331f707baab57555de488e4219e3247c7562c7))
* no.2834264 解决内存调试xia相关 ([c1a52ca](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/c1a52cac113e7a9c97f76c7535e2225673f5cfde))
* no.2834264 解决导图构建相关 ([773c60f](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/773c60fcaa623a0d2c2af93874cd80caae235f50))
* no.2834264 解决导图节点不显示问题 ([14fca2b](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/14fca2bcf4ec425aa5d75766772492b1776f2101))
* no.2834264 解决底层插件打包ie测试 ([38a0334](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/38a03349594515f65f0112a981f4f045615fae7f))
* no.2834264 解决底层插件打包ie测试 ([83378a4](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/83378a440544874292e6601b0a7a8cd2fd5f9ffb))
* no.2834264 解决底层插件打包ie测试 ([9f2d931](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/9f2d931a861c63dd08edd5f29e7791e9b21dd9ff))
* no.2834264 解决底层插件打包ie测试 ([7cd083b](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/7cd083bac440c126091204a8135caadb3bcba32a))
* no.2834264 解决底层插件打包ie测试 ([5c43923](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/5c43923c8d4cd5445982041bf35406d85eb63416))
* no.2834264 解决放开渲染方式 ([5601657](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/5601657708d8f3bc5015dcc56b1344fb0a23fda4))
* no.2834264 解决放开渲染方式 ([44fa157](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/44fa157d4fcc666b6ab2d8260bdfb336f3741d1b))
* no.2834264 解决新版插件容器大小问题 ([046df82](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/046df82e25b4386ae75bdc9f1f89969d2265a022))
* no.2834264 解决新版插件自适应大小问题 ([fa555f2](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/fa555f2f608af36cf432122c41a25d6872ded389))
* no.2834264 解决测试环境测试 ([7e20838](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/7e208387cc5946546c95bbadd6f82606c8bfa493))
* no.2834264 解决自定义卡片序号字段不显示 ([b1438a3](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/b1438a3c9768407df915c889850e042ebe3b200f))
* no.3069000 解决思维导图卡片显隐点击不生效问题 ([6fa2fcc](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/6fa2fcc05d1519cba17e96050323ad9cf0a5083a))
* no.3069000 解决思维导图卡片显隐点击不生效问题 ([652a004](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/652a00466897efac2cbaa4c484354c2339f917c7))
* no.3069000 解决思维导图卡片显隐节点缓存问题 ([f230b19](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/f230b199bc71a88f6d7fc30799bd7c026496a9ea))
* no.3173078 解决ie11下不显示问题 ([084ed73](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/084ed73baf3164d2c7a768bfc6490cf67c6583e4))
* no.3173078 解决ie测试 ([415dadd](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/415dadd9101b3ce9b2fae72151806d31801bb29f))
* no.3173078 解决ie测试 ([627b460](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/627b460ad1be025dc6410fd0f500541a8169b4e8))
* no.3173078 解决ie测试 ([2d6a04f](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/2d6a04fa458453bb84ed142e9b7361a4e7e7926e))
* no.3173078 解决卡片在ie下不显示问题 ([579df93](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/579df93977523e3da496ce9a2f924448f98f58e2))
* no.3173078 解决卡片字段显隐出现已删除的节点数据 ([aa33394](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/aa333945b62c08004843664a7fd93fd378483fbe))
* no.3173078 解决卡片字段显隐节点编辑后名称被同步的问题 ([30e8548](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/30e85487a339684142c80b8bdd8961b18140eea2))
* no.3173078 解决外部传参取值问题 ([275fa5c](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/275fa5c569f1a92777f7f3f1635a6118e5bb354d))
* no.3173078 解决导图居中问题 ([0a4610d](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/0a4610d4c11f089a2d388fe352de28e628ee2f27))
* no.3173078 解决导图居中问题 ([e5b6e91](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/e5b6e91581a1b6f9efeb9cd1d1c038c5e83af2b4))
* no.3173078 解决思维导图新版国际化问题 ([ef7a506](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/ef7a5063999af9562714005b105f9ba4c8d386fc))
* no.3173078 解决思维导图新版相关问题 ([310e81d](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/310e81d3b3dc32b0701349fd0970a400d8d6255a))
* no.3173078 解决思维导图新版相关问题 ([ab5308e](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/ab5308effc59a29102c0cac04b9d3cb06ad8f1b7))
* no.3173078 解决思维导图新版相关问题 ([35c53fa](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/35c53fa3f95263bd75b37e0443eab1ab63d39c6d))
* no.3173078 解决思维导图新版相关问题 ([657ed69](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/657ed69e99f75ebe1002fbc0b37459f6c2bee057))
* no.3173078 解决思维导图新版相关问题 ([9d9c087](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/9d9c0878bb2f2794859b91e0c5174b1cac26c2bd))
* no.3173078 解决思维导图新版问题 ([f763fd1](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/f763fd13499e3a2cc08f7cf1ea76cb512538e9c4))
* no.3173078 解决点击右侧推出按钮不主动推出问题 ([c40a513](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/c40a51356c8721a31cc77e81d7fea3274524d418))
* no.3173078 解决还原导图卡片 ([dbb966a](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/dbb966aecbdffc4c24b22049d5049b386f3f3cbc))


### Features

* no.2024031111 新增导图支持刷新组件传值 ([ac68049](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/ac68049b02e1a1e702b084c0aa7984a8c57ea064))
* no.2834264 新增d3render ([8161d42](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/8161d4212f379356d50b6a635d2c6ee0e3af40d7))
* no.2834264 新增EB思维导图底层插件版本升级 ([8ee5f11](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/8ee5f119f97c76d24b760e54c5f97828263a6d80))
* no.2834264 新增ie11兼容性测试 ([6915840](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/69158404caa6a719d287d6a9732569f40fd77006))
* no.2834264 新增ie11兼容性测试 ([176116c](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/176116cdd13fc95887dcd0ac2be727b3f6abccbb))
* no.2834264 新增ie11兼容性测试 ([8006ff4](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/8006ff44ac016b5fb5a3d68f15778a334b447996))
* no.2834264 新增ie11兼容性测试 ([416e6be](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/416e6be11ba23693211a7128978e8aaa57ac29e6))
* no.2834264 新增ie11兼容性测试 ([0038f71](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/0038f71965a9f68796253cd06b3b73fa5f761377))
* no.2834264 新增ie11支持 ([5035e54](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/5035e54ff92ff8153093bb5839c1b6cc070dac78))
* no.2834264 新增ie11支持 ([355739f](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/355739fdd2cf2d1e13f739c8b376ef1d8b958530))
* no.2834264 新增全屏展示 ([2fafb8d](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/2fafb8d7eaa606c65fb09c867664d31d64ff42ca))
* no.2834264 新增全屏展示 ([03098cc](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/03098cc5419eca1da2a6228533b93a7d0b0220e8))
* no.2834264 新增全屏展示 ([cc5c58f](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/cc5c58f5bcbf2c7f1bdb70c6d6df8e48d44b63df))
* no.2834264 新增兼容性 ([35a3370](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/35a3370a87d7d585914b651a8117073020d9a36a))
* no.2834264 新增兼容性 ([71bb771](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/71bb771d9b31235427421ce968b31c75598512ba))
* no.2834264 新增兼容性 ([54f260a](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/54f260a165249efbf333df443a7fca585ba7afca))
* no.2834264 新增兼容性 ([049f82e](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/049f82e698f8e29b8b34e8377fdcb83fe1c0ff26))
* no.2834264 新增功能配置对接-界面缩放、全部展开、全部折叠 ([a984ce2](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/a984ce21ccf4a55328799ca22855433dbdfbff98))
* no.2834264 新增功能配置对接-界面缩放、全部展开、全部折叠 ([33427ff](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/33427ff9bbe2168a26e1f6f3a7cb17237f7d4c8f))
* no.2834264 新增功能配置拆分为基本设置和底部工具栏配置并兼容老数据;新增节点收起时是否显示收起的数量 ([8d6f988](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/8d6f988dd3f0f5acfd15fe98ce00347c60d041d0))
* no.2834264 新增底层插件库迁入本项目 ([40c012c](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/40c012c1b30dc26afc385504a510bb7fb2c3679f))
* no.2834264 新增底层插件打包兼容polyfill ([3037509](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/3037509d9da018c795bdb91ce99d9cafe4ab5de2))
* no.2834264 新增底层插件打包兼容polyfill ([0f86a6c](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/0f86a6ca6df647b61f41f049c215a371b4ab8634))
* no.2834264 新增支持按层级配置展开收缩图标颜色（插件不支持，已优化原生代码支持） ([a55dd20](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/a55dd2062f84404465d080608414e9fd7448182b))
* no.2834264 新增支持点击回到根节点 ([4bef6a5](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/4bef6a5c51a58288a7e2d01181f1ae90c14b3db7))
* no.2834264 新增支持隐藏根节点 ([9de24c8](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/9de24c8bfd829273f9cc071ce19cbb1f76e666df))
* no.2834264 新增支持隐藏根节点 ([863fb41](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/863fb41217d28ab94c161684497eaa893a287467))
* no.2834264 新增新版思维导图插件引入 ([ec84681](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/ec84681366c61f302b2e2e2e5d6323c55254365e))
* no.2834264 新增新版思维导图插件引入 ([6ec02bd](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/6ec02bd346495a6e5496fa6a84ca73d410e0a51d))
* no.2834264 新增新版思维导图插件引入 ([7094c8f](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/7094c8f8d44de81e10620ea974d11afe626623e3))
* no.2834264 新增新版思维导图插件引入 ([5e118db](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/5e118db15c474f217f77c7acd2f4a6167554f840))
* no.2834264 新增横纵向滚动条，同步画布滚动 ([27cc639](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/27cc639577ca43dd715e44f82214492110f08eb5))
* no.2834264 新增节点样式设置适配 ([c184e72](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/c184e72317eb9f8d044cc88f9c30bfd314ec7e44))
* no.2834264 新增逻辑结构图默认主题配置 ([c450461](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/c450461eabffa0d814f4325218cf6b3981a6f5ad))
* no.3140432 新增【21834】独立部署ebuilder服务动态路由改造 ([72829e9](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/72829e9a7e64e30971c0ef400348f93f2f3f5e7d))
* no.3173078 新增动态引入ResizeObserver ([79b5aa4](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/79b5aa4fc13cadffccafec2d82c24c4d3ec013be))


### Performance Improvements

* no.2909186 优化新增思维导图图例设置 ([5526514](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/55265140d06def38348ba0475d9393c90279914e))
* no.2909186 优化新增思维导图图例设置 ([a9ffdf7](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/a9ffdf7005d5220d5cd8a2e4d605be9d465c50fc))
* no.2909186 优化新增思维导图图例设置 ([f3db381](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/f3db38185fcad7db5e11386785822cb75692d513))
* no.2909186 优化新增思维导图图例设置 ([33acc25](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/33acc25d3e3173479e5d2da9912564791d8da685))
* no.2909186 优化新增思维导图图例设置 ([6327492](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/632749223a1d66221a461da4bc1fc5869ddeb83a))
* no.2909186 优化新增思维导图图例设置 ([f7160cf](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/f7160cf1775d1856b4297d32f4c135c950dbb9f8))
* no.2909186 优化新增思维导图图例设置 ([51079dc](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/51079dc60b816f43d1962682a8281b0fad28ee5a))
* no.2909186 优化新增思维导图图例设置 ([2b250b3](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/2b250b3f83266eb0651d605f8d14d869d14d7349))
* no.2909186 优化新增思维导图图例设置 （rea修改同步dev） ([2564781](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/25647817057d49a6aa204687092744cf4fb9b2a0))
* no.2909186 优化新增思维导图图例设置 (代码同步v2版本test) ([a9562b9](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/a9562b9b90aeb90b890070520229e9d372c4c6d4))
* no.2909186 优化新增思维导图图例设置(合并test) ([54c0d95](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/54c0d958be358a5bd244b4f2833d4f1fcdc623c9))
* no.3122616 优化EB仓库组件打包体积的结果的功能 ([d4d2aba](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/d4d2abaf4a5fb2317837761b8650172f3994f3bb))
* no.3122616 优化EB仓库组件打包体积的结果的功能 ([9cb0cd7](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/9cb0cd78ec20c3662ecae1816fb28f8eca685980))
* no.3177731 优化思维导图图例勾选问题 ([72a03ee](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/72a03ee28d45ad3dab8146b8ee3adf95d8ea85dd))



## [0.1.12](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/compare/v0.1.11...v0.1.12) (2024-08-09)


### Bug Fixes

* no.3121716 解决思维导图页面下节点动作配置不生效 ([bdf95f5](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/bdf95f5f57aa7d117d3ea8d0b213a1bd747531a9))
* no.3121716 解决思维导图页面下节点动作配置不生效的问题 ([9e27c03](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/9e27c03424cefbb6b1a3233ba1007e61969782af))
* no.3123647 解决思维导图刷新组件支持选中 ([64d943f](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/64d943ffc7cd6cc939a8557f8c5f013a8b9360b0))
* no.3123647 解决思维导图请求数据时支持平铺传入路由参数 ([2863c25](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/2863c25bc2edbc6931083f96ebd2c6bde560c7ad))
* no.3123647 解决思维导图高级视图打开编辑器节点动作详细编辑保存报错问题 ([0717312](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/0717312d47c6dccd885093f2c97bebd542840db6))
* no.3138469 解决思维导图卡片显隐设置不显示按钮问题 ([36292a7](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/36292a7b9311dde39ad6bb779340978f01f8ba8a))
* no.3138568  解决思维导图卡片显隐，全部取消展示不会记住勾选结果问题 ([477ca35](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/477ca3568394d20e198ff856f4cff4e5470c4e71))
* no.3138568  解决思维导图卡片显隐，全部取消展示不会记住勾选结果问题 ([a37f9ba](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/a37f9ba9010f8842cedf387d92a72f1c5bb4cda9))
* no.3168522 解决思维导图关联关系设置筛选，对应列表无法筛选问题 ([14552ec](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/14552ecfa8b2897edf872c150990281178e3068c))
* no.3168522 解决思维导图关联关系设置筛选，对应列表无法筛选问题 ([0f00c69](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/0f00c690eeab34120198d1c20c07e8720185c843))
* no.3168522 解决思维导图关联关系设置筛选，对应列表无法筛选问题 ([4ff0355](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/4ff0355564b370becc48a89f9baecf11b3c71361))
* no.3168522 解决思维导图关联关系设置筛选，对应列表无法筛选问题 ([8c72908](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/8c7290877f762cf3ace26034b58f0c0c25a95293))
* no.3168522 解决思维导图关联关系设置筛选，对应列表无法筛选问题 ([ddc9f67](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/ddc9f670a21d8c752d4ed108f84aa41ce073173d))
* no.3168522 解决思维导图关联关系设置筛选，对应列表无法筛选问题 ([a6208b6](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/a6208b66455b3ff0085379b410b4f5230196cb57))
* no.3168522 解决思维导图关联关系设置筛选，对应列表无法筛选问题 ([351d46f](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/351d46febc449caba4c1e4384dd81c5bfc8b47ea))
* no.3168522 解决思维导图容器内无法撑开的问题 ([a454c00](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/a454c00e5fdb85eda3f5475a582193a114ad36cf))
* no.3168522 解决思维导图容器内无法撑开的问题 ([fcea722](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/fcea722fbe5aaa5b87b5f57a3c6f9a4d1b45d725))
* no.3173078 解决思维导图——高级视图设置图例后，设置项丢失 的问题 ([a97803c](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/a97803cf1384069032fcefa437be27cdbfc8cafc))
* no.3173078 解决思维导图保存配置失效 ([4e40e76](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/4e40e76f24ee4ee5798fc9c57c7feedcf22a2541))
* no.3173078 解决思维导图保存配置失效 ([152f3aa](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/152f3aa8802e4c9611773adbcfcaf7ff57b4fc59))
* no.3173078 解决思维导图节点配置重复保存配置失效 ([14fff20](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/14fff200539f08ddc72dc9572bd3facbe178cebb))
* no.3173078 解决思维导图节点配置重复保存配置失效 ([8781912](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/8781912623d88534e0d617f10a1c6805ab7fe4d6))


### Features

* no.3140432 新增【21834】独立部署ebuilder服务动态路由改造 ([195648c](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/195648cfbba30fce0a77005ca9cce7c763f0acd2))


### Performance Improvements

* no.3164031 优化思维导图高级视图不撑开问题 ([a5e6fa2](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/a5e6fa21ca7f6ed442a3640eeb7a9b22864c28aa))



## [0.1.11](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/compare/v0.1.10...v0.1.11) (2024-07-15)


### Bug Fixes

* no.3069000 解决思维导图卡片显隐，设置项不记录修改后的名称的问题 ([258afe3](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/258afe365852532b72b2adbbf4eac5bea6eaf2c3))
* no.3069000 解决思维导图卡片显隐节点缓存问题 ([6f92e4c](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/6f92e4cc7f695e0d2fd6f6e5e5304898c2b2556b))
* no.3112130 解决思维导图根节点设置图片和尺寸后，二次进入会还原问题 ([65ed3f3](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/65ed3f35ddeba77f906144d5a950ae7e77823161))


### Performance Improvements

* no.2909186 优化新增思维导图图例设置 ([23a5405](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/23a54054951472217b5af1b5c89c1a221db3d739))
* no.2909186 优化新增思维导图图例设置 ([7c45952](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/7c459528798c260b5dbc358b1fbeb736304961da))
* no.2909186 优化新增思维导图图例设置 ([f60a8b0](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/f60a8b038652194cf823a9668032b6a7aa63c96d))
* no.2909186 优化新增思维导图图例设置 ([1c98826](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/1c98826e5aa876283738b6f7ea71d501d87513ab))
* no.2909186 优化新增思维导图图例设置 ([70c2f84](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/70c2f8484999ef451ce30d8b473d27ad1e061099))
* no.2909186 优化新增思维导图图例设置 ([94d0db3](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/94d0db32bd6294070713e77441180c359e96dedb))
* no.2909186 优化新增思维导图图例设置 ([3bcd0bd](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/3bcd0bd78f5d6c12503353eee6dc5b921714e1b7))
* no.2909186 优化新增思维导图图例设置 ([3854a52](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/3854a520a991a89592fd65625b62b902cdb4a59e))
* no.2909186 优化新增思维导图图例设置 ([3207d3e](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/3207d3e1cef1417a3c8187f5efd329abf814da85))
* no.2909186 优化新增思维导图图例设置 ([04ccbaa](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/04ccbaae0ae96195fb52de6b7267e8cfda132bab))



## [0.1.10](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/compare/v0.1.9...v0.1.10) (2024-06-14)


### Bug Fixes

* no.2746860 解决显示字段老数据兼容问题 ([578a4f7](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/578a4f778c90680476f63e903face3f22bd24a75))
* no.2969544 解决卡片显隐相关问题 ([b52a1d6](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/b52a1d6b553fcabad5ac63d6465fa04e0264148a))
* no.2969544 解决卡片显隐相关问题 ([ede0108](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/ede01089a0bd4e613214a5e5d9bd0eceea9e8661))
* no.2969544 解决卡片显隐相关问题 ([ac76817](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/ac76817f667b07173ea76e3e0e831da04cd0b503))
* no.2969544 解决卡片显隐相关问题 ([4da31ed](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/4da31eddcae51d3d4977e0ec10efeebdd1a1c2f9))
* no.2969544 解决老数据兼容 ([adb1355](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/adb13551da5f3b144228e83b708aa03eeae86e73))
* no.2969544 解决节点字段显隐国际化问题 ([4ec3d62](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/4ec3d62daf0719547c6314a90353d72a2a3b8a3c))
* no.2969544 解决节点字段显隐相关问题 ([f649ea0](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/f649ea08426f48201073849dba2fabfdab0f6699))
* no.2969544 解决节点字段显隐相关问题 ([f6195f5](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/f6195f5fcb5d1f40c9be8042f2cb1f153f957867))
* no.2969544 解决节点字段显隐英文下显示问题 ([97ce4ad](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/97ce4adc6a802e9587e12e1b70b3d97e7d1ae147))
* no.2969544 解决高级视图下设计器内节点字段兼容老数据排序显示 ([8ebae87](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/8ebae87bde4f7e2c2c78d09485d5502d46bbf2f4))
* no.2969544 解决高级视图下设计器内节点字段显隐相关问题 ([ae508da](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/ae508dadde844cff230120d7c24fac16083d526e))
* no.2969544 解决高级视图下设计器内节点字段显隐相关问题 ([9c90947](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/9c9094785d52303589b51c04dd76af4f71faa05a))
* no.2969544 解决高级视图下设计器内节点字段显隐相关问题 ([b140ae5](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/b140ae5cf650918d88951142ec49dbc425c02255))
* no.2990400 解决思维导图-保存显示字段前端报错 ([2867b86](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/2867b863f5b7cfcbff2aff0f3f825b5a48f4a872))
* no.2990400 解决思维导图-保存显示字段前端报错 ([6d55407](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/6d554073c4df50349e38c90d21dcc1a45764fedb))
* no.3003702 解决思维导图高级视图配置跳转本表单布局时间动作右侧滑出不生效问题 ([d366365](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/d366365833124013198c8ad91f8485acb9b94a3f))
* no.3003702 解决思维导图高级视图配置跳转本表单布局时间动作右侧滑出不生效问题 ([1a5b329](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/1a5b329b431b1cf01c6ba8f84a21811b2c542e3f))
* no.3003702 解决思维导图高级视图配置跳转本表单布局时间动作右侧滑出不生效问题 ([cccd4b2](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/cccd4b2fdabcb7a20c0476f313bf5d01fe948845))


### Features

* no.2688972 新增卡片支持配置显示内容开关 ([cab842e](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/cab842e2dab39dd78c04391c57adf569d3cf1465))
* no.2746860 新增下拉选项支持全选 ([b0a1285](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/b0a1285e7be92014128f7b02b0be0fb7eb5cd3a7))
* no.2746860 新增名称支持国际化 ([0f7b4e4](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/0f7b4e44d8c688e72719f8d25a1b1ce7bd381785))
* no.2746860 新增国际化处理 ([ed688eb](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/ed688eb8daf1c01ffc169cd873ff110b4d371ebd))
* no.2746860 新增国际化处理,新增是否记住勾选结果配置 ([fd5c31b](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/fd5c31b32b36d3c5227b57b5f52f603c0e9c4b1b))
* no.2746860 新增思维导图组件支持配置前台显示内容 ([3629d80](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/3629d809d0a30ac54f59b0fc59ad45a04c9dd788))
* no.2969544 新增节点字段显隐支持区分字段名和节点名 ([591e28f](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/591e28f32195f18964d69e0818ddfbcd419ffc55))


### Performance Improvements

* no.2746860 优化下拉显示为下拉框展示 ([2a88593](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/2a8859387511949f97316ad2b0bb7bd314e8df7f))
* no.2746860 优化思维导图组件支持配置前台显示内容兼容数据不含name场景 ([5a0deb5](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/5a0deb510474475136e4645f58fa41149796cb54))
* no.2969544 优化节点字段显隐配置效果 ([171844b](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/171844bee6b00a3a5174a7c83a9b7e493f087890))
* no.2969544 优化设计器内节点新增唯一标识 ([6b7d26f](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/6b7d26f35bcc72e1a33ec5d95110bad479ced6d5))



## [0.1.9](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/compare/v0.1.8...v0.1.9) (2024-05-17)


### Bug Fixes

* no.2936911 解决思维导图-高级视图问题 ([911fc7d](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/911fc7dabdaa9ccc0c18e71889708065c37b59fb))
* no.2936911 解决思维导图二开口子问题 ([047dbe3](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/047dbe37663b78225afe19ab4f540ffb5e7abf32))
* no.2936911 解决思维导图二开口子问题 ([79df01d](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/79df01d984c30b97ca893437dd1d37a47d3e2d3e))
* no.2936911 解决思维导图二开口子问题 ([ca0584e](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/ca0584e0386043fea4bf67a98b36ed8f5206ea29))
* no.2936911 解决思维导图二开口子问题 ([197e120](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/197e120129dbaf4de49e45b2874a1c8229f789ee))
* no.2936911 解决思维导图二开口子问题 ([0e23edf](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/0e23edfd5c46dfc7a180d19774433732c7c58685))
* no.2969544 解决思维导图二开口子问题 ([f99471b](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/f99471b998c5cbafa25697015ef599678132a21b))
* no.2969544 解决思维导图二开口子问题 ([877896d](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/877896d534ef1384def0d37b788ca362f06843d9))
* no.2969544 解决思维导图高级视图-配置节点标题后，提示系统错误 ([efc40b9](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/efc40b9e43cd1be630fac6da0c5b37dc75824748))
* no.2969544 解决思维导图高级视图-配置节点标题后，提示系统错误 ([4538478](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/4538478d9b9e1814c0258dfa55f2ec3c01e56bc6))
* no.2990400 解决思维导图-保存显示字段前端报错 ([379228d](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/379228d8da8a630f90dcf0fca36461d31a7c821f))



## [0.1.8](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/compare/v0.1.7...v0.1.8) (2024-04-12)


### Bug Fixes

* no.2801185 解决ebdmindmap,ebdboard步骤条多语言静态变量修修改（思维导图0326） ([bacdbe5](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/bacdbe593ec60fd45a3224fded382c81e9d18377))
* no.2801185 解决ebdmindmap,ebdboard步骤条多语言静态变量修修改（思维导图rea0326） ([2e28e1f](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/2e28e1f82e30fa410fddd4af85e17829478c64a2))
* no.2891478 解决思维导图测试内存问题 ([1b5ff9a](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/1b5ff9ab43def12d0b30127efc48457277e6f0b1))
* no.2917301 解决jsmind插件展开收缩无响应 ([d90142d](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/d90142dc3f2f4a536d9add58dc576f8892e39854))
* no.2917301 解决jsmind插件展开收缩无响应 ([95d61da](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/95d61daba73b700f27d21224662716c0c578ac0a))
* no.2917301 解决切换不同思维导图导致白屏问题（监听事件卸载机制优化） ([d9ab857](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/d9ab8575c56448c67188c64b79e3f467712a3e63))
* no.2917301 解决思维导图jsmind插件升级 ([4d06be7](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/4d06be7890a7eda6ab4eedf88861a4a09d2ed5c6))
* no.2917301 解决思维导图不加载问题 ([0982527](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/0982527f13e6754dd74e6a43826d75a105aa8526))
* no.2917301 解决思维导图不加载问题 ([ff179b8](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/ff179b8b9a98c434f40055127b9fcd5568af6045))
* no.2917301 解决思维导图性能优化 ([4b3642f](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/4b3642faf1c555f81f5405bad61c0e43724cda6b))
* no.2917301 解决思维导图性能优化 ([3059a5b](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/3059a5bb1c0d2b0d48c47acc97292cd498841c29))
* no.2917301 解决思维导图插件版本升级 ([cf933df](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/cf933dffb388ec04c0d0239a71b2b9e77ae3145a))
* no.2917301 解决思维导图插件版本升级 ([78cfd15](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/78cfd158ebb447cf4f8743e4798849514b03e07c))
* no.2917301 解决思维导图插件版本升级 ([a4bd41a](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/a4bd41a112736eff2fbe5b54a90d8df345fd8157))
* no.2917301 解决思维导图标准业务数据源相关问题 ([8e3572e](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/8e3572e879a49b03f852ddb634e20827cc18e12c))
* no.2917301 解决思维导图根节点显示名称不同步刷新视图 ([899c660](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/899c660e1f37cbd7a93fa7e1f8a101b1ea9b55ca))
* no.2917301 解决思维导图组件切换时空白 ([52e9309](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/52e93095df770cf3e9c68a58fd87683207a2077c))
* no.2917301 解决思维导图组件切换时空白 ([13cad29](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/13cad29bbe7eb13582f86d9fc4f812a0648436a6))
* no.2917301 解决思维导图组件切换时空白 ([ca0cd89](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/ca0cd894b7fcdefc2b652e4c5e0c50a915ffed5b))
* no.2917301 解决思维导图组件切换时空白 ([7841e27](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/7841e2781b1e4c84b9464650a35bec9e521177c6))
* no.2917301 解决思维导图组件文档显示不出来问题 ([a11fafc](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/a11fafce7ff26fc5e713edd7a2cf33ab92c7c55f))
* no.2917301 解决高级视图下数据源 ([db1dbe7](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/db1dbe7e34eaad9629dda6539377ef907fd14d11))
* no.2917301 解决高级视图下数据源 ([e913153](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/e913153b767fde019eea3d8f2f4b33814a9cd395))
* no.2936911 解决思维导图-高级视图问题 ([66c5897](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/66c589753a46992cb78ee2a6c8a69192ccea4ded))
* no.2936911 解决思维导图-高级视图问题 ([1875976](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/1875976fd4a2dee0b16ab0a589cf3a12fa48da63))


### Features

* no.2924657 新增E10---思维导图支持横向左右双向展示 ([c58edbe](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/c58edbe8930b862bbfde25bb3cd232bdc2a34aaa))
* no.2924657 新增E10---思维导图支持横向左右双向展示 ([d5d99b9](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/d5d99b9cac92fe851139cd9c6c407e638d475707))
* no.2924657 新增E10---思维导图支持横向左右双向展示 ([43e4dd7](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/43e4dd70ca64a172ddef42fcb3201b13257411e0))


### Performance Improvements

* no.2836509 优化思维导图回归问题处理(卡片文本换行异常，通用设置设置所占位置调整) ([fdec60b](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/fdec60b3627dd2f481486551c4d1385fd14a942a))
* no.2836509 优化思维导图回归问题处理(卡片文本换行异常，通用设置设置所占位置调整) ([978aadf](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/978aadf25e17ef0d3a855e6f2c90090b4938d5b6))
* no.2836509 优化思维导图回归问题处理（根节点图标宽高失焦触发更新） ([97e38b2](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/97e38b29c65c6657f8fa48eba1c2d4be49740bf9))
* no.2847828 优化步骤条回归问题处理(图标设置修改尺寸不实时更新) ([de9327e](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/de9327e6b220deb65c242ab497edc9d95ac78d8d))
* no.2910623 优化思维导图-通用设置-节点设置固定宽高后，上下级节点间无法收缩 ([873bec3](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/873bec3e4b0b2ca136ee795d00ff265c4b956196))
* no.2910623 优化思维导图-通用设置-节点设置固定宽高后，上下级节点间无法收缩 ([d537d29](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/d537d29ac72a0d7dc934b283e0b119c6940cdacc))
* no.2916138 优化思维导图DS_Store文件去除 ([2f520f6](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/2f520f67c22cc4467856e8f3b6774072daaf8bee))
* no.2916138 优化思维导图DS_Store文件去除 ([e05d1ac](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/e05d1ac8f0f996c9200ba1c241e27290659e5af0))
* no.2916138 优化思维导图DS_Store文件去除 ([3d19bf9](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/3d19bf975608f989e05d536bb1071b82340df9b7))
* no.2916138 优化思维导图DS_Store文件去除 ([751a538](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/751a538fa19eabb7383d8f5a1afa1cb74c3187ed))



## [0.1.7](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/compare/v0.1.6...v0.1.7) (2024-03-22)


### Bug Fixes

* no.2799232 解决放开二级字段上级字段 ([055b806](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/055b806226d72999676bb8e4a4cfaa27f371f47a))
* no.2799232 解决数据源屏蔽外部,自定义和数据集合 ([1df256b](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/1df256b1ad430d515169ca2120fa0a1b7addb0f3))
* no.2799232 解决获取数据截图重复请求问题 ([83d6d9f](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/83d6d9fe9a06568a8c6d2d9fd35cd5c343300811))
* no.2858079 解决以下问题 ([eb1cbdd](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/eb1cbdde7bf24b0a92586dc9f6618b0caf7e04ec))
* no.2858079 解决以下问题 ([b1ac580](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/b1ac5800c6a06a1f05ca6997920d7d029ac118a2))
* no.2858079 解决删除已有的显示字段，节点未展示显示框 ([6fdb607](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/6fdb607b589970cead353e563d842df1945e3918))
* no.2858079 解决删除已有的显示字段，节点未展示显示框 ([1c70664](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/1c7066452be9c0fc5abb6258c76fcd1fe89def7b))
* no.2858079 解决同级节点无法拖拽;屏蔽字段配置拓宽区域 ([cfc9049](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/cfc9049a33d6a44e20b9cbbce6f06cdce3bdfd63))
* no.2858079 解决同级节点无法拖拽;屏蔽字段配置拓宽区域 ([503a7d3](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/503a7d38ea657f052d317f1bcad2c8a8f87802cf))
* no.2858079 解决同行显示字段之间应有间隔 ([ea688a4](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/ea688a418681fb29400b4be82cbf742f382ed35e))
* no.2858079 解决放开数据加工上级字段限制 ([03a081c](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/03a081c20dd0941d52ff92845ee376b2f3f8c739))
* no.2858079 解决更新上级字段显示逻辑 ([206040a](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/206040acfe7034f5d0cd1816389c404573e7a131))
* no.2858079 解决页面有多个思维导图时接口被取消的问题 ([80d132a](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/80d132abd366d461741296d8bf8ad53dc869f8c0))
* no.2858079 解决默认展示层级无法保存[0301基线包缺陷问题，上线出hotfix修复] ([9371fe2](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/9371fe2483b638824efeeb022e19df5bd26fd774))
* no.2858079 解决默认展示层级未控制住eb表单内部的上下级展示 ([ff25bb3](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/ff25bb3a597a9863284054b826407f1afc368333))
* no.2884629 解决同级节点标准业务数据源限制添加 ([692a721](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/692a7210dccd9ebf4f7caf814bfc0bb7707d98af))
* no.2884629 解决思维导图--多数据源未渲染出显示字段 ([3492862](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/349286263b169eab4aa4261b23afeb655b87f2a2))
* no.2884629 解决思维导图--多数据源未渲染出显示字段 ([bc344d6](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/bc344d6b9515ae3e8ed1daa1f4b86592760fc668))
* no.2884629 解决拖动同级节点排序后，页面未即时刷新拖动排序效果 ([66b2d60](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/66b2d60dfa17abdb20ac45cbe45773d8013ef719))
* no.2884629 解决本级节点字段和上级节点字段增加对Number类型支持 ([adcc48c](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/adcc48c49e0bd30d6bbf723c302cfc99de7696a0))
* no.2884629 解决本级节点字段和上级节点字段增加对Number类型支持 ([0a06b05](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/0a06b05c06d94317189b291a90db30b972703abd))
* no.2891478 解决子节点有标准业务数据源时限制新增 ([9179fb7](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/9179fb76120a3b2da812fe26f2b8f876cef50524))
* no.2891478 解决子节点有标准业务数据源时限制新增 ([6f877c4](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/6f877c40cd42ed6e8891b35bde07ec4e96267447))
* no.2891478 解决思维导图上级字段逻辑更新为只能选择主键和关联ebuilder ([afc47c3](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/afc47c3b47355cc2fa1c98cb61f9df1bb1d87adf))


### Performance Improvements

* no.2836509 优化思维导图回归问题处理 ([4b6ad3a](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/4b6ad3a7d03bf599b81b85dd418b679e7e8b694d))



## [0.1.6](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/compare/v0.1.5...v0.1.6) (2024-03-01)


### Bug Fixes

* no.2799232 解决ie下思维导图自适应节点尺寸，数据ID被遮挡 ([8bd0a35](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/8bd0a352844a97813e5425461e3f4473373f10be))
* no.2799232 解决上级字段隐藏问题 ([dd12ae9](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/dd12ae92f0ada4c8eb2806b9660db12a0fec4b0f))
* no.2799232 解决四级节点的标准业务数据源匹配后无法展示 ([41bae27](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/41bae27de2dd8a18fc22b1b76f65dd6269193b7f))
* no.2799232 解决多次删除新建会导致三级节点部分不展示 ([6018e2c](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/6018e2c81571990ce5c8fbd17b85bf557502d74b))
* no.2799232 解决多次删除新建会导致三级节点部分不展示 ([a2ab8d5](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/a2ab8d5cfe807c9a278515836d5c7c172ac63c0d))
* no.2799232 解决多次删除新建会导致三级节点部分不展示 ([7d93780](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/7d937805085d62a4bf7059389f51d05b1fa4ece0))
* no.2799232 解决多次删除新建会导致三级节点部分不展示 ([c96a4d8](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/c96a4d8e5781c108a022e90d97a32d0e85252e44))
* no.2799232 解决屏蔽二级节点上级字段（二级节点上级节点是根节点） ([b536ab8](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/b536ab8e8a25f6ef103aee56171b591a11615fd2))
* no.2799232 解决思维导图弹窗打开时渲染问题 ([79fd30d](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/79fd30d3ca8e350ae766b7842c6e5f43b3aa4e11))
* no.2799232 解决思维导图未配置节点具体内容时，自适应节点内边距动态判断 ([66b326c](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/66b326c5352c0661fc3563e087ab187f8372a351))
* no.2799232 解决本级或者上级是数仓数据源，屏蔽上级字段 ([b151a05](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/b151a05d482b3a90b00be4b4374c5096f48266da))
* no.2799232 解决步骤条内嵌思维导图，设计器内，撑不开；预览后思维导图展示效果异常 ([741a79f](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/741a79f909bba4bc0b244a239f10f044204cf61c))
* no.2799232 解决步骤条内嵌思维导图，设计器内，撑不开；预览后思维导图展示效果异常 ([3268bd9](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/3268bd94e29903ed26482652fc9a89a35bd4d3a1))
* no.2799232 解决步骤条内嵌思维导图，设计器内，撑不开；预览后思维导图展示效果异常 ([6b52a5f](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/6b52a5f32ba0a669e19bf026f3b92a5f6b1f891e))
* no.2799232 解决步骤条内嵌思维导图，设计器内，撑不开；预览后思维导图展示效果异常 ([8b82473](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/8b824737a9af3f35b5c1b76b7dc1b0702bbca7fc))
* no.2799232 解决步骤条内嵌思维导图，设计器内，撑不开；预览后思维导图展示效果异常 ([fc14552](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/fc145520a3cd642574671f1d6c79a3f4fffd67f4))
* no.2799232 解决节点名称过长,做省略展示 ([76743a8](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/76743a8ce87b35f283a8bd0be674a604bafdf001))
* no.2799232 解决节点名称过长,做省略展示 ([b4d673f](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/b4d673fc8072581a96d1396e0fff627a2c959c82))
* no.2799232 解决较长的自定义内容，超过显示字段框 ([d0d5a73](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/d0d5a73e43eb45365f13e2fbb42e41e18ffdabe8))
* no.2826204 解决兼容根节点下标准数据源显示 ([77c45bf](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/77c45bf4485c39353ebe85300d459daf740d2d29))
* no.2826204 解决兼容根节点下标准数据源显示 ([fb14231](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/fb14231a8d3dca49ddeb96cdbefea89e22b43f21))
* no.2826204 解决容器下思维导图不显示 ([3dcf33b](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/3dcf33b6eb9192d083faedb9c8358ef0a1700f6d))
* no.2826204 解决屏蔽数据源分组设置 ([245b19e](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/245b19e040490a6a02aedfb3703528b26d68839a))
* no.2826204 解决标准业务数据数据源作为二级节点，无法展示数据 ([1b2257f](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/1b2257f767bc39041fa1b8522babf9ed28f2deec))
* no.2826204 解决标准业务数据源下屏蔽上级字段及上下级节点设置 ([d083f29](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/d083f29b8038f95b3fced7f4bc4b9f8f10991f44))
* no.2826204 解决配置标准业务数据源，层级展示不正确 ([5eb23ae](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/5eb23ae738eb5975b0b2638382771de5bc775633))
* no.2826204 解决配置标准业务数据源，预览展示-宽度未自适应该显示框 ([deef270](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/deef27061a578caa2257c5c01c8c0e8727a45783))
* no.2827855 解决根节点，隐藏根节点，节点设置， 图标设置前面标识应默认横向 ([b722953](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/b722953c2c705965f587c6ed341bff113a9d31a6))
* no.2836509 解决修复根节点尺寸自适应，显示名称较长，部分字段被折叠 ([51aeece](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/51aeece7bcd36eded5f61ae4520932e9a448bbce))
* no.2836509 解决容器内添加思维导图，无法撑开容器 ([9e910fd](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/9e910fd62544893877a8960aeee6407ad1f1584d))
* no.2836509 解决容器大小问题 ([8e2fe4a](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/8e2fe4ad61c448dd4608ffd15bef7a3d63e1f3fd))
* no.2836509 解决容器大小问题 ([f18af17](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/f18af17cfdf588f40731c3192ace369d2dc8e183))
* no.2836509 解决展开层级增加大于0限制 ([8e0c331](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/8e0c33133ae8e8e0d65b683b2eeb4a9b9fd2c7e7))
* no.2836509 解决根节点选择通用，没按通用设置走 ([6ac5677](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/6ac5677d87f32622c3bab98ae8f9c6583a17ec9e))
* no.2836509 解决节点尺寸自适应，序号展示不完全 ([7a20930](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/7a2093079aac547b9cb8b379bc44a29288c6096b))


### Performance Improvements

* no.2799232 优化eb文档 ([1d29ce5](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/1d29ce53fed7f9991f6a5b6fca488bc044966c68))
* no.2799232 优化eb文档 ([613b8f2](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/613b8f25ea3592e0d392a6275196c8a9e1272ddf))
* no.2799232 优化组件文档 ([89d9a85](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/89d9a8532ab932d2e0b081394d01660941572916))
* no.2827855 优化ie11思维导图滚动条问题 ([887d125](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/887d125f4f450d1f9d9cc8ac79c9ce1b2046eb0b))
* no.2827855 优化ie11思维导图滚动条问题 ([aec231c](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/aec231c01660c95083cabe3ba2207e69b57508ae))
* no.2827855 优化ie11思维导图滚动条问题 ([b215ab8](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/b215ab8c0f918c6ae06ae928a025bd2e1b0b8919))
* no.2827855 优化ie11思维导图滚动条问题 ([51f7fa5](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/51f7fa5201f912609b964e17b49a53b66e51b1e4))
* no.2827855 优化ie11思维导图滚动条问题 ([115f810](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/115f81015a0244ff78bab52242d311eee0a3d445))
* no.2827855 优化ie11思维导图滚动条问题 ([2068a06](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/2068a0677c05de0618b4a6a57d91a3718ffd930a))
* no.2827855 优化ie11思维导图滚动条问题 ([d903c71](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/d903c7171c8904e2ebdf7d5e3865e6e580fde75b))
* no.2827855 优化ie11思维导图滚动条问题 ([69076fb](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/69076fb087ada077cc6e8257b36195ed461c53e9))
* no.2827855 优化ie11思维导图滚动条问题 ([dfd5731](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/dfd573145386b3f1ad5c094c46ed07412657b56c))
* no.2827855 优化ie11思维导图滚动条问题 ([e239989](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/e239989326cb3d327bed860ce1980127bdcd4f37))
* no.2827855 优化ie11思维导图滚动条问题 ([dec8a67](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/dec8a6780b00b21b4e9ddb9976e5121653c382aa))
* no.2827855 优化ie11思维导图滚动条问题 ([635d959](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/635d959522fda2996a487baf80e42f0e5245a38d))
* no.2827855 优化ie11思维导图滚动条问题 ([7d33ce8](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/7d33ce80d3fb89df535ff791048bfad02e9afc28))
* no.2827855 优化ie11思维导图滚动条问题 ([dc8ec47](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/dc8ec47e7e8d6cd24ede5597f037a16895e8ec96))
* no.2827855 优化ie11思维导图滚动条问题 ([bb8bb72](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/bb8bb72fb82bf31f8da4c4d028f3baeae0aabede))
* no.2827855 优化ie11思维导图滚动条问题 ([87f9810](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/87f98103748f4374293c012a58ccce4c7acbc960))
* no.2827855 优化ie11思维导图滚动条问题 ([350a0a9](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/350a0a9ca77e09755ef18c8bc44c3fc7c9b2c93f))
* no.2827855 优化ie11思维导图滚动条问题 ([9d4efcb](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/9d4efcb740e216b36e5d2998654ff5f5d58c615d))
* no.2827855 优化ie11思维导图滚动条问题 ([68b1b76](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/68b1b76220d0ec345385076106c1848aa6b3b9b5))
* no.2827855 优化ie11思维导图滚动条问题 ([0686c45](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/0686c45040556c52db9f64286c8aa7f8e5d727fc))
* no.2827855 优化ie11思维导图滚动条问题 ([d3cb875](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/d3cb8758be456af4277e8443487992309b249b0c))
* no.2827855 优化ie11思维导图滚动条问题 ([78f8d38](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/78f8d3873cbbc001adaef3375fe71c2da889dfcd))
* no.2836509 优化思维导图回归问题处理 ([5e66ca9](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/5e66ca9a6cf04c82c049e28bbbbf73e5bbd0a393))
* no.2836509 优化思维导图回归问题处理 ([48eaaa8](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/48eaaa8a3109c7d99ea0de2da8c5194e8a516870))
* no.2836509 优化思维导图回归问题处理(ie样式问题处理) ([275ffac](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/275ffac91fd9bbfceb57de0e0434d2aad1f7013f))



## [0.1.5](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/compare/v0.1.4...v0.1.5) (2024-02-02)


### Bug Fixes

* no.2024000145 解决JSON.Parse精度问题导致解析数字时出现精度丢失的问题 ([61c1429](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/61c1429b2d4b6abc4fb158331b93345e2324108f))
* no.2729545 解决上级字段筛选优化 ([a8d3839](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/a8d3839fcecca3e5678c5208e86cc3dfb0994d41))
* no.2729545 解决版本抛出和支持字段点击事件 ([162aa11](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/162aa11eaaab3b4281c9996a8f37d4d742c23cd2))
* no.2784857 解决思维导图-节点样式-保存自定义类型后，无边框展示 ([63c3d43](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/63c3d43b82fd49cc85a3c62af95cd71160a79983))
* no.2799232 解决二开口子文档 ([9d5bb44](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/9d5bb44b80ecf21ce4757548ab4809a0e336c6b5))
* no.2799232 解决容器里思维导图增加默认最小高度 ([160b61d](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/160b61d473c09c76a6181389f8e594dd4f86dce3))
* no.2799232 解决显示字段配置明细表字段，显示不正确 ([f189ec9](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/f189ec94a464049433ace99312350b6e3ed8cbf3))
* no.2799232 解决添加多个同级节点，选择数据源后前端报错 ([9ee8229](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/9ee8229b59678ffbf70cb64cf00d81a4df310042))
* no.2801185 解决ebdmindmap,ebdboard步骤条多语言静态变量修修改 ([6b442fa](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/6b442fa5d45f98192743dc104f9618ea58867596))
* no.2810562 解决IE11兼容性-思维导图无法展示 ([b9a2d17](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/b9a2d17a7e92db740e64f6cedc691d937ef153ab))
* no.2810562 解决IE11兼容性-思维导图无法展示 ([ffdcde4](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/ffdcde4a9bbba6cec799013efc516ace4d518224))
* no.2810562 解决IE11兼容性-思维导图无法展示 ([c2f3168](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/c2f3168debc40d9edf5c9132921c87a53a4ddbf8))
* no.2810562 解决IE11兼容性-思维导图无法展示 ([cf9d8ab](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/cf9d8abd2ace6a434a4b4a96fa5fb796c25bb42c))


### Features

* no.2785882 新增思维导图二开口子相关sdk事件支持 ([e66b80a](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/e66b80a9bb27480d335167c1310da188732d3e56))
* no.2785882 新增思维导图二开口子相关sdk事件支持 ([f793f8b](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/f793f8b0a7051d1e232eac952fa14d0e5f0603fe))



## [0.1.4](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/compare/v0.1.3...v0.1.4) (2024-01-12)


### Bug Fixes

* no.2661444 解决自适应大小设置后有遮盖和多个思维导图同时存在时的高度塌陷问题 ([d367e04](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/d367e0451baec8c46d32485a3846e0ca9bf58e3d))
* no.2661444 解决自适应大小设置后有遮盖问题 ([18c47f9](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/18c47f9bff2f3ab15599d0988904572692fc51e3))
* no.2782994 解决节点动作内添加点击事件，未生效 ([a2b8171](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/a2b817132d35f08b5cfcfd8e1020d80277e963eb))


### Features

* no.2749911 新增思维导图排序节点拖拽优化 ([b586aca](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/b586acae6a30d88c0fdeeacdf5090e9175450f4e))
* no.2749911 新增思维导图排序节点限制处理 ([870de06](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/870de06b6ed48ec53a06ec7c3354a84b6a2ce4b8))
* no.2749911 新增思维导图排序节点限制处理 ([0535a22](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/0535a22a742bb8f23f7843d6f67902f0deb16e9e))
* no.2749911 新增思维导图排序节点限制处理，关闭子节点自动折叠 ([e412ef0](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/e412ef0e74839662ebdfb5c83138894e9487e396))
* no.2749911 新增思维导图排序节点限制处理，关闭子节点自动折叠 ([beea938](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/beea938c67171e44979e47cb5a389269a98bb573))
* no.2749911 新增思维导图排序节点限制处理，关闭子节点自动折叠 ([7e60b68](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/7e60b68b81779314c5936f399ee6fbbe59be56fe))



## [0.1.3](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/compare/v0.1.2...v0.1.3) (2024-01-05)


### Bug Fixes

* no.0 解决子节点下自定义事件参数带不上的问题 ([660784c](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/660784cf33366634e063d94fb9404bf449225ee0))
* no.0 解决思维导图根节点设置图标后显示异常问题 ([ab0b1a1](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/ab0b1a16476ffab9cd72bb6f77972f6f543aee93))
* no.0 解决思维导图节点删除后新加节点显示异常的问题 ([7a92df8](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/7a92df8bf25d2def016d94d758ff4c771bc60fd0))
* no.0 解决更新本地配置 ([82a2387](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/82a23870fcea630716cc0539d4e874f65f3c4694))
* no.0 解决问题 ([f472ad5](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/f472ad573228d2e72eb30d67ca75aa9f442ed233))
* no.2023044960 解决思维导图数据源，选择标准业务数据源，数据不显示 ([253be0d](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/253be0db3b5074808b663fa7a9ac61a46f187043))
* no.2023047445 解决标准业务数据审批、文档、邮件不显示数据问题 ([9ba44e8](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/9ba44e88dc6a012b6119e33cf7b928395d9aa160))
* no.2023050025 解决思维导图编辑器页面调整间距，页面支持预览问题 ([4d7f9e2](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/4d7f9e2f7ef65a28ccc293e81efd86c5d63295e3))
* no.2023053107 解决通用设置，设置后，节点重叠问题 ([8662ade](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/8662ade42f5495fe1fabd4fde9d3de3c22388c5d))
* no.2023053107 解决通用设置，设置后，节点重叠问题 ([b3b191b](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/b3b191b094e4025a2723762176c1af9db98d50f1))
* no.2023053107 解决通用设置，设置后，节点重叠问题 ([ae48c80](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/ae48c80afa2ca8f9218e9f80ab5b9f7ff66b5981))
* no.2023053107 解决通用设置，设置后，节点重叠问题 ([eef9546](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/eef9546b4bd1f15cdae8a2c7a58813a9aecff9ee))
* no.2023053107 解决通用设置，设置后，节点重叠问题 ([4413a45](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/4413a4595bf45f429a11d8efc58273809bd9af8e))
* no.2023053107 解决通用设置，设置后，节点重叠问题 ([aaf7e81](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/aaf7e81bcae63b537973964e28f7e8944424eaf5))
* no.2023053107 解决通用设置，设置后，节点重叠问题 ([14109de](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/14109de53048586609ae7ca8dcc4a15af593fb8d))
* no.2023053107 解决通用设置，设置后，节点重叠问题 ([8eed014](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/8eed0140325b7016895b0d59374ad65c09b3bb47))
* no.2592024 解决思维导图选了非eb的数据源，getObjFieldEntities会报错 ([e90c1fa](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/e90c1faff70ff627cf3447ecb76ed406d001396e))
* no.2592024 解决思维导图选了非eb的数据源，getObjFieldEntities会报错 ([c60b21a](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/c60b21afca2ca9d75c6b630059a0fdeef567278a))
* no.2632206 解决标准业务数据源筛选不生效 ([f3ace75](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/f3ace752f68368412726ea954afeb549ef914286))
* no.2661442 解决nodeFilter导致思维导图设计器报错临时处理 ([25fa0f0](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/25fa0f0b05830b7f771cb08f6923fa999a91944d))
* no.2661442 解决功能配置英文模式下样式问题 ([e25a0b8](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/e25a0b8a3c68b69c1fb61387104ea9498098f4ca))
* no.2661442 解决根节点自适应后添加图片显示问题 ([dac05ac](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/dac05ace2c510fb363d1b59b815a37b545d253b6))
* no.2661442 解决自定义api规划的问题 ([73e7c41](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/73e7c4123981352477ebacfc95afd62c1859ff14))
* no.2661442 解决节点尺寸自适应问题 ([5cad18f](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/5cad18f81f377f8da4582893287e7388030a0225))
* no.2661442 解决节点样式设置国际化 ([8a95d9e](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/8a95d9e82031749fd151f7ed6a9827229e201f19))
* no.2661442 解决节点设置合并问题 ([895a597](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/895a59792ee94806c0d6ffafdb1db19a0f7902cd))
* no.2661442 解决节点设置配置折叠后下划线显示异常 ([62ff455](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/62ff45589cda086c63dc528bdfae7a7d47cc91bb))
* no.2661442 解决部分字段节点设置后报错 ([36a8c52](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/36a8c52d83409b48be71ccf4953e7d419d1a07cc))
* no.2661446 解决根节点样式设置不需要默认值的问题 ([34eec68](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/34eec68fc87dc3f55637f7eaa719bafbafcc3455))
* no.2729545 解决上级字段筛选优化 ([f7a1528](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/f7a15285daa408c7adce24b5dbc84b060c8039a7))
* no.2729545 解决容器大小兼容问题 ([f05506d](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/f05506da17f5a5bb397ee002656967867b2e8ee6))
* no.2729545 解决容器大小兼容问题 ([636285a](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/636285aed208359e48c947599f96d6a1bb1c85fa))
* no.2729545 解决思维导图大小窗口，监听定位问题 ([e687571](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/e6875718aebca6a7848d289c7c07972efa059c6a))
* no.2729545 解决思维导图大小窗口，监听定位问题 ([2498871](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/2498871d9614c7eab7b9a123b037b249aeb158c7))
* no.2729545 解决根节点点击事件不生效 ([8e02625](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/8e02625f36a8600714fe1e3877e6ae3c75ccb4e5))
* no.2729545 解决版本抛出和支持字段点击事件 ([bdcf5b1](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/bdcf5b1e396dca4db24f228c4ab9818a6d45db97))


### Features

* no.0 新增cardlayout报错兼容处理 ([34b96b0](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/34b96b095f6fa0b212ac9858035dc93950bce778))
* no.0 新增二级节点支持标准业务数据源 ([8ab786e](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/8ab786e1b6c4d9fa89c02ad1d8f8e16bbda2d2b4))
* no.0 新增思维导图功能设置排序功能处理 ([1bf757d](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/1bf757d525ac95acc9d181fc27d9edc6c6f9cf63))
* no.0 新增思维导图排序功能 ([45d4a82](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/45d4a82ab75d34227c204698d22fa46a026c6aef))
* no.0 新增思维导图排序功能 ([959baaa](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/959baaae1eaa746c4bb249eb3d2463be8efd75e1))
* no.0 新增思维导图排序功能样式优化 ([5ef988f](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/5ef988f7a2d644d60fad047a05d4e06985683e5d))
* no.0 新增思维导图排序功能样式优化 ([dca4b76](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/dca4b76d8d79a686fae9602bd6959d677cf2234a))
* no.0 新增思维导图支持配置鼠标移入背景色、边框色配置 ([a53cc12](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/a53cc127a49b6c25b317b1d023ef1bd53e44ffa9))
* no.0 新增排序代码暂存 ([5affa04](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/5affa04b49fef24748f7163013c8387b8326ab8c))
* no.0 新增数据源放开 ([9f16a5f](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/9f16a5fe41a7e7a7c057b9b61b08e1cb1d193210))
* no.0 新增数据源放开 ([544ac2b](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/544ac2bd2977c70c3da67e1895275dde105ed636))
* no.0 新增根节点图标宽高优化 ([cb4bf55](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/cb4bf554fab9de050a3a25761268e939d48dc0ac))
* no.0 新增根节点图标宽高优化 ([fe8526a](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/fe8526abf5a57b51d7a0f987c9b86be3f6ad8ed4))
* no.0 新增根节点支持图片配置与显示开关、实时生效等 ([a320743](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/a3207436e153aec9a50d50654634371b6725f917))
* no.0 新增节点mobx删除节点报错修复 ([d4223f3](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/d4223f3be205d23eb4b09396aa7038742fb7154e))
* no.0 新增节点mobx删除节点报错修复 ([10e6fb5](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/10e6fb5248edd71405860c287a1d1b293af11b4a))
* no.0 新增节点mobx报错处理 ([7926602](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/792660206a13e53cc8b79f9744112afa695d7277))
* no.0 新增节点排序优化 ([e6695f9](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/e6695f9e0c53062174240a778c2f17d301eaef50))
* no.0 新增节点排序优化 ([c104c4c](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/c104c4ccb1df0c69d69d4d3efd1d802a98dc5454))
* no.0 新增节点支持样式设置 ([ab1acbd](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/ab1acbdd9569b6c0a5e55a9ed3dd286b9b16c3aa))
* no.0 新增节点新增保存优化 ([916021f](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/916021ff8a41d05cf7387261b6f29769a41defb3))
* no.2023043946 新增思维导图支持显示隐藏根节点 ([24e7546](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/24e7546b4e1151a3df5c797e6bb00a3de90c12c6))
* no.2023043946 新增思维导图支持隐藏根节点 ([dcc5f1e](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/dcc5f1e94382c6f270463cedbcf50bc9e35dc424))
* no.2023043946 新增思维导图支持隐藏根节点 ([7a108ea](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/7a108ea4ad358648c7da467480f3226111480269))
* no.2023044636 新增思维导图支持节点尺寸设置 ([d9b5dfa](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/d9b5dfaaafa2ddc0379909aafc9c4d247a5f9b32))
* no.2023045034 新增思维导图支持自定义文本按钮 ([291a983](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/291a983978df0115a68f3dbb3dbc977be5647068))
* no.2023045034 新增思维导图支持自定义文本按钮 ([6407877](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/640787788629452e5a0236c08d72598ea5f1dc29))
* no.2023045034 新增思维导图支持自定义文本按钮 ([0e313d6](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/0e313d6b56e5b415613d1f6060df26b57226dc89))
* no.2023046224 修复思维导图调换二、三级节点位置后，数据显示无调整 ([2f396ce](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/2f396ce84a8c3e718cfa25a9575f08ed98e9cb10))
* no.2023046224 新增忽略eslintcache ([07c4d70](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/07c4d70fe1807333cc4ed7e374ae9cfbde36def1))
* no.2023046224 新增思维导图功能配置修复 ([69083b2](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/69083b2245c84c4eec1a8b0f61600fb44e69e772))
* no.2023046224 新增思维导图功能配置修复 ([8f6311e](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/8f6311edb65e4e8737d6e1b59b740102bc075e86))
* no.2023046224 新增思维导图同级节点拖拽 ([52e4afb](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/52e4afb4caa45b5b1c767b599a4f47465d88f47b))
* no.2023046224 新增思维导图调换二、三级节点位置后，数据显示无调整 ([6eecd31](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/6eecd314e56b03e8e06a1eaaf47df31a464f6443))
* no.2598096 新增思维导图组件文档书写 ([f6e0ddb](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/f6e0ddbcc5d8f2813d891f284eb2c18025bb27f2))
* no.2598096 新增思维导图组件文档书写 ([6290345](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/629034568e3f68bfd3de1962e2c670ee49b09a33))
* no.2661446 增加节点样式设置类型选择(通用  自定义) ([0e1bb8a](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/0e1bb8ac5ea243f96f34b2d26263ae0f35bdc1ed))
* no.2749911 新增思维导图-功能设置-全选勾选框和单个勾选框联动异常 ([0f28f40](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/0f28f401246b4df66adcf5786704b1db38976434))
* **xss:** no.2297527 新增【紧急】【e9 ([1244d31](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/1244d31aa5a1a71bc4eab8a71e2b217a43536973))
* **xss:** no.2297527 新增【紧急】【e9升e10】云端waf防护白名单-SQL条件设计器拦截修改 ([610b47e](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/610b47eac7ec2eb585d3692becbcdb6c1323913a))
* **xss:** no.2297527 新增【紧急】【e9升e10】云端waf防护白名单-SQL条件设计器拦截修改 ([7be7c39](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/7be7c397878634c6e7c329cfa391ef3eeb5a0946))


### Performance Improvements

* no.0 优化jsmind触发更新逻辑 ([11e68dd](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/11e68dd137440f57f7f5cc0124e9daa2828432fe))
* no.2023044022 优化默认“显示”根节点优化为“隐藏”根节点以兼容历史数据 ([ec6b53e](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/ec6b53e5a818efe3b30dedad6ddab186f2b0f381))
* no.2023044023 优化思维导图支持节点样式设置：包括通用设置/单独级别节点设置 ([89c6ef8](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/89c6ef81fd40c8a2548b0293047f564f6ab77a27))
* no.2023044023 优化思维导图支持节点样式设置：包括通用设置/单独级别节点设置 ([f4c186e](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/f4c186e5702393722e524144c354ab09704bfe08))
* no.2023044023 优化思维导图支持节点样式设置：包括通用设置/单独级别节点设置 ([698caa6](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/698caa62e575a615a23012dae97e1683d02ec7d9))
* no.2661446 优化导图节点样式设置 ([2e7cceb](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/2e7cceb9701a1be498e0ada9830367055d1023e3))
* no.2661446 优化导图节点样式设置 ([ee65640](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/ee656408603a5547a47a76c02ba06887e59060d8))
* no.2661446 优化导图节点样式设置 ([c783301](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/c7833016e5082e6e8b57f3f9dccb87d33493475b))
* no.2661446 优化导图节点样式设置 ([c5573e3](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/c5573e3755874f2bea492056182f954495d65209))
* no.2700080  优化看板 思维导图 显示字段、条件设计器、动作屏蔽标签字段（自建） ([01a9482](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/01a948255318e47b11430e2c8ef4a5e94c443d81))
* no.2700080 优化看板 思维导图 显示字段、条件设计器、动作屏蔽标签字段（自建） ([a0612f0](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/a0612f033e93e29d6c56b8d0f253e02dea8f6fe2))



## [0.1.2](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/compare/v0.1.1...v0.1.2) (2023-06-17)


### Features

* **xss:** no.2297527 新增【紧急】【e9 ([efcc2ad](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/efcc2ad0a9bdcc0ff308e4ea0b57f03e182dd448))
* **xss:** no.2297527 新增【紧急】【e9升e10】云端waf防护白名单-SQL条件设计器拦截修改 ([b4f793f](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/b4f793f6fe50e19e936f52b5fc24ddfc29c5a68e))
* **xss:** no.2297527 新增【紧急】【e9升e10】云端waf防护白名单-SQL条件设计器拦截修改 ([3404840](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/3404840f5263e0c3b4b1f6de9ade36c6b35cc712))



## 0.1.1 (2023-06-16)


### Bug Fixes

* no.0 修复更新grid为空判断 ([15d57de](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/15d57ded12c2a8b100cccbfb7a0f0b789f1521f3))
* no.2201524 修复思维导图标题开启后会显示两层标题 ([66c10ac](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/66c10aced5d6d90249a6d8b771be691b083c8b64))


### Features

* no.0 新增merge devlop ([addb372](http://10.12.101.12/FRONTEND/weapp-ebdmindmap/commits/addb37272bd0e6d6fe40fd8c847613ec4604cc9e))



