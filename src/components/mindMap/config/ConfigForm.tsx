import React, { createContext } from 'react';
import { Form, FormStore, FormSwitchProps, FormLayoutProps, FormLayoutType, FormItemProps, FormDatas, CorsComponent } from '@weapp/ui';
import { cloneDeep } from '@weapp/utils';
import { PageModuleType, PageLayoutType, LayoutType } from '@weapp/ebdcoms';
import { GroupProps } from '@weapp/ui/lib/components/form/types';

export const ServicePathContext = createContext<any>(null);
export const ServicePathProvider = ServicePathContext.Provider;

export interface ConfigFormProps {
  getGroups?: any;
}

const customCollapsePanelProps = (groupItem: GroupProps) => ({
  title: <CorsComponent weId={`${groupItem.id || ''}_75md2h`} app='@weapp/ebdcoms' compName='Collapse' className='ebcoms-collapse' title={<span title={groupItem.title}>{groupItem.title}</span>} visible={groupItem.visible} />,
});
const getItem = (_items: FormItemProps): FormItemProps => {
  // 深拷贝，防止污染组件config里的初始数据
  _items = cloneDeep(_items);
  Object.keys(_items).forEach((key: string) => {
    _items[key].__customRender__ = _items[key].customRender;
    // customRender在@weapp/ui Form中已废弃
    delete _items[key].customRender;
  });
  return { ..._items };
};

// type CustomRenderComs = { [key: string]: any };

export class ConfigForm extends React.PureComponent<ConfigFormProps & any> {
  formStore: FormStore = new FormStore();

  componentDidMount() {
    // 存放自定义渲染组件Swicth组件的实例
    const { data, items, layout, rootProps, groups } = this.props;
    const { nodeTreeInfo } = rootProps.config;
    const formData = { ...nodeTreeInfo[data.id], isHideRoot: data.isHideRoot };
    this.formStore.initForm({
      data: formData,
      items: getItem(items),
      layout,
      groups: this.getGroups(formData),
    });
  }
  getGroups = (formData: any) => {
    const { groups, getGroups } = this.props;
    return getGroups ? getGroups(formData) : groups;
  };

  onDataChange = (value?: FormDatas) => {
    const { data, rootProps } = this.props;
    const { config, onConfigChange } = rootProps;
    const { nodeTreeInfo } = config;
    const keyName = data.id;
    const oldProps = nodeTreeInfo[keyName] || {};
    if (Object.keys(value as any)[0] === 'isHideRoot') {
      // 是否为根节点更新到全局config下
      onConfigChange(value);
    } else {
      const newValues = {
        nodeTreeInfo: {
          ...nodeTreeInfo,
          [keyName]: {
            ...oldProps,
            ...value,
          },
        },
      };
      onConfigChange(newValues);
    }
    this.props.onChange(value);
  };

  customRenderFormSwitch = () => (id: string, formProps: FormSwitchProps) => {
    const { pageId, appid, event, client, data, pageScope, layoutInfo, store, comId, onComChange, objId, rootProps } = this.props;
    const CustomCom: any = this.formStore.items[id]?.__customRender__;
    const configData = { ...data, ...(rootProps.config.nodeTreeInfo[data.id] || {}) };
    if (CustomCom) {
      const comProps = {
        // 设计器优化后，value取到的值不是最新的
        ...(formProps as any).props,
        value: configData[id],
      };
      const allProps = {
        key: id,
        ...comProps,
        event,
        rootProps,
        form: this.formStore,
        pageId,
        appid,
        pageScope,
        layoutInfo,
        client,
        store,
        comId,
        objId,
        config: configData,
        onConfigChange: this.onDataChange,
        onFormChange: this.onDataChange,
        onComChange,
      };
      return <CustomCom weId={`${this.props.weId || ''}_oh33t7`} {...allProps} />;
    }
    return null;
  };

  onCustomHide = (col: FormLayoutProps): FormLayoutProps => {
    const { customHide, client } = this.props;
    const isMobile = client === 'MOBILE';
    if (customHide) {
      col = customHide.call(this, col, isMobile);
    }
    return col;
  };

  customRenderLabel = (id: string, label?: string) => {
    const { items } = this.props;
    if (items[id]?.customRenderLabel) {
      return items[id]?.customRenderLabel(id, label);
    }
    return (
      <span className='ui-formItem-label-span' title={label}>
        {label}
      </span>
    );
  };

  render() {
    const { data, rootProps } = this.props;
    const { nodeTreeInfo } = rootProps.config;
    const formData = { ...nodeTreeInfo[data.id] };
    const { type } = data;
    return (
      <Form
        weId={`${this.props.weId || ''}_uvj8q0`}
        data={formData}
        store={this.formStore}
        className={`ebcoms-config-form ebcoms-config-${type}`}
        onChange={this.onDataChange}
        customHide={this.onCustomHide}
        customCollapsePanelProps={customCollapsePanelProps}
        customRenderFormSwitch={this.customRenderFormSwitch()}
        customRenderLabel={this.customRenderLabel}
        noLine
      />
    );
  }
}

export default function createForm(formProps: any) {
  class ComConfig extends React.PureComponent<any> {
    buildInKeys: string[] = ['title', 'titleEnabled', 'footerEnabled'];

    BUILDIN_GROUPID = 'build-in-group';

    getLayoutByItems() {
      return Object.keys(formProps.items).reduce((layout: FormLayoutType[], itemKey: string) => {
        layout.push([this.getLayoutItem(itemKey, formProps.items[itemKey])]);
        return layout;
      }, [] as FormLayoutType[]);
    }

    getLayoutItem(itemKey: string, _layoutItem: Partial<FormLayoutProps> = {}) {
      const item = formProps.items[itemKey];
      const { label, labelSpan = item.labelSpan, groupId, className = '', titleSpan, helpTip, layoutWrapClassName } = _layoutItem;
      const layoutItem: FormLayoutProps = {
        labelSpan,
        id: itemKey,
        items: [itemKey],
        hide: false, // 公共组件加了判断，不加该字段，组件无法显示
        label: label || item.label,
        className,
        titleSpan,
        helpTip,
        wrapClassName: layoutWrapClassName,
      };

      if (formProps?.groups) {
        const isBuildInKey = this.buildInKeys.indexOf(itemKey) > -1;

        layoutItem.groupId = isBuildInKey ? this.BUILDIN_GROUPID : groupId || this.BUILDIN_GROUPID;
      }

      if (layoutItem?.labelSpan === undefined) {
        if (item.itemType === 'SWITCH') {
          layoutItem.labelSpan = 20;
        } else if (itemKey === 'title') {
          layoutItem.labelSpan = 0;
        } else {
          // 默认上下结构
          layoutItem.labelSpan = 24;
        }
      }
      return layoutItem;
    }

    render() {
      const {
        data,
        page = {
          client: 'PC',
          id: '',
          module: PageModuleType.Eb,
          appid: '',
          layout: PageLayoutType.Default,
          layoutType: LayoutType.GRID,
          datasetVals: undefined,
        },
        comId,
        isChild = false,
        event = null,
        comServicePath = 'ebuilder/coms',
        client,
        onChange,
        onComChange,
        setConfigRef,
        nodeTreeInfo,
        onNodeTreeInfoChange,
      } = this.props;
      const { layout, ...formDatas } = data;
      const { client: _client, id, appid, module, layout: pageLayout, layoutType, datasetVals } = page;
      const layoutInfo = {
        pageLayout: pageLayout!,
        comLayoutScope: layout.scope,
        layoutType,
        datasetVals,
      };
      return (
        <ServicePathProvider weId={`${this.props.weId || ''}_j3x66w`} value={comServicePath}>
          <ConfigForm
            weId={`${this.props.weId || ''}_jqihtu`}
            title
            footer
            {...formProps}
            rootProps={page}
            layout={this.getLayoutByItems()}
            setConfigRef={setConfigRef}
            client={client || _client}
            event={event}
            pageId={id}
            appid={appid}
            objId={page?.config?.objId}
            pageScope={module}
            layoutInfo={layoutInfo}
            isChild={isChild}
            comId={comId}
            data={formDatas}
            onChange={onChange}
            onComChange={onComChange}
            nodeTreeInfo={nodeTreeInfo}
            onNodeTreeInfoChange={onNodeTreeInfoChange}
          />
        </ServicePathProvider>
      );
    }
  }

  return ComConfig as any;
}
