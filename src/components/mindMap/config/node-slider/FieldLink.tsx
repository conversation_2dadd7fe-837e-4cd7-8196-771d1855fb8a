import { Select, SelectOptionData } from '@weapp/ui';
import { observer } from 'mobx-react';
import React from 'react';
// import { BrowserType, EtComponentKey } from '../../../../../constants/EtComponent';
import { FormFieldItem } from '../../../../types/common';
import { isEbRelationBrowser, isEteams } from '../../../../utils';
import { BrowserType, EtComponentKey } from '../../../common/constants/EtComponent';
import { NodeNames } from '../../engine/node-config/constants';
import sliderStore from './store';

@observer
export default class FieldLink extends React.PureComponent<any, any> {
  constructor(props: any) {
    super(props);
    this.state = {
      supnodefields: [],
    };
  }

  componentDidMount() {
    const { id, rootProps, config } = this.props;
    if (id === NodeNames.Supnodefield) {
      const nodeInfo = this.getNodeInfo(rootProps, config.pid);
      this.setNodeFields(nodeInfo);
    }
  }

  componentDidUpdate(preProps: any) {
    const { id, rootProps, config } = this.props;
    const { pid } = config;
    if (id === NodeNames.Supnodefield && pid !== preProps.config.pid) {
      const nodeInfo = this.getNodeInfo(rootProps, pid);
      this.setNodeFields(nodeInfo);
    }
  }

  getNodeInfo = (rootProps: any, nodeId: string) => {
    const info = rootProps?.config?.nodeTreeInfo;
    if (!(info && info[nodeId])) return null;
    return info[nodeId];
  };

  setNodeFields = async (nodeInfo: any) => {
    if (!nodeInfo || !nodeInfo?.dataset?.id) return;
    const { dataset } = nodeInfo;
    let fields = await sliderStore.getFormFieldData(dataset);
    fields = sliderStore.getOptionsByComponentKey(fields, (f: FormFieldItem) => {
      if (`${f.multiSelect}` !== 'true' || isEteams(dataset)) {
        if (f.fieldName === 'id' || f.fieldName === 'name') {
          // id,标题
          return true;
        }
        if ([
          EtComponentKey.Text,
          EtComponentKey.TextArea,
          EtComponentKey.Employee,
          EtComponentKey.Creator,
          EtComponentKey.Updater,
          EtComponentKey.Department,
          EtComponentKey.Subcompany,
          EtComponentKey.Number,
        ].includes(f.componentKey)) {
          return true;
        }
        if (isEbRelationBrowser(f)
        ) {
          return true;
        } if (BrowserType.includes(f.componentKey)) {
          return true;
        }
      }
      return false;
    });
    this.setState({ supnodefields: fields });
  };

  render() {
    const { supnodefields } = this.state;
    const { onChange, value, id } = this.props;
    let options: SelectOptionData[] = [];
    if (id === NodeNames.Curnodefield) {
      options = sliderStore.ebuilderField;
    } else if (id === NodeNames.Supnodefield) {
      options = supnodefields;
    } else if (id === NodeNames.SupField) {
      options = sliderStore.SupFieldField;
    }
    return (
      <Select
        weId={`${this.props.weId || ''}_zxm1ne`}
        data={options}
        value={value}
        onSelect={onChange}
      />
    );
  }
}
