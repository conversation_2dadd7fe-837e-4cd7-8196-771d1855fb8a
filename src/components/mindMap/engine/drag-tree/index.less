.tree-box {
  height: 100%;
  width: 100%;
  border: 2px solid transparent;
  padding: var(--h-spacing-lg);
  overflow: auto;
  display: flex;
  align-items: center;
  .mindmap-ds-tree-box {
    white-space: nowrap;
    padding-right: 3px;
    display: flex;
    align-items: center;
  }
}

.mindmap-ds-tree-item {
  position: relative;
  height: 38px;
  line-height: 37px;
  width: 160px;
  margin-bottom: 40px;
  border: 1px solid #dedede;
  color: #666;
  border-radius: 2px;
  .rootdisable-box {
    background: var(--base-white);
  }
  h6 {
    border-left: 2px solid transparent;
    padding: 0 10px 0 10px;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    font-weight: normal;
    cursor: pointer;
    // cursor: -webkit-grab;
    margin: 0;
    height: 36px;
    font-size: var(--font-size-12);
  }
  /*操作按钮样式*/

  .mindmap-ds-treenode-operate-add,
  .mindmap-ds-treenode-operate-del {
    position: absolute;
    right: -25px;
    top: 0px;
    font-size: var(--font-size-md);
    z-index: 1;
    cursor: pointer;

    svg {
      width: 20px;
      height: 20px;
    }
  }
  .mindmap-ds-treenode-operate-del {
    right: -45px;
  }
}

.mindmap-ds-tree-wrap { //父节点后的横线
  position: relative;
  &:before {
    position: absolute;
    top: calc(50% - 20px);
    left: 30px;
    width: 21px;
    border-top: 1px solid var(--border-color);
    content: '';
    height: 10px;
  }
}

.mindmap-ds-tree-wrap {
  overflow: hidden;
  margin-left: -2px;
  padding-right: 30px;
  .mindmap-ds-tree-box {
    margin-left: 95px;
    &:first-child > .mindmap-ds-tree-item {
      &:before {
        position: absolute;
        z-index: 200;
        top: -977px;
        left: -46px;
        width: 1px;
        height: 1000px;
        background: var(--bg-base);
        content: '';
      }

      &:after {
        //node的最后一个向下弯曲
        position: absolute;
        top: 18px;
        left: -46px;
        width: 44px;
        border-top: 1px solid var(--border-color);
        content: '';
        height: 10px;
        border-top-left-radius: 10px;
      }
    }

    & + .mindmap-ds-tree-box > .mindmap-ds-tree-item {
      &:before {
        position: absolute;
        left: -46px;
        top: -10000px;
        bottom: 25px;
        border-left: 1px solid var(--border-color);
        content: '';
      }

      &:after {
        //node的中间直线
        position: absolute;
        top: 8px;
        left: -46px;
        width: 44px;
        height: 10px;
        border: 1px solid var(--border-color);
        border-width: 0 0 1px 1px;
        // border-bottom-left-radius: 10px;
        content: '';
      }
    }
    &:last-child > .mindmap-ds-tree-item {
      //node的最后一个向上弯曲
      &:after {
        border-bottom-left-radius: 10px;
      }
    }
  }
  /***覆盖，只有一个子节点的时候，前面的线不弯曲，直线
  */
  .mindmap-ds-tree-box-isone .mindmap-ds-tree-item {
    &:after {
      position: absolute !important;
      top: 18px !important;
      left: -46px !important;
      width: 44px !important;
      height: 1px !important;
      border: 1px solid var(--border-color) !important;
      border-width: 0 0 1px 1px !important;
      content: '';
    }
  }
}

.mindmap-ds-tree-wrap,
.mindmap-ds-tree-item {
  position: relative;
  display: inline-block;
  vertical-align: top;
}

//选中高亮
.mindmap-ds-active {
  border: 1px solid var(--border-color-active);
  h6 {
    color: var(--primary);
  }
}
