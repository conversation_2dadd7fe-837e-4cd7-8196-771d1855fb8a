import { observable, action, runInAction, computed, toJS } from 'mobx';
import { FormStore, FormItemProps, FormLayoutType, FormDatas } from '@weapp/ui';
import { getLabel, isEmpty } from '@weapp/utils';
import { INIT_NODE_STYLE } from '../../../../../constants';
import { MIND_LINE_STYLE } from '../../../../../constants/enum';

const STYLE_FORM_LAYOUT: FormLayoutType[] = [[{ id: 'styleType', label: `${getLabel('53872', '类型')}：`, labelSpan: 4, items: ['styleType'], hide: false }]];

const FORM_LAYOUT = (isRoot = false, lineStyle: MIND_LINE_STYLE): FormLayoutType[] => {
  let layout: FormLayoutType[] = [
    [{ id: 'background', label: `${getLabel('54606', '背景色')}：`, labelSpan: 10, items: ['background'], hide: false }],
    [{ id: 'mouseEnterBackground', label: `${getLabel('247792', '鼠标滑过背景色')}：`, labelSpan: 10, items: ['mouseEnterBackground'], hide: false }],
    [{ id: 'borderColor', label: `${getLabel('124295', '边框颜色')}：`, labelSpan: 10, items: ['borderColor'], hide: false }],
    [{ id: 'mouseEnterBorderColor', label: `${getLabel('247793', '鼠标滑过边框色')}：`, labelSpan: 10, items: ['mouseEnterBorderColor'], hide: false }],
  ];
  if (isRoot) {
    let showLine = false
    if (lineStyle === MIND_LINE_STYLE.CURVE) {
      showLine = true
    }
    if (showLine) {
      layout.push([{ id: 'lineColor', label: `${getLabel('147333', '线条颜色')}：`, labelSpan: 10, items: ['lineColor'], hide: false }], [{ id: 'expandColor', label: `${getLabel('249240', '展开收缩图标颜色')}：`, labelSpan: 10, items: ['expandColor'], hide: false }]);
    }
  } else {
    layout.push([{ id: 'lineColor', label: `${getLabel('147333', '线条颜色')}：`, labelSpan: 10, items: ['lineColor'], hide: false }], [{ id: 'expandColor', label: `${getLabel('249240', '展开收缩图标颜色')}：`, labelSpan: 10, items: ['expandColor'], hide: false }]);
  }
  return layout;
};

const STYLE_FORM_ITEMS = (): FormItemProps => ({
  styleType: {
    itemType: 'RADIO',
    data: [
      { id: 'common', content: getLabel('56756', '通用') },
      { id: 'custom', content: getLabel('54002', '自定义') },
    ],
  },
});

const FORM_ITEMS = (isRoot = false) => {
  let item: FormItemProps = {
    background: {
      itemType: 'COLORPICKER',
      // otherParams: {
      //     onBeforeChange: (val: any) => {
      //         //   return ''
      //       return '#ffffff'
      //     }
      //   }
    },
    mouseEnterBackground: {
      itemType: 'COLORPICKER',
    },
    borderColor: {
      itemType: 'COLORPICKER',
    },
    mouseEnterBorderColor: {
      itemType: 'COLORPICKER',
    },
    lineColor: { itemType: 'COLORPICKER' },
    expandColor: { itemType: 'COLORPICKER' },
  };
  // if (!isRoot) {
  //   item = {...item,
  //     'lineColor': { itemType: 'COLORPICKER'}
  //   }
  // }
  return item;
};

export class NodeStyleStore {
  @observable node = new FormStore();
  @observable formStore = new FormStore();
  @observable loading = false;
  @observable isRoot: boolean = false;
  @observable isCommon: boolean = true;
  @observable lineStyle: MIND_LINE_STYLE = MIND_LINE_STYLE.CURVE;

  getFormItem = () => {
    if (!this.isCommon) return { ...STYLE_FORM_ITEMS(), ...FORM_ITEMS(this.isRoot) };
    return FORM_ITEMS();
  };
  getFormLayout = () => {
    if (!this.isCommon) return [...STYLE_FORM_LAYOUT, ...FORM_LAYOUT(this.isRoot, this.lineStyle)];
    return FORM_LAYOUT(this.isRoot, this.lineStyle);
  };
  // 节点类型 根节点或者指定节点或者通用节点
  @computed get nodeType() {
    return 'common';
  }
  @computed get formData() {
    let data = this.formStore.getFormDatas();
    return data;
  }
  @action init = (data?: any, isRoot = false, isCommon?: boolean, lineStyle?: MIND_LINE_STYLE) => {
    runInAction(() => {
      this.isRoot = isRoot;
      this.isCommon = !!isCommon;
      this.lineStyle = lineStyle || MIND_LINE_STYLE.CURVE;
    });
    this.initForm(() => {
      if (!isEmpty(data)) {
        this.updatePropsData(data);
      }
    });
  };
  @action initForm = (cb?: Function) => {
    this.formStore.initForm({
      layout: this.getFormLayout(),
      items: this.getFormItem(),
      data: INIT_NODE_STYLE(),
      groups: [],
    });
    this.updateLayout();
    cb && cb();
  };
  updateLayout = () => {
    const formData = this.formStore.getFormDatas();
    if (this.isCommon) {
      // 通用设置生效
      this.formStore.setLayoutProps('styleType', { hide: true });
    } else {
      // 子节点生效
      const isHide = formData.styleType === 'common';
      Object.keys(FORM_ITEMS(this.isRoot)).map((key) => {
        this.formStore.setLayoutProps(key, { hide: isHide });
      });
      if (isHide) return;
    }
  };
  omFormChange = (value?: FormDatas, otherParams?: any) => {
    // 处理联动
    runInAction(() => {
      // 处理联动
      this.updateLayout();
    });
  };
  @action reset = () => {
    this.formStore = new FormStore();
    this.node = new FormStore();
    this.isCommon = false;
    this.loading = false;
    this.isRoot = false;
  };
  @action updatePropsData = (data: any = {}) => {
    const { background, borderColor, mouseEnterBackground, mouseEnterBorderColor, lineColor, expandColor, styleType = 'common' } = data;
    const value = {
      styleType,
      background,
      borderColor,
      mouseEnterBackground,
      mouseEnterBorderColor,
      lineColor,
      expandColor,
    };
    this.formStore.updateDatas(value);
    setTimeout(() => {
      this.updateLayout();
    }, 0);
  };
}

const instance = new NodeStyleStore();
export type NodeStyleStoreType = Pick<NodeStyleStore, keyof NodeStyleStore>;
export default instance;
