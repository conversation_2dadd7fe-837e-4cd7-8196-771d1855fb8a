import React from 'react';
import LSComponentPanel from '../ComponentPanel/Content';

const isHidden = false;
if (isHidden) {
  console.log(React);
}

export const config = {
  dataset: {
    id: '797342111581413376',
    type: 'FORM',
    groupId: '608763759724576768',
    physical: false,
  },
  setting: {
    name: '111',
    timeUnit: '1',
    primaryField: '5',
    rootNode: '',
    showNameField: '797342137296756737',
    parentField: '797342317542776834',
    progressField: '797342137296756739',
    prevField: '803269106312994816',
    scheduleDateStartField: '797342317542776836',
    scheduleTimeStartField: '798365370325385218',
    scheduleDateEndField: '797342506550632449',
    scheduleTimeEndField: '798365370325385218',
    realDateStartField: '797342265986392066',
    realTimeStartField: '798365370325385218',
    realDateEndField: '797342317542776835',
    ganttOccupyList: [
      {
        id: 'temp_b98e3b82d64c49229cf8e08ea1c425ae',
        category: '1',
        iconName: '计划',
        color: '#b9a6a6',
      },
      {
        id: 'temp_848097abd1594fa6ba0e3e61bb195d62',
        category: '1',
        iconName: '实际',
        color: '#ce3a3a',
      },
      {
        id: 'temp_7215a41bb3104ab38fcf92cdf1111198',
        category: '1',
        iconName: '111',
        color: '#d59090',
        filter: {},
      },
      {
        id: 'temp_3210e814d85644869092edd0d4a895ab',
        category: '2',
        iconName: '22',
        color: '#F6EA00',
        filter: {},
      },
    ],
  },
  fields: [],
  filter: {},
  orders: [],
  condition: {},
  button: {},
  convert: {},
  layout: {
    hidden: false,
    wrapped: false,
    selected: true,
  },
  package: '@weapp/ebdfpage',
  titleEnabled: false,
  styles: [
    {
      customStyle: {
        container: {
          padding: '0px 16px 0px 0px',
          margin: '0px 0px 0px 0px',
          backgroundColor: '#f6f6f6',
        },
      },
      useCustom: true,
      id: '7',
      category: '7',
    },
    {
      customStyle: {
        title: {
          background: {
            imgUrl: '',
            backgroundColor: 'transparent',
            positionType: 'grid',
            backgroundImage: '',
            backgroundSize: 'auto',
            backgroundPosition: 'center center',
            backgroundRepeat: 'no-repeat',
          },
        },
      },
      useCustom: true,
      id: '',
      category: '1',
    },
  ],
  showLunar: true,
  flow: {
    parent: 'ROOT',
    nodes: [],
    isCanvas: false,
    linkedNodes: {},
    nodeId: '9RYRgNhBhi',
  },
  footerEnabled: false,
  objId: '797342111581413376',
  fromEbuilder: true,
  type: 'Gantt',
  canDelete: false,
  field: [
    {
      conditionTypeValue: 'like',
      formInfo: {
        fieldId: '797342137296756737',
      },
      mainField: true,
      deleteType: false,
      type: 'String',
      lazyLoad: false,
      compTypec: '',
      compType: 'Text',
      placeholderMark: false,
      objId: '797342111581413376',
      name: '797342137296756737',
      richEditor: false,
      conditionType: [
        {
          id: 'like',
          content: '包含',
          disabled: false,
        },
        {
          id: 'notLike',
          content: '不包含',
          disabled: false,
        },
        {
          id: 'eq',
          content: '等于',
          disabled: false,
        },
        {
          id: 'notEq',
          content: '不等于',
          disabled: false,
        },
        {
          id: 'isNull',
          content: '为空',
          disabled: false,
        },
        {
          id: 'isNotNull',
          content: '不为空',
          disabled: false,
        },
      ],
      id: '797342137296756737',
      text: '是什么名字',
      mainPrimaryKey: false,
      multiSelect: 'false',
      objName: '',
      orderType: 'DEFAULT',
      width: 100,
      uid: 'f967ada14eba464d924f3cb19851dc0e',
      ganttFieldSetting: {
        alignment: 'left',
        showName: '是什么名字',
        showTrans: [
          {
            ruleShow: {
              ruleTitle: '不等于“”/显示',
              ruleContent:
                "%3Cspan%20style='display:%20inline-block;height:100%25;padding:%200%2010px;line-height:%2016px;border-radius:%202px;border:%201px%20solid%20#0099ff;background:%20#0099ff;color:%20#ffffff'%3E%E6%96%87%E5%AD%97%3C/span%3E",
            },
            index: 0,
            ruleConfig: {
              ruleType: 'highlight',
              ruleConditionType: 'simple',
              ruleCondition: 'neq',
              ruleConditionValue: '',
              showFormatType: 'type1',
              highlightPredefined: {
                borderColor: '#0099ff',
                fontColor: '#ffffff',
                background: '#0099ff',
              },
              highlightCustom: {
                fontFamily: '宋体',
                fontSize: '14',
                bold: false,
                italic: false,
                underline: false,
                backColor: '',
                foreColor: '',
              },
              optionFormat: [],
              optionBorderFillet: 0,
              valueFormat: {
                type: 'none',
                isThousandth: false,
              },
              avatarSize: 'small',
              isShowUserName: false,
              isShowDeptName: false,
              valueFormatUnit: 'none',
              valueFormatSuffix: '',
              dataBarMinValue: {
                type: 'percentage',
                value: '0',
              },
              dataBarMaxValue: {
                type: 'percentage',
                value: '0',
              },
              dataBarFillMethod: 'gradual',
              dataBarFillPosition: 'complete',
              dataBarPositiveColor: '#29c287',
              dataBarNegativeColor: '#d60e27',
              dataBarIsShowOnly: false,
              iconSetStyle: '',
              iconSetInfo: [],
              iconSetCondition: 'percentage',
              iconSetIsShowOnly: false,
              iconName: '',
              assetsItemData: {
                path: '#icon-14-o',
                style: {
                  bgColor: '#65C8D0',
                  borderColor: '#65C8D0',
                  color: '#ffffff',
                  fillType: 'backgroundColor',
                  fontSize: 36,
                  radius: '3px',
                },
              },
              iconColor: '',
              iconFillInfo: {
                iconFillType: '',
                iconFillColor: '',
              },
              iconShowOnly: false,
              colorScaleStyle: [],
              colorScaleMinValue: {
                valueType: 'number',
                value: '',
                color: '#ffffff',
              },
              colorScaleMaxValue: {
                valueType: 'number',
                value: '',
                color: '#ffffff',
              },
              tableType: 'interlaced',
              tableConditionRowBackColor: '#ffffff',
              tableConditionRowStyle: {
                fontFamily: '宋体',
                fontSize: '14',
                bold: false,
                italic: false,
                underline: false,
                backColor: '',
                foreColor: '',
                type: 'system',
              },
              tableOddRowBackColor: '#ffffff',
              tableOddRowStyle: {
                fontFamily: '宋体',
                fontSize: '14',
                bold: false,
                italic: false,
                underline: false,
                backColor: '',
                foreColor: '',
                type: 'system',
              },
              tableEvenRowBackColor: '#ffffff',
              tableEvenRowStyle: {
                fontFamily: '宋体',
                fontSize: '14',
                bold: false,
                italic: false,
                underline: false,
                backColor: '',
                foreColor: '',
                type: 'system',
              },
              prefix: '',
              suffix: '',
            },
          },
        ],
      },
    },
  ],
  searchConditions: {
    commonFilters: [],
    quickFilters: [],
    groupFilters: [],
    commonSearchType: '0',
  },
  order: [
    {
      canNotLink: false,
      fieldName: '实际开始日期',
      fieldType: 'Date',
      componentKey: 'DateComponent',
      fieldId: '797342265986392066',
      multiSelect: false,
      subFormId: '797342111581413376',
      fieldShowName: '实际开始日期',
      selected: false,
      orderType: 'desc',
      titleCls: '',
    },
  ],
};

/** 流式布局组件信息 */
export const mockLayoutDatas: any[] = [
  {
    id: '704875575821426689',
    createTime: 1647316634000,
    updateTime: 1673424420000,
    creator: '3572883652751288456',
    tenantKey: 't2k74i3rjo',
    deleteType: 0,
    pageId: '704875571645136897',
    layoutType: 'FLOW',
    terminalType: 'PC',
    thumbnail: '816909965295566849',
    comps: [
      {
        id: 'd4d85a4bb19c43ccaaa82a7e3c8f9948',
        refCompId: 'da743a2636264689b9489b09364c02e9',
        type: 'Occupy',
        category: '4',
        refModule: '',
        tenantKey: 't2k74i3rjo',
        styleCategoryCompKey: 'BasicStyleConfig',
        styleCategoryPackage: '@weapp/ebdcoms',
        applyStyle: '1',
        lock: false,
        config: {
          type: 'Occupy',
          name: '占用视图',
          layout: {
            hidden: false,
            wrapped: false,
            selected: true,
          },
          title: '',
          titleEnabled: false,
          dataset: {
            id: '796888734774427648',
            text: 'wzh-数据源eb-物理-名称',
            type: 'FORM',
            groupId: '796529709720641537',
            isPhysical: true,
          },
          baseInfo: {
            id: '',
            objId: '',
            titlefieldid: '',
          },
          condition: {},
          defaultView: '4',
          weekStart: 1,
          startTimeRange: 8,
          endTimeRange: 24,
          resourceField: '796888983815266306',
          startDateTimeField: '796888859361878016',
          endDateTimeField: '796888859361878017',
          weekColorLog: '',
          weekShowField: '',
          promptInfo: {
            field: [
              {
                name: 'order_sort',
                text: '序号',
                content: '',
                type: 'text',
                overFlow: {
                  number: '',
                  type: 'line',
                },
                autoFocus: false,
                id: '-3',
                uid: 'c493cf89d8ac42c9ab2831465696f0e0',
              },
              {
                formInfo: {
                  fieldId: '2',
                },
                mainField: true,
                deleteType: false,
                type: 'Employee',
                lazyLoad: false,
                compType: 'Employee',
                placeholderMark: false,
                objId: '796888734774427648',
                name: 'creator',
                richEditor: false,
                text: '创建者',
                mainPrimaryKey: false,
                multiSelect: 'false',
                group: 'form',
                overFlow: {
                  number: '',
                  type: 'line',
                },
                autoFocus: false,
                id: '2',
                uid: 'd4e7979f315645149731ccfbf7af19ef',
              },
            ],
            cardLayout: {
              split: [
                {
                  index: 0,
                  setting: {
                    width: {
                      type: '',
                      value: '',
                    },
                  },
                  id: 'e11cf715f07046ba8157427064583d9c',
                  type: '0',
                },
              ],
              grid: [
                [
                  [
                    {
                      x: 0,
                      y: 0,
                      w: 1,
                      h: 1,
                      field: {
                        id: '-3',
                        uid: 'c493cf89d8ac42c9ab2831465696f0e0',
                        style: {},
                        width: {
                          type: '1',
                          value: '',
                          unit: 'px',
                        },
                        minWidth: '80',
                        showTrans: [],
                        align: 'left',
                        wordWrap: false,
                        padding: [0, 12, 0, 0],
                        image: {
                          showType: 'grid',
                          h: '100%',
                          w: '100%',
                        },
                        horAlign: '0',
                        content: '',
                        isTransAvatar: false,
                        overFlow: {
                          number: '',
                          type: 'line',
                        },
                      },
                      i: 'f5ae6c98347242c985c338da98c31e0c',
                    },
                    {
                      x: 1,
                      y: 0,
                      w: 1,
                      h: 1,
                      field: {
                        id: '2',
                        uid: 'd4e7979f315645149731ccfbf7af19ef',
                        style: {},
                        width: {
                          type: '1',
                          value: '',
                          unit: 'px',
                        },
                        minWidth: '80',
                        showTrans: [],
                        align: 'left',
                        wordWrap: false,
                        padding: [0, 12, 0, 0],
                        image: {
                          showType: 'grid',
                          h: '100%',
                          w: '100%',
                        },
                        horAlign: '0',
                        isTransAvatar: false,
                        overFlow: {
                          number: '',
                          type: 'line',
                        },
                      },
                      i: 'd7ecd2df0d4341a2af724cbf347f8b55',
                    },
                  ],
                ],
              ],
              size: [[4, 3]],
              row: [
                [
                  {
                    index: 0,
                    isHide: false,
                    setting: {
                      height: '',
                      alignItems: 'center',
                    },
                  },
                  {
                    index: 1,
                    isHide: false,
                    setting: {
                      height: '',
                      alignItems: 'center',
                    },
                  },
                  {
                    index: 2,
                    isHide: false,
                    setting: {
                      height: '',
                      alignItems: 'center',
                    },
                  },
                  {
                    index: 3,
                    isHide: false,
                    setting: {
                      height: '',
                      alignItems: 'center',
                    },
                  },
                ],
              ],
              extendArea: {
                id: 'fd1d92f5581c4bb3985fb4056e9f7608',
                display: 'none',
                type: 'current',
                pageMode: 'part',
                pageSize: 10,
              },
              shrinkDisplayType: 'single',
              shrinkRegionIndex: -1,
            },
            tableField: [
              {
                id: '-3',
                name: 'order_sort',
                text: '序号',
                content: '',
                type: 'text',
                style: {},
                showTrans: [],
                isTransAvatar: false,
                width: {
                  type: '1',
                  value: '',
                  unit: 'px',
                },
                wordWrap: false,
                horAlign: '0',
                align: 'left',
                minWidth: '80',
                padding: [0, 12, 0, 0],
                image: {
                  showType: 'grid',
                  h: '100%',
                  w: '100%',
                },
                overFlow: {
                  number: '',
                  type: 'line',
                },
                uid: 'c493cf89d8ac42c9ab2831465696f0e0',
                autoFocus: false,
              },
              {
                conditionTypeValue: 'in',
                formInfo: {
                  fieldId: '2',
                },
                mainField: true,
                deleteType: false,
                type: 'Employee',
                lazyLoad: false,
                compType: 'Employee',
                placeholderMark: false,
                objId: '796888734774427648',
                name: 'creator',
                richEditor: false,
                conditionType: [
                  {
                    id: 'in',
                    content: '属于',
                    disabled: false,
                  },
                  {
                    id: 'notIn',
                    content: '不属于',
                    disabled: false,
                  },
                  {
                    id: 'isNull',
                    content: '为空',
                    disabled: false,
                  },
                  {
                    id: 'isNotNull',
                    content: '不为空',
                    disabled: false,
                  },
                ],
                id: '2',
                text: '创建者',
                mainPrimaryKey: false,
                multiSelect: 'false',
                group: 'form',
                style: {},
                showTrans: [],
                isTransAvatar: false,
                width: {
                  type: '1',
                  value: '',
                  unit: 'px',
                },
                wordWrap: false,
                horAlign: '0',
                align: 'left',
                minWidth: '80',
                padding: [0, 12, 0, 0],
                image: {
                  showType: 'grid',
                  h: '100%',
                  w: '100%',
                },
                overFlow: {
                  number: '',
                  type: 'line',
                },
                uid: 'd4e7979f315645149731ccfbf7af19ef',
                autoFocus: false,
              },
            ],
          },
          detailedInfo: {
            field: [],
            cardLayout: {
              split: [
                {
                  index: 0,
                  setting: {
                    width: {
                      type: '',
                      value: '',
                    },
                  },
                  id: 'c6c6a2eec61f4aefa2738dc4ccff1163',
                  type: '0',
                },
              ],
              grid: [[[]]],
              size: [[4, 3]],
              row: [
                [
                  {
                    index: 0,
                    isHide: false,
                    setting: {
                      height: '',
                      alignItems: 'center',
                    },
                  },
                  {
                    index: 1,
                    isHide: false,
                    setting: {
                      height: '',
                      alignItems: 'center',
                    },
                  },
                  {
                    index: 2,
                    isHide: false,
                    setting: {
                      height: '',
                      alignItems: 'center',
                    },
                  },
                  {
                    index: 3,
                    isHide: false,
                    setting: {
                      height: '',
                      alignItems: 'center',
                    },
                  },
                ],
              ],
              extendArea: {
                id: 'ec26f294d3784d68ba9bc5d8884e6608',
                display: 'none',
                type: 'current',
                pageMode: 'part',
                pageSize: 10,
              },
              shrinkDisplayType: 'single',
              shrinkRegionIndex: -1,
            },
          },
          listShowFields:
            '[{"id":"-3","name":"order_sort","text":"序号","content":"","type":"Text","objName":"","orderType":"DEFAULT","width":100,"uid":"cfd44d7cfa704ec99e6001ef197f617d"},{"conditionTypeValue":"in","formInfo":{"fieldId":"2"},"mainField":true,"deleteType":false,"type":"Employee","lazyLoad":false,"compType":"Employee","placeholderMark":false,"objId":"796888734774427648","name":"creator","richEditor":false,"conditionType":[{"id":"in","content":"属于","disabled":false},{"id":"notIn","content":"不属于","disabled":false},{"id":"isNull","content":"为空","disabled":false},{"id":"isNotNull","content":"不为空","disabled":false}],"id":"2","text":"创建者","mainPrimaryKey":false,"multiSelect":"false","objName":"","orderType":"DEFAULT","width":100,"uid":"be379ae8d6ff46c99e8d129c335f0c15"},{"conditionTypeValue":"like","formInfo":{"fieldId":"1"},"mainField":true,"deleteType":false,"type":"String","lazyLoad":false,"compTypec":"","compType":"Text","placeholderMark":false,"objId":"796888734774427648","name":"name","richEditor":false,"conditionType":[{"id":"like","content":"包含","disabled":false},{"id":"notLike","content":"不包含","disabled":false},{"id":"eq","content":"等于","disabled":false},{"id":"notEq","content":"不等于","disabled":false},{"id":"isNull","content":"为空","disabled":false},{"id":"isNotNull","content":"不为空","disabled":false}],"id":"1","text":"标题","mainPrimaryKey":false,"multiSelect":"false","objName":"","orderType":"DEFAULT","width":100,"uid":"a640e6be2f474ea1935b5bc06990a132"},{"conditionTypeValue":"in","formInfo":{"fieldId":"8"},"mainField":true,"deleteType":false,"type":"Select","lazyLoad":false,"compType":"Select","placeholderMark":false,"objId":"796888734774427648","name":"data_status","richEditor":false,"conditionType":[{"id":"in","content":"属于","disabled":false},{"id":"notIn","content":"不属于","disabled":false},{"id":"isNull","content":"为空","disabled":false},{"id":"isNotNull","content":"不为空","disabled":false}],"id":"8","text":"数据状态","mainPrimaryKey":false,"multiSelect":"false","objName":"","orderType":"DEFAULT","width":100,"uid":"da78a8d9c4414e33ac238ff813054424"},{"conditionTypeValue":"in","formInfo":{"fieldId":"6"},"mainField":true,"deleteType":false,"type":"Employee","lazyLoad":false,"compType":"Employee","placeholderMark":false,"objId":"796888734774427648","name":"updater","richEditor":false,"conditionType":[{"id":"in","content":"属于","disabled":false},{"id":"notIn","content":"不属于","disabled":false},{"id":"isNull","content":"为空","disabled":false},{"id":"isNotNull","content":"不为空","disabled":false}],"id":"6","text":"最后更新人","mainPrimaryKey":false,"multiSelect":"false","objName":"","orderType":"DEFAULT","width":100,"uid":"f5c024beb6b44f0c90e12ede34c83a17"}]',
          filter: {},
          conflictColor: '#ff9c9d',
          usageColor: '#c6ddff',
          panelHeightRatio: 60,
          flow: {
            parent: 'ROOT',
            nodes: [],
            isCanvas: false,
            linkedNodes: {},
            nodeId: '_SidBp_ci2',
          },
          resourceFieldObjId: '796888734774427648',
          endTimeField: '',
          showBottomList: true,
          showTimeSlot: [8, 24],
          viewType: 'cardType',
          cardLayout: {
            field: [
              {
                name: 'order_sort',
                text: '序号',
                content: '',
                type: 'text',
                overFlow: {
                  number: '',
                  type: 'line',
                },
                autoFocus: false,
                id: '-3',
                uid: 'a79ded8969aa4c97a2d107913e5ebeb5',
              },
              {
                formInfo: {
                  fieldId: '2',
                },
                mainField: true,
                deleteType: false,
                type: 'Employee',
                lazyLoad: false,
                compType: 'Employee',
                placeholderMark: false,
                objId: '796888734774427648',
                name: 'creator',
                richEditor: false,
                text: '创建者',
                mainPrimaryKey: false,
                multiSelect: 'false',
                group: 'form',
                overFlow: {
                  number: '',
                  type: 'line',
                },
                autoFocus: false,
                id: '2',
                uid: 'e1bd86834b814646b83f8092690bce1d',
              },
              {
                formInfo: {
                  fieldId: '1',
                },
                mainField: true,
                deleteType: false,
                type: 'String',
                lazyLoad: false,
                compTypec: '',
                compType: 'Text',
                placeholderMark: false,
                objId: '796888734774427648',
                name: 'name',
                richEditor: false,
                text: '标题',
                mainPrimaryKey: false,
                multiSelect: 'false',
                group: 'form',
                overFlow: {
                  number: '',
                  type: 'line',
                },
                autoFocus: false,
                id: '1',
                uid: 'ab7804c706d24cf08a261546478612ed',
              },
            ],
            cardLayout: {
              split: [
                {
                  index: 0,
                  setting: {
                    width: {
                      type: '',
                      value: '',
                    },
                  },
                  id: 'df266386e4b342fe982555700aa32186',
                  type: '0',
                },
              ],
              grid: [
                [
                  [
                    {
                      x: 0,
                      y: 0,
                      w: 1,
                      h: 1,
                      field: {
                        id: '-3',
                        uid: 'a79ded8969aa4c97a2d107913e5ebeb5',
                        style: {},
                        width: {
                          type: '1',
                          value: '',
                          unit: 'px',
                        },
                        minWidth: '80',
                        showTrans: [],
                        align: 'left',
                        wordWrap: false,
                        padding: [0, 12, 0, 0],
                        image: {
                          showType: 'grid',
                          h: '100%',
                          w: '100%',
                        },
                        horAlign: '0',
                        content: '',
                        isTransAvatar: false,
                        overFlow: {
                          number: '',
                          type: 'line',
                        },
                      },
                      i: 'de490b6b403240a197b06ed06b73ffcb',
                    },
                    {
                      x: 1,
                      y: 0,
                      w: 1,
                      h: 1,
                      field: {
                        id: '2',
                        uid: 'e1bd86834b814646b83f8092690bce1d',
                        style: {},
                        width: {
                          type: '1',
                          value: '',
                          unit: 'px',
                        },
                        minWidth: '80',
                        showTrans: [],
                        align: 'left',
                        wordWrap: false,
                        padding: [0, 12, 0, 0],
                        image: {
                          showType: 'grid',
                          h: '100%',
                          w: '100%',
                        },
                        horAlign: '0',
                        isTransAvatar: false,
                        overFlow: {
                          number: '',
                          type: 'line',
                        },
                      },
                      i: 'bf05481543e74d2dbde8056c81dd22a4',
                    },
                    {
                      x: 2,
                      y: 0,
                      w: 1,
                      h: 1,
                      field: {
                        id: '1',
                        uid: 'ab7804c706d24cf08a261546478612ed',
                        style: {},
                        width: {
                          type: '1',
                          value: '',
                          unit: 'px',
                        },
                        minWidth: '80',
                        showTrans: [],
                        align: 'left',
                        wordWrap: false,
                        padding: [0, 12, 0, 0],
                        image: {
                          showType: 'grid',
                          h: '100%',
                          w: '100%',
                        },
                        horAlign: '0',
                        isTransAvatar: false,
                        overFlow: {
                          number: '',
                          type: 'line',
                        },
                      },
                      i: 'd31b758aa26f41879bf0083961c7e8d1',
                    },
                  ],
                ],
              ],
              size: [[4, 3]],
              row: [
                [
                  {
                    index: 0,
                    isHide: false,
                    setting: {
                      height: '',
                      alignItems: 'center',
                    },
                  },
                  {
                    index: 1,
                    isHide: false,
                    setting: {
                      height: '',
                      alignItems: 'center',
                    },
                  },
                  {
                    index: 2,
                    isHide: false,
                    setting: {
                      height: '',
                      alignItems: 'center',
                    },
                  },
                  {
                    index: 3,
                    isHide: false,
                    setting: {
                      height: '',
                      alignItems: 'center',
                    },
                  },
                ],
              ],
              extendArea: {
                id: 'b54e46deb4604bd7877f8e034d976468',
                display: 'none',
                type: 'current',
                pageMode: 'part',
                pageSize: 10,
              },
              shrinkDisplayType: 'single',
              shrinkRegionIndex: -1,
            },
            tableField: [
              {
                id: '-3',
                name: 'order_sort',
                text: '序号',
                content: '',
                type: 'text',
                style: {},
                showTrans: [],
                isTransAvatar: false,
                width: {
                  type: '1',
                  value: '',
                  unit: 'px',
                },
                wordWrap: false,
                horAlign: '0',
                align: 'left',
                minWidth: '80',
                padding: [0, 12, 0, 0],
                image: {
                  showType: 'grid',
                  h: '100%',
                  w: '100%',
                },
                overFlow: {
                  number: '',
                  type: 'line',
                },
                uid: 'a79ded8969aa4c97a2d107913e5ebeb5',
                autoFocus: false,
              },
              {
                conditionTypeValue: 'in',
                formInfo: {
                  fieldId: '2',
                },
                mainField: true,
                deleteType: false,
                type: 'Employee',
                lazyLoad: false,
                compType: 'Employee',
                placeholderMark: false,
                objId: '796888734774427648',
                name: 'creator',
                richEditor: false,
                conditionType: [
                  {
                    id: 'in',
                    content: '属于',
                    disabled: false,
                  },
                  {
                    id: 'notIn',
                    content: '不属于',
                    disabled: false,
                  },
                  {
                    id: 'isNull',
                    content: '为空',
                    disabled: false,
                  },
                  {
                    id: 'isNotNull',
                    content: '不为空',
                    disabled: false,
                  },
                ],
                id: '2',
                text: '创建者',
                mainPrimaryKey: false,
                multiSelect: 'false',
                group: 'form',
                style: {},
                showTrans: [],
                isTransAvatar: false,
                width: {
                  type: '1',
                  value: '',
                  unit: 'px',
                },
                wordWrap: false,
                horAlign: '0',
                align: 'left',
                minWidth: '80',
                padding: [0, 12, 0, 0],
                image: {
                  showType: 'grid',
                  h: '100%',
                  w: '100%',
                },
                overFlow: {
                  number: '',
                  type: 'line',
                },
                uid: 'e1bd86834b814646b83f8092690bce1d',
                autoFocus: false,
              },
              {
                conditionTypeValue: 'like',
                formInfo: {
                  fieldId: '1',
                },
                mainField: true,
                deleteType: false,
                type: 'String',
                lazyLoad: false,
                compTypec: '',
                compType: 'Text',
                placeholderMark: false,
                objId: '796888734774427648',
                name: 'name',
                richEditor: false,
                conditionType: [
                  {
                    id: 'like',
                    content: '包含',
                    disabled: false,
                  },
                  {
                    id: 'notLike',
                    content: '不包含',
                    disabled: false,
                  },
                  {
                    id: 'eq',
                    content: '等于',
                    disabled: false,
                  },
                  {
                    id: 'notEq',
                    content: '不等于',
                    disabled: false,
                  },
                  {
                    id: 'isNull',
                    content: '为空',
                    disabled: false,
                  },
                  {
                    id: 'isNotNull',
                    content: '不为空',
                    disabled: false,
                  },
                ],
                id: '1',
                text: '标题',
                mainPrimaryKey: false,
                multiSelect: 'false',
                group: 'form',
                style: {},
                showTrans: [],
                isTransAvatar: false,
                width: {
                  type: '1',
                  value: '',
                  unit: 'px',
                },
                wordWrap: false,
                horAlign: '0',
                align: 'left',
                minWidth: '80',
                padding: [0, 12, 0, 0],
                image: {
                  showType: 'grid',
                  h: '100%',
                  w: '100%',
                },
                overFlow: {
                  number: '',
                  type: 'line',
                },
                uid: 'ab7804c706d24cf08a261546478612ed',
                autoFocus: false,
              },
            ],
          },
          resourceImgField: '796888764746768384',
          resourceSelectField: '796888764746768385',
        },
        package: '@weapp/ebdfpage',
        isLock: false,
        isCustom: false,
      },
    ],
    ecodeId: '752102898903474177',
    rightCompIds: ['d4d85a4bb19c43ccaaa82a7e3c8f9948', 'da743a2636264689b9489b09364c02e9'],
    module: 'EB_PAGE',
    sourceCode: {
      ebcode:
        '%7B%22js%22:%7B%22content%22:%22%22,%22compiledContent%22:%22%5C%22use%20strict%5C%22;%22,%22name%22:%22index%22,%22state%22:%22n%22%7D,%22css%22:%7B%22content%22:%22.ebcom-pricepanel%20%5Cr%5Cn+%20.ebcom-prstateitempanel%20%5Cr%5Cn+%20.ebcom-prsystemusagepanel%20%5Cr%5Cn+%20.ebcom-prworkrecordpanel%20%5Cr%5Cn+%20.ebcom-prokrcompletionratepanel%20%5Cr%5Cn+%20.ebcom-prokrcompletionratepanel%5Cr%5Cn+%20.ebcom-prcrmpanel%5Cr%5Cn+%20.ebcom-prkpiscorepanel%5Cr%5Cn%7B%5Cr%5Cn%20%20%5Ctborder:%200.3px%20solid%20var(--border-color);%5Cr%5Cn%7D%5Cr%5Cn%5Cr%5Cn.weapp-de-paper-box.weapp-de-paper-box%20%7B%5Cr%5Cn%20%20overflow:%20hidden;%5Cr%5Cn%7D%5Cr%5Cn%5Cr%5Cn.ebcoms-tab-menu-top%7B%5Cr%5Cn%20%20%20padding:%200;%5Cr%5Cn%20%20%20height:%2040px;%5Cr%5Cn%20%20%20%5Cr%5Cn%7D%5Cr%5Cn.ui-menu-nav-container%20.ui-menu-nav-wrap%7B%5Cr%5Cn%20%20padding-left:%2016px;%5Cr%5Cn%20%20margin-bottom:%200%20!important;%5Cr%5Cn%20%20border-bottom:%200.3px%20solid%20var(--border-color)%20!important;%5Cr%5Cn%7D%5Cr%5Cn%5Cr%5Cn/*%20%E6%96%87%E5%AD%97Tab%E7%BB%84%E4%BB%B6%E9%AB%98%E5%BA%A6%E7%BB%9F%E4%B8%8040%20*/%5Cr%5Cn/*%20.ebcoms-tab-menu-top%20%7B%5Cr%5Cn%20%20height:%2040px;%5Cr%5Cn%5Cr%5Cn%7D%5Cr%5Cn.ebcoms-tab-menu-top%20%20.ui-menu-tab-top-container%7B%5Cr%5Cn%20%20%20%20height:%2040px;%5Cr%5Cn%20%20%20%20line-height:%2040px;%5Cr%5Cn%7D%20*/%5Cr%5Cn%5Cr%5Cn/*%20TabContent%E5%8C%BA%E5%9F%9F%E7%BB%99%E4%B8%80%E4%B8%AA%E9%80%82%E5%90%88%E7%9A%84%E9%AB%98%E5%BA%A6%20%E8%AE%A9%E5%85%B6%E6%BB%9A%E5%8A%A8%20*/%5Cr%5Cn#b9df5b04a2194a69aa11fe72e236fcd2%20%7B%5Cr%5Cn%20%20height:%20calc(100vh%20-%20150px);%5Cr%5Cn%20%20%20%20overflow-y:%20auto;%5Cr%5Cn%20%20%20%20background:%20#F7F7F7;%5Cr%5Cn%7D%5Cr%5Cn%5Cr%5Cn#db1b7dd393c24794bc0e9fd413f97692%20%7B%5Cr%5Cn%20%20height:%20calc(100vh%20-%20150px);%5Cr%5Cn%20%20overflow-y:%20auto%5Cr%5Cn%7D%5Cr%5Cn%5Cr%5Cn#a5a09a37f1ab4d8281d29ca14850dee2%20%7B%5Cr%5Cn%20%20height:%20calc(100vh%20-%20150px);%5Cr%5Cn%20%20overflow-y:%20auto%5Cr%5Cn%7D%5Cr%5Cn%5Cr%5Cn#df604d6baf46473bb8a99b2019b12c65%20%7B%5Cr%5Cn%20%20%20%20height:%20calc(100vh%20-%20150px);%5Cr%5Cn%20%20%20%20overflow-y:%20auto%5Cr%5Cn%7D%20%22,%22compiledContent%22:%22.ebcom-pricepanel%20%5Cr%5Cn+%20.ebcom-prstateitempanel%20%5Cr%5Cn+%20.ebcom-prsystemusagepanel%20%5Cr%5Cn+%20.ebcom-prworkrecordpanel%20%5Cr%5Cn+%20.ebcom-prokrcompletionratepanel%20%5Cr%5Cn+%20.ebcom-prokrcompletionratepanel%5Cr%5Cn+%20.ebcom-prcrmpanel%5Cr%5Cn+%20.ebcom-prkpiscorepanel%5Cr%5Cn%7B%5Cr%5Cn%20%20%5Ctborder:%200.3px%20solid%20var(--border-color);%5Cr%5Cn%7D%5Cr%5Cn%5Cr%5Cn.weapp-de-paper-box.weapp-de-paper-box%20%7B%5Cr%5Cn%20%20overflow:%20hidden;%5Cr%5Cn%7D%5Cr%5Cn%5Cr%5Cn.ebcoms-tab-menu-top%7B%5Cr%5Cn%20%20%20padding:%200;%5Cr%5Cn%20%20%20height:%2040px;%5Cr%5Cn%20%20%20%5Cr%5Cn%7D%5Cr%5Cn.ui-menu-nav-container%20.ui-menu-nav-wrap%7B%5Cr%5Cn%20%20padding-left:%2016px;%5Cr%5Cn%20%20margin-bottom:%200%20!important;%5Cr%5Cn%20%20border-bottom:%200.3px%20solid%20var(--border-color)%20!important;%5Cr%5Cn%7D%5Cr%5Cn%5Cr%5Cn/*%20%E6%96%87%E5%AD%97Tab%E7%BB%84%E4%BB%B6%E9%AB%98%E5%BA%A6%E7%BB%9F%E4%B8%8040%20*/%5Cr%5Cn/*%20.ebcoms-tab-menu-top%20%7B%5Cr%5Cn%20%20height:%2040px;%5Cr%5Cn%5Cr%5Cn%7D%5Cr%5Cn.ebcoms-tab-menu-top%20%20.ui-menu-tab-top-container%7B%5Cr%5Cn%20%20%20%20height:%2040px;%5Cr%5Cn%20%20%20%20line-height:%2040px;%5Cr%5Cn%7D%20*/%5Cr%5Cn%5Cr%5Cn/*%20TabContent%E5%8C%BA%E5%9F%9F%E7%BB%99%E4%B8%80%E4%B8%AA%E9%80%82%E5%90%88%E7%9A%84%E9%AB%98%E5%BA%A6%20%E8%AE%A9%E5%85%B6%E6%BB%9A%E5%8A%A8%20*/%5Cr%5Cn#b9df5b04a2194a69aa11fe72e236fcd2%20%7B%5Cr%5Cn%20%20height:%20calc(100vh%20-%20150px);%5Cr%5Cn%20%20%20%20overflow-y:%20auto;%5Cr%5Cn%20%20%20%20background:%20#F7F7F7;%5Cr%5Cn%7D%5Cr%5Cn%5Cr%5Cn#db1b7dd393c24794bc0e9fd413f97692%20%7B%5Cr%5Cn%20%20height:%20calc(100vh%20-%20150px);%5Cr%5Cn%20%20overflow-y:%20auto%5Cr%5Cn%7D%5Cr%5Cn%5Cr%5Cn#a5a09a37f1ab4d8281d29ca14850dee2%20%7B%5Cr%5Cn%20%20height:%20calc(100vh%20-%20150px);%5Cr%5Cn%20%20overflow-y:%20auto%5Cr%5Cn%7D%5Cr%5Cn%5Cr%5Cn#df604d6baf46473bb8a99b2019b12c65%20%7B%5Cr%5Cn%20%20%20%20height:%20calc(100vh%20-%20150px);%5Cr%5Cn%20%20%20%20overflow-y:%20auto%5Cr%5Cn%7D%20%22,%22name%22:%22index%22,%22state%22:%22n%22%7D%7D',
      ecode:
        "%7B%22js%22:%7B%22content%22:%22import%20%7B%20regOvProps%20%7D%20from%20'@weapp/utils';%5Cn%5Cn%20%20regOvProps('weappEBpage',%20'PageView',%20(props)%20=%3E%20%7B%5Cn%20%20%20%20if%20(704875571645136897%20==%20props.pageId)%20%7B%5Cn%20%20%20%20%20%20function%20ecodeMount()%20%7B%5Cn%20%20%20%20%20%20%20%20%5Cn%20%20%20%20%20%20%7D%5Cn%20%20%20%20%5Cn%20%20%20%20%20%20return%20%7B%5Cn%20%20%20%20%20%20%20%20...props,%5Cn%20%20%20%20%20%20%20%20ecodeMount,%5Cn%20%20%20%20%20%20%7D%5Cn%20%20%20%20%7D%20else%20%7B%5Cn%20%20%20%20%20%20return%20props;%5Cn%20%20%20%20%7D%5Cn%20%20%7D,%200);%22,%22compiledContent%22:%22(function%20()%20%7B%5Cn%20%20%20%20%20%20%5C%22use%20strict%5C%22;%5Cn%20%20%20%20%20%20window.weappEcodesdk.jsonp.export(%5C%22$%7BappId%7D%5C%22,%20%5C%22index.js%5C%22,%20function%20()%20%7B%5Cn%20%20%20%20%20%20%20%20var%20exports%20=%20%7B%7D;%5Cn%20%20%20%20%20%20%20%20var%20require%20=%20window.weappEcodesdk.jsonp.require.bind(null,%20%5C%22$%7BappId%7D%5C%22,%20%5C%22index.js%5C%22);%5Cn%20%20%20%20%20%20%20%20%5Cn%20%20%20%20%20%20%20%20Object.defineProperty(exports,%20%5C%22__esModule%5C%22,%20%7B%5Cn%20%20%20%20%20%20%20%20%20%20value:%20true%5Cn%20%20%20%20%20%20%20%20%7D);%5Cn%5Cn%20%20%20%20%20%20%20%20exports.load%20=%20void%200;%5Cn%5Cn%20%20%20%20%20%20%20%20var%20load%20=%20function()%20%7B%5Cn%20%20%20%20%20%20%20%20%20%20%20var%20ebSdk%20=%20window.ebuilderSDK;%20//%E5%85%A8%E5%B1%80SDK%5Cn%20%20%20%20%20%20%20%20%20%20%20var%20pageSdk%20=%20window.ebuilderSDK?.getPageSDK();%20//%E5%BD%93%E5%89%8D%E9%A1%B5%E9%9D%A2SDK%20%5Cn%20%20%20%20%20%20%20%20%20%20%5C%22use%20strict%5C%22;%5Cn%20%20%20%20%20%20%20%20%7D;%5Cn%5Cn%20%20%20%20%20%20%20%20exports.load%20=%20load;%5Cn%5Cn%20%20%20%20%20%20%20%20return%20exports;%5Cn%20%20%20%20%20%20%7D)%5Cn%20%20%20%20%7D)();%22,%22name%22:%22index%22,%22state%22:%22n%22%7D,%22css%22:%7B%22content%22:%22#ebpage_704875571645136897%20.ebcom-pricepanel%20%5Cr%5Cn+%20.ebcom-prstateitempanel%20%5Cr%5Cn+%20.ebcom-prsystemusagepanel%20%5Cr%5Cn+%20.ebcom-prworkrecordpanel%20%5Cr%5Cn+%20.ebcom-prokrcompletionratepanel%20%5Cr%5Cn+%20.ebcom-prokrcompletionratepanel%5Cr%5Cn+%20.ebcom-prcrmpanel%5Cr%5Cn+%20.ebcom-prkpiscorepanel%7B%5Cr%5Cn%20%20%5Ctborder:%200.3px%20solid%20var(--border-color);%5Cr%5Cn%7D#ebpage_704875571645136897%20.weapp-de-paper-box.weapp-de-paper-box%7B%5Cr%5Cn%20%20overflow:%20hidden;%5Cr%5Cn%7D#ebpage_704875571645136897%20.ebcoms-tab-menu-top%7B%5Cr%5Cn%20%20%20padding:%200;%5Cr%5Cn%20%20%20height:%2040px;%5Cr%5Cn%20%20%20%5Cr%5Cn%7D#ebpage_704875571645136897%20.ui-menu-nav-container%20.ui-menu-nav-wrap%7B%5Cr%5Cn%20%20padding-left:%2016px;%5Cr%5Cn%20%20margin-bottom:%200%20!important;%5Cr%5Cn%20%20border-bottom:%200.3px%20solid%20var(--border-color)%20!important;%5Cr%5Cn%7D#ebpage_704875571645136897%20#b9df5b04a2194a69aa11fe72e236fcd2%7B%5Cr%5Cn%20%20height:%20calc(100vh%20-%20150px);%5Cr%5Cn%20%20%20%20overflow-y:%20auto;%5Cr%5Cn%20%20%20%20background:%20#F7F7F7;%5Cr%5Cn%7D#ebpage_704875571645136897%20#db1b7dd393c24794bc0e9fd413f97692%7B%5Cr%5Cn%20%20height:%20calc(100vh%20-%20150px);%5Cr%5Cn%20%20overflow-y:%20auto%5Cr%5Cn%7D#ebpage_704875571645136897%20#a5a09a37f1ab4d8281d29ca14850dee2%7B%5Cr%5Cn%20%20height:%20calc(100vh%20-%20150px);%5Cr%5Cn%20%20overflow-y:%20auto%5Cr%5Cn%7D#ebpage_704875571645136897%20#df604d6baf46473bb8a99b2019b12c65%7B%5Cr%5Cn%20%20%20%20height:%20calc(100vh%20-%20150px);%5Cr%5Cn%20%20%20%20overflow-y:%20auto%5Cr%5Cn%7D%22,%22compiledContent%22:%22#ebpage_704875571645136897%20.ebcom-pricepanel%20%5Cr%5Cn+%20.ebcom-prstateitempanel%20%5Cr%5Cn+%20.ebcom-prsystemusagepanel%20%5Cr%5Cn+%20.ebcom-prworkrecordpanel%20%5Cr%5Cn+%20.ebcom-prokrcompletionratepanel%20%5Cr%5Cn+%20.ebcom-prokrcompletionratepanel%5Cr%5Cn+%20.ebcom-prcrmpanel%5Cr%5Cn+%20.ebcom-prkpiscorepanel%7B%5Cr%5Cn%20%20%5Ctborder:%200.3px%20solid%20var(--border-color);%5Cr%5Cn%7D#ebpage_704875571645136897%20.weapp-de-paper-box.weapp-de-paper-box%7B%5Cr%5Cn%20%20overflow:%20hidden;%5Cr%5Cn%7D#ebpage_704875571645136897%20.ebcoms-tab-menu-top%7B%5Cr%5Cn%20%20%20padding:%200;%5Cr%5Cn%20%20%20height:%2040px;%5Cr%5Cn%20%20%20%5Cr%5Cn%7D#ebpage_704875571645136897%20.ui-menu-nav-container%20.ui-menu-nav-wrap%7B%5Cr%5Cn%20%20padding-left:%2016px;%5Cr%5Cn%20%20margin-bottom:%200%20!important;%5Cr%5Cn%20%20border-bottom:%200.3px%20solid%20var(--border-color)%20!important;%5Cr%5Cn%7D#ebpage_704875571645136897%20#b9df5b04a2194a69aa11fe72e236fcd2%7B%5Cr%5Cn%20%20height:%20calc(100vh%20-%20150px);%5Cr%5Cn%20%20%20%20overflow-y:%20auto;%5Cr%5Cn%20%20%20%20background:%20#F7F7F7;%5Cr%5Cn%7D#ebpage_704875571645136897%20#db1b7dd393c24794bc0e9fd413f97692%7B%5Cr%5Cn%20%20height:%20calc(100vh%20-%20150px);%5Cr%5Cn%20%20overflow-y:%20auto%5Cr%5Cn%7D#ebpage_704875571645136897%20#a5a09a37f1ab4d8281d29ca14850dee2%7B%5Cr%5Cn%20%20height:%20calc(100vh%20-%20150px);%5Cr%5Cn%20%20overflow-y:%20auto%5Cr%5Cn%7D#ebpage_704875571645136897%20#df604d6baf46473bb8a99b2019b12c65%7B%5Cr%5Cn%20%20%20%20height:%20calc(100vh%20-%20150px);%5Cr%5Cn%20%20%20%20overflow-y:%20auto%5Cr%5Cn%7D%22,%22name%22:%22index%22,%22state%22:%22n%22%7D%7D",
    },
    config: {
      style: {
        page: {
          background: {
            backgroundColor: 'transparent',
            backgroundImage: '',
            backgroundPosition: 'center center',
            backgroundRepeat: 'no-repeat',
            backgroundSize: 'auto',
            positionType: 'grid',
            imgUrl: '',
          },
        },
      },
      dataset: [],
    },
    refTheme: '',
  },
  {
    id: '704875575821426690',
    createTime: 1647316634000,
    updateTime: 1673424420000,
    creator: '3572883652751288456',
    tenantKey: 't2k74i3rjo',
    deleteType: 0,
    pageId: '704875571645136897',
    layoutType: 'FLOW',
    terminalType: 'MOBILE',
    thumbnail: '817009720136335360',
    comps: [
      {
        id: 'da743a2636264689b9489b09364c02e9',
        refCompId: 'd4d85a4bb19c43ccaaa82a7e3c8f9948',
        type: 'Occupy',
        category: '4',
        refModule: '',
        tenantKey: 't2k74i3rjo',
        styleCategoryCompKey: 'BasicStyleConfig',
        styleCategoryPackage: '@weapp/ebdcoms',
        applyStyle: '1',
        lock: false,
        config: {
          type: 'Occupy',
          name: '占用视图',
          layout: {
            hidden: false,
            wrapped: false,
            selected: true,
          },
          title: '',
          titleEnabled: false,
          dataset: {
            id: '796888734774427648',
            text: 'wzh-数据源eb-物理-名称',
            type: 'FORM',
            groupId: '796529709720641537',
            isPhysical: true,
          },
          baseInfo: {
            id: '',
            objId: '',
            titlefieldid: '',
          },
          condition: {},
          defaultView: '4',
          weekStart: 1,
          startTimeRange: 8,
          endTimeRange: 24,
          resourceField: '796888983815266306',
          startDateTimeField: '796888859361878016',
          endDateTimeField: '796888859361878017',
          weekColorLog: '',
          weekShowField: '',
          promptInfo: {
            field: [
              {
                name: 'order_sort',
                text: '序号',
                content: '',
                type: 'text',
                overFlow: {
                  number: '',
                  type: 'line',
                },
                autoFocus: false,
                id: '-3',
                uid: 'c493cf89d8ac42c9ab2831465696f0e0',
              },
              {
                formInfo: {
                  fieldId: '2',
                },
                mainField: true,
                deleteType: false,
                type: 'Employee',
                lazyLoad: false,
                compType: 'Employee',
                placeholderMark: false,
                objId: '796888734774427648',
                name: 'creator',
                richEditor: false,
                text: '创建者',
                mainPrimaryKey: false,
                multiSelect: 'false',
                group: 'form',
                overFlow: {
                  number: '',
                  type: 'line',
                },
                autoFocus: false,
                id: '2',
                uid: 'd4e7979f315645149731ccfbf7af19ef',
              },
            ],
            cardLayout: {
              split: [
                {
                  index: 0,
                  setting: {
                    width: {
                      type: '',
                      value: '',
                    },
                  },
                  id: 'e11cf715f07046ba8157427064583d9c',
                  type: '0',
                },
              ],
              grid: [
                [
                  [
                    {
                      x: 0,
                      y: 0,
                      w: 1,
                      h: 1,
                      field: {
                        id: '-3',
                        uid: 'c493cf89d8ac42c9ab2831465696f0e0',
                        style: {},
                        width: {
                          type: '1',
                          value: '',
                          unit: 'px',
                        },
                        minWidth: '80',
                        showTrans: [],
                        align: 'left',
                        wordWrap: false,
                        padding: [0, 12, 0, 0],
                        image: {
                          showType: 'grid',
                          h: '100%',
                          w: '100%',
                        },
                        horAlign: '0',
                        content: '',
                        isTransAvatar: false,
                        overFlow: {
                          number: '',
                          type: 'line',
                        },
                      },
                      i: 'f5ae6c98347242c985c338da98c31e0c',
                    },
                    {
                      x: 1,
                      y: 0,
                      w: 1,
                      h: 1,
                      field: {
                        id: '2',
                        uid: 'd4e7979f315645149731ccfbf7af19ef',
                        style: {},
                        width: {
                          type: '1',
                          value: '',
                          unit: 'px',
                        },
                        minWidth: '80',
                        showTrans: [],
                        align: 'left',
                        wordWrap: false,
                        padding: [0, 12, 0, 0],
                        image: {
                          showType: 'grid',
                          h: '100%',
                          w: '100%',
                        },
                        horAlign: '0',
                        isTransAvatar: false,
                        overFlow: {
                          number: '',
                          type: 'line',
                        },
                      },
                      i: 'd7ecd2df0d4341a2af724cbf347f8b55',
                    },
                  ],
                ],
              ],
              size: [[4, 3]],
              row: [
                [
                  {
                    index: 0,
                    isHide: false,
                    setting: {
                      height: '',
                      alignItems: 'center',
                    },
                  },
                  {
                    index: 1,
                    isHide: false,
                    setting: {
                      height: '',
                      alignItems: 'center',
                    },
                  },
                  {
                    index: 2,
                    isHide: false,
                    setting: {
                      height: '',
                      alignItems: 'center',
                    },
                  },
                  {
                    index: 3,
                    isHide: false,
                    setting: {
                      height: '',
                      alignItems: 'center',
                    },
                  },
                ],
              ],
              extendArea: {
                id: 'fd1d92f5581c4bb3985fb4056e9f7608',
                display: 'none',
                type: 'current',
                pageMode: 'part',
                pageSize: 10,
              },
              shrinkDisplayType: 'single',
              shrinkRegionIndex: -1,
            },
            tableField: [
              {
                id: '-3',
                name: 'order_sort',
                text: '序号',
                content: '',
                type: 'text',
                style: {},
                showTrans: [],
                isTransAvatar: false,
                width: {
                  type: '1',
                  value: '',
                  unit: 'px',
                },
                wordWrap: false,
                horAlign: '0',
                align: 'left',
                minWidth: '80',
                padding: [0, 12, 0, 0],
                image: {
                  showType: 'grid',
                  h: '100%',
                  w: '100%',
                },
                overFlow: {
                  number: '',
                  type: 'line',
                },
                uid: 'c493cf89d8ac42c9ab2831465696f0e0',
                autoFocus: false,
              },
              {
                conditionTypeValue: 'in',
                formInfo: {
                  fieldId: '2',
                },
                mainField: true,
                deleteType: false,
                type: 'Employee',
                lazyLoad: false,
                compType: 'Employee',
                placeholderMark: false,
                objId: '796888734774427648',
                name: 'creator',
                richEditor: false,
                conditionType: [
                  {
                    id: 'in',
                    content: '属于',
                    disabled: false,
                  },
                  {
                    id: 'notIn',
                    content: '不属于',
                    disabled: false,
                  },
                  {
                    id: 'isNull',
                    content: '为空',
                    disabled: false,
                  },
                  {
                    id: 'isNotNull',
                    content: '不为空',
                    disabled: false,
                  },
                ],
                id: '2',
                text: '创建者',
                mainPrimaryKey: false,
                multiSelect: 'false',
                group: 'form',
                style: {},
                showTrans: [],
                isTransAvatar: false,
                width: {
                  type: '1',
                  value: '',
                  unit: 'px',
                },
                wordWrap: false,
                horAlign: '0',
                align: 'left',
                minWidth: '80',
                padding: [0, 12, 0, 0],
                image: {
                  showType: 'grid',
                  h: '100%',
                  w: '100%',
                },
                overFlow: {
                  number: '',
                  type: 'line',
                },
                uid: 'd4e7979f315645149731ccfbf7af19ef',
                autoFocus: false,
              },
            ],
          },
          detailedInfo: {
            field: [],
            cardLayout: {
              split: [
                {
                  index: 0,
                  setting: {
                    width: {
                      type: '',
                      value: '',
                    },
                  },
                  id: 'c6c6a2eec61f4aefa2738dc4ccff1163',
                  type: '0',
                },
              ],
              grid: [[[]]],
              size: [[4, 3]],
              row: [
                [
                  {
                    index: 0,
                    isHide: false,
                    setting: {
                      height: '',
                      alignItems: 'center',
                    },
                  },
                  {
                    index: 1,
                    isHide: false,
                    setting: {
                      height: '',
                      alignItems: 'center',
                    },
                  },
                  {
                    index: 2,
                    isHide: false,
                    setting: {
                      height: '',
                      alignItems: 'center',
                    },
                  },
                  {
                    index: 3,
                    isHide: false,
                    setting: {
                      height: '',
                      alignItems: 'center',
                    },
                  },
                ],
              ],
              extendArea: {
                id: 'ec26f294d3784d68ba9bc5d8884e6608',
                display: 'none',
                type: 'current',
                pageMode: 'part',
                pageSize: 10,
              },
              shrinkDisplayType: 'single',
              shrinkRegionIndex: -1,
            },
          },
          listShowFields:
            '[{"id":"-3","name":"order_sort","text":"序号","content":"","type":"Text","objName":"","orderType":"DEFAULT","width":100,"uid":"cfd44d7cfa704ec99e6001ef197f617d"},{"conditionTypeValue":"in","formInfo":{"fieldId":"2"},"mainField":true,"deleteType":false,"type":"Employee","lazyLoad":false,"compType":"Employee","placeholderMark":false,"objId":"796888734774427648","name":"creator","richEditor":false,"conditionType":[{"id":"in","content":"属于","disabled":false},{"id":"notIn","content":"不属于","disabled":false},{"id":"isNull","content":"为空","disabled":false},{"id":"isNotNull","content":"不为空","disabled":false}],"id":"2","text":"创建者","mainPrimaryKey":false,"multiSelect":"false","objName":"","orderType":"DEFAULT","width":100,"uid":"be379ae8d6ff46c99e8d129c335f0c15"},{"conditionTypeValue":"like","formInfo":{"fieldId":"1"},"mainField":true,"deleteType":false,"type":"String","lazyLoad":false,"compTypec":"","compType":"Text","placeholderMark":false,"objId":"796888734774427648","name":"name","richEditor":false,"conditionType":[{"id":"like","content":"包含","disabled":false},{"id":"notLike","content":"不包含","disabled":false},{"id":"eq","content":"等于","disabled":false},{"id":"notEq","content":"不等于","disabled":false},{"id":"isNull","content":"为空","disabled":false},{"id":"isNotNull","content":"不为空","disabled":false}],"id":"1","text":"标题","mainPrimaryKey":false,"multiSelect":"false","objName":"","orderType":"DEFAULT","width":100,"uid":"a640e6be2f474ea1935b5bc06990a132"},{"conditionTypeValue":"in","formInfo":{"fieldId":"8"},"mainField":true,"deleteType":false,"type":"Select","lazyLoad":false,"compType":"Select","placeholderMark":false,"objId":"796888734774427648","name":"data_status","richEditor":false,"conditionType":[{"id":"in","content":"属于","disabled":false},{"id":"notIn","content":"不属于","disabled":false},{"id":"isNull","content":"为空","disabled":false},{"id":"isNotNull","content":"不为空","disabled":false}],"id":"8","text":"数据状态","mainPrimaryKey":false,"multiSelect":"false","objName":"","orderType":"DEFAULT","width":100,"uid":"da78a8d9c4414e33ac238ff813054424"},{"conditionTypeValue":"in","formInfo":{"fieldId":"6"},"mainField":true,"deleteType":false,"type":"Employee","lazyLoad":false,"compType":"Employee","placeholderMark":false,"objId":"796888734774427648","name":"updater","richEditor":false,"conditionType":[{"id":"in","content":"属于","disabled":false},{"id":"notIn","content":"不属于","disabled":false},{"id":"isNull","content":"为空","disabled":false},{"id":"isNotNull","content":"不为空","disabled":false}],"id":"6","text":"最后更新人","mainPrimaryKey":false,"multiSelect":"false","objName":"","orderType":"DEFAULT","width":100,"uid":"f5c024beb6b44f0c90e12ede34c83a17"}]',
          filter: {},
          conflictColor: '#ff9c9d',
          usageColor: '#c6ddff',
          panelHeightRatio: 60,
          flow: {
            parent: 'ROOT',
            nodes: [],
            isCanvas: false,
            linkedNodes: {},
            nodeId: '_SidBp_ci2',
          },
          resourceFieldObjId: '796888734774427648',
          endTimeField: '',
          showBottomList: true,
          showTimeSlot: [8, 24],
          viewType: 'cardType',
          cardLayout: {
            field: [
              {
                name: 'order_sort',
                text: '序号',
                content: '',
                type: 'text',
                overFlow: {
                  number: '',
                  type: 'line',
                },
                autoFocus: false,
                id: '-3',
                uid: 'a79ded8969aa4c97a2d107913e5ebeb5',
              },
              {
                formInfo: {
                  fieldId: '2',
                },
                mainField: true,
                deleteType: false,
                type: 'Employee',
                lazyLoad: false,
                compType: 'Employee',
                placeholderMark: false,
                objId: '796888734774427648',
                name: 'creator',
                richEditor: false,
                text: '创建者',
                mainPrimaryKey: false,
                multiSelect: 'false',
                group: 'form',
                overFlow: {
                  number: '',
                  type: 'line',
                },
                autoFocus: false,
                id: '2',
                uid: 'e1bd86834b814646b83f8092690bce1d',
              },
              {
                formInfo: {
                  fieldId: '1',
                },
                mainField: true,
                deleteType: false,
                type: 'String',
                lazyLoad: false,
                compTypec: '',
                compType: 'Text',
                placeholderMark: false,
                objId: '796888734774427648',
                name: 'name',
                richEditor: false,
                text: '标题',
                mainPrimaryKey: false,
                multiSelect: 'false',
                group: 'form',
                overFlow: {
                  number: '',
                  type: 'line',
                },
                autoFocus: false,
                id: '1',
                uid: 'ab7804c706d24cf08a261546478612ed',
              },
            ],
            cardLayout: {
              split: [
                {
                  index: 0,
                  setting: {
                    width: {
                      type: '',
                      value: '',
                    },
                  },
                  id: 'df266386e4b342fe982555700aa32186',
                  type: '0',
                },
              ],
              grid: [
                [
                  [
                    {
                      x: 0,
                      y: 0,
                      w: 1,
                      h: 1,
                      field: {
                        id: '-3',
                        uid: 'a79ded8969aa4c97a2d107913e5ebeb5',
                        style: {},
                        width: {
                          type: '1',
                          value: '',
                          unit: 'px',
                        },
                        minWidth: '80',
                        showTrans: [],
                        align: 'left',
                        wordWrap: false,
                        padding: [0, 12, 0, 0],
                        image: {
                          showType: 'grid',
                          h: '100%',
                          w: '100%',
                        },
                        horAlign: '0',
                        content: '',
                        isTransAvatar: false,
                        overFlow: {
                          number: '',
                          type: 'line',
                        },
                      },
                      i: 'de490b6b403240a197b06ed06b73ffcb',
                    },
                    {
                      x: 1,
                      y: 0,
                      w: 1,
                      h: 1,
                      field: {
                        id: '2',
                        uid: 'e1bd86834b814646b83f8092690bce1d',
                        style: {},
                        width: {
                          type: '1',
                          value: '',
                          unit: 'px',
                        },
                        minWidth: '80',
                        showTrans: [],
                        align: 'left',
                        wordWrap: false,
                        padding: [0, 12, 0, 0],
                        image: {
                          showType: 'grid',
                          h: '100%',
                          w: '100%',
                        },
                        horAlign: '0',
                        isTransAvatar: false,
                        overFlow: {
                          number: '',
                          type: 'line',
                        },
                      },
                      i: 'bf05481543e74d2dbde8056c81dd22a4',
                    },
                    {
                      x: 2,
                      y: 0,
                      w: 1,
                      h: 1,
                      field: {
                        id: '1',
                        uid: 'ab7804c706d24cf08a261546478612ed',
                        style: {},
                        width: {
                          type: '1',
                          value: '',
                          unit: 'px',
                        },
                        minWidth: '80',
                        showTrans: [],
                        align: 'left',
                        wordWrap: false,
                        padding: [0, 12, 0, 0],
                        image: {
                          showType: 'grid',
                          h: '100%',
                          w: '100%',
                        },
                        horAlign: '0',
                        isTransAvatar: false,
                        overFlow: {
                          number: '',
                          type: 'line',
                        },
                      },
                      i: 'd31b758aa26f41879bf0083961c7e8d1',
                    },
                  ],
                ],
              ],
              size: [[4, 3]],
              row: [
                [
                  {
                    index: 0,
                    isHide: false,
                    setting: {
                      height: '',
                      alignItems: 'center',
                    },
                  },
                  {
                    index: 1,
                    isHide: false,
                    setting: {
                      height: '',
                      alignItems: 'center',
                    },
                  },
                  {
                    index: 2,
                    isHide: false,
                    setting: {
                      height: '',
                      alignItems: 'center',
                    },
                  },
                  {
                    index: 3,
                    isHide: false,
                    setting: {
                      height: '',
                      alignItems: 'center',
                    },
                  },
                ],
              ],
              extendArea: {
                id: 'b54e46deb4604bd7877f8e034d976468',
                display: 'none',
                type: 'current',
                pageMode: 'part',
                pageSize: 10,
              },
              shrinkDisplayType: 'single',
              shrinkRegionIndex: -1,
            },
            tableField: [
              {
                id: '-3',
                name: 'order_sort',
                text: '序号',
                content: '',
                type: 'text',
                style: {},
                showTrans: [],
                isTransAvatar: false,
                width: {
                  type: '1',
                  value: '',
                  unit: 'px',
                },
                wordWrap: false,
                horAlign: '0',
                align: 'left',
                minWidth: '80',
                padding: [0, 12, 0, 0],
                image: {
                  showType: 'grid',
                  h: '100%',
                  w: '100%',
                },
                overFlow: {
                  number: '',
                  type: 'line',
                },
                uid: 'a79ded8969aa4c97a2d107913e5ebeb5',
                autoFocus: false,
              },
              {
                conditionTypeValue: 'in',
                formInfo: {
                  fieldId: '2',
                },
                mainField: true,
                deleteType: false,
                type: 'Employee',
                lazyLoad: false,
                compType: 'Employee',
                placeholderMark: false,
                objId: '796888734774427648',
                name: 'creator',
                richEditor: false,
                conditionType: [
                  {
                    id: 'in',
                    content: '属于',
                    disabled: false,
                  },
                  {
                    id: 'notIn',
                    content: '不属于',
                    disabled: false,
                  },
                  {
                    id: 'isNull',
                    content: '为空',
                    disabled: false,
                  },
                  {
                    id: 'isNotNull',
                    content: '不为空',
                    disabled: false,
                  },
                ],
                id: '2',
                text: '创建者',
                mainPrimaryKey: false,
                multiSelect: 'false',
                group: 'form',
                style: {},
                showTrans: [],
                isTransAvatar: false,
                width: {
                  type: '1',
                  value: '',
                  unit: 'px',
                },
                wordWrap: false,
                horAlign: '0',
                align: 'left',
                minWidth: '80',
                padding: [0, 12, 0, 0],
                image: {
                  showType: 'grid',
                  h: '100%',
                  w: '100%',
                },
                overFlow: {
                  number: '',
                  type: 'line',
                },
                uid: 'e1bd86834b814646b83f8092690bce1d',
                autoFocus: false,
              },
              {
                conditionTypeValue: 'like',
                formInfo: {
                  fieldId: '1',
                },
                mainField: true,
                deleteType: false,
                type: 'String',
                lazyLoad: false,
                compTypec: '',
                compType: 'Text',
                placeholderMark: false,
                objId: '796888734774427648',
                name: 'name',
                richEditor: false,
                conditionType: [
                  {
                    id: 'like',
                    content: '包含',
                    disabled: false,
                  },
                  {
                    id: 'notLike',
                    content: '不包含',
                    disabled: false,
                  },
                  {
                    id: 'eq',
                    content: '等于',
                    disabled: false,
                  },
                  {
                    id: 'notEq',
                    content: '不等于',
                    disabled: false,
                  },
                  {
                    id: 'isNull',
                    content: '为空',
                    disabled: false,
                  },
                  {
                    id: 'isNotNull',
                    content: '不为空',
                    disabled: false,
                  },
                ],
                id: '1',
                text: '标题',
                mainPrimaryKey: false,
                multiSelect: 'false',
                group: 'form',
                style: {},
                showTrans: [],
                isTransAvatar: false,
                width: {
                  type: '1',
                  value: '',
                  unit: 'px',
                },
                wordWrap: false,
                horAlign: '0',
                align: 'left',
                minWidth: '80',
                padding: [0, 12, 0, 0],
                image: {
                  showType: 'grid',
                  h: '100%',
                  w: '100%',
                },
                overFlow: {
                  number: '',
                  type: 'line',
                },
                uid: 'ab7804c706d24cf08a261546478612ed',
                autoFocus: false,
              },
            ],
          },
          resourceImgField: '796888764746768384',
          resourceSelectField: '796888764746768385',
        },
        package: '@weapp/ebdfpage',
        isLock: false,
        isCustom: false,
      },
    ],
    rightCompIds: ['d4d85a4bb19c43ccaaa82a7e3c8f9948', 'da743a2636264689b9489b09364c02e9'],
    module: 'EB_PAGE',
    sourceCode: {},
    config: {},
    refTheme: '',
  },
];
