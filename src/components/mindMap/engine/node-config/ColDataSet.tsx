import { observer } from 'mobx-react';
import React from 'react';
import DataSet from '../../../common/plugins/DataSet';
import { DataSetItem, DataSetProps } from '../../../common/plugins/DataSet/types';
interface CalDataSetProps extends DataSetProps {}

@observer
export default class CalDataSet extends React.PureComponent<CalDataSetProps> {
  onChange = (dataset: DataSetItem) => {
    this.props.onChange(dataset);
  };

  render() {
    const { value } = this.props;
    return (
      <DataSet
        weId={`${this.props.weId || ''}_hof9vb`}
        {...this.props}
        value={value}
        onChange={this.onChange}
      />
    );
  }
}
