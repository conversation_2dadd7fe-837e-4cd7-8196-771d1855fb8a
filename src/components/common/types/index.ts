import { DataSet } from "@weapp/ebdcoms";
import { AnyObj } from "@weapp/ui";
import { FormFieldItem } from "../../../types/common";
import { MindPluginCenter } from "../../mindMap/types";
import { MindMapConfigData } from "../../../components/mindMap/types";

export interface ViewComProps {
    compId: string;
    pageId: string;
    config: MindMapConfigData;
    isMobile: boolean;
    isDesign: boolean;
    ebParams?: AnyObj; // eb参数
    isDoc?: boolean; // eb参数
    comServicePath?: string;
    page?: AnyObj;
    ebStore?: AnyObj;
    events?: AnyObj;
    pluginCenter?: MindPluginCenter // 引入插件机制
    // 是否是表单后台引擎
    isEngine?: boolean;
    onRef?: (ref: any) => void;
    coms?: any[]
    lineStyle?: string; // 连线方式
}

export interface DataSetItem extends DataSet { }

export interface DataSetField {
    compType: string;
    formatType: string;
    id: string;
    name: string;
    objId: string;
    optionType: string;
    text: string;
    type: string;
}

export enum Direction {
    Left = 'left',
    Right = 'right',
    Down = 'down',
    Up = 'up',
}

export type ChangeSelectOptions = {
    [key: string]: FormFieldItem[];
};

export enum ActionType {
    System = 'system',
    Custom = 'custom',
    Interface = 'customInterface',
    OpenPage = 'openpage',
    Esb = 'esb',
    Reply = 'reply',
    ConfirmBox = 'confirmBox', // 确认框
    JsCode = 'jsCode', // 执行JavaScript
    SmallCard = 'smallCard', // 执行JavaScript
}

  
export interface OccupyBuilderProps {
    appId: string;
    pageId: string;
    viewStore: any;
    config: any;
    clientType: 'PC' | 'MOBILE';
    refreshFn?: () => void;
}