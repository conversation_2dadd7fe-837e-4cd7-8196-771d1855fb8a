import React from 'react';
import { observer } from 'mobx-react';
import { Form, Button, Dialog } from '@weapp/ui';
import { getLabel, isEmpty } from '@weapp/utils';
// import isEmpty from 'lodash-es/isEmpty';
import { NodeSizeStore } from './store';
import { NodeSizeDataType } from './types';
import { INIT_NODE_SIZE, ebdfClsPrefix } from '../../../../../constants';
import '../index.less';

const cls = `${ebdfClsPrefix}-mindmap-node-slider-node-size`;
interface Props {
  value?: NodeSizeDataType;
  config: any;
  onChange?: (changeData: NodeSizeDataType) => void;
  onConfigChange?: (changeData: any) => void;
}
@observer
class NodeSizeSet extends React.PureComponent<Props> {
  state = {
    visible: false,
  };
  store = new NodeSizeStore();
  componentWillUnmount(): void {
    this.store.reset();
  }
  // 是否为通用设置
  isCommonConfig = () => {
    const { config } = this.props;
    return !config.id;
  };
  init = (cb?: Function) => {
    try {
      const { value, config } = this.props;
      const { commonConfig, commonNodeSizeSet } = config;
      let sizeValue = INIT_NODE_SIZE();
      if (this.isCommonConfig()) {
        // 判断是否同步标识 兼容低版本
        if (!isEmpty(commonConfig?.nodeSize) && isEmpty(commonNodeSizeSet)) {
          sizeValue = commonConfig?.nodeSize;
        } else if (!isEmpty(value)) {
          sizeValue = value;
        }
      } else {
        sizeValue = isEmpty(value) ? INIT_NODE_SIZE(config.id.indexOf('root_') > -1) : value;
      }
      this.store.init(sizeValue, this.isCommonConfig(), cb);
    } catch (e) {}
  };
  changeVisible = (visible: boolean) => {
    if (visible) {
      // 避免闪烁
      return this.init(() => {
        this.setState({ visible });
      });
    }
    this.setState({ visible });
  };
  onSure = () => {
    this.changeVisible(false);
    const { onChange } = this.props;
    const { formStore } = this.store;
    const formData = formStore.getFormDatas();
    const newValues = {
      height: {
        type: formData.heightType,
        size: formData.heightSize,
      },
      width: {
        type: formData.widthType,
        size: formData.widthSize,
      },
      sizeType: formData.sizeType,
    };
    // 更新value
    onChange && onChange(newValues);
  };
  renderForm = () => {
    const { visible } = this.state;
    const { formStore } = this.store;
    if (!visible) return null;
    const formData = formStore.getFormDatas();
    const title = this.isCommonConfig() ? `${getLabel('245997', '节点尺寸通用设置')}` : `${getLabel('245998', '节点尺寸设置')}`;
    return (
      <Dialog
        weId={`${this.props.weId || ''}_p401ie`}
        title={title}
        closable
        width={400}
        onClose={() => this.changeVisible(false)}
        destroyOnClose
        visible={visible}
        mask
        footer={[
          <Button weId={`${this.props.weId || ''}_9le4rz`} key='sure' type='primary' onClick={this.onSure}>
            {getLabel('40565', '确定')}
          </Button>,
          <Button weId={`${this.props.weId || ''}_4assuu`} key='cancel' onClick={() => this.changeVisible(false)}>
            {getLabel('53937', '取消')}
          </Button>,
        ]}
      >
        <Form weId={`${this.props.weId || ''}_kofeue`} store={this.store.formStore} onChange={this.store.omFormChange} className={formData.sizeType === 'common' ? `${cls}-formHide` : ''} />
      </Dialog>
    );
  };
  render() {
    return (
      <div className={cls}>
        <Button weId={`${this.props.weId || ''}_kysbu3`} onClick={() => this.changeVisible(true)} size={this.isCommonConfig() ? 'small' : 'middle'}>
          {getLabel('54205', '设置')}
        </Button>
        {this.renderForm()}
      </div>
    );
  }
}
export default NodeSizeSet;
