/**
 * <AUTHOR>
 * @createTime 2021-11-15
 */
import { DataSet } from '@weapp/ebdcoms';
import { CorsComponent } from '@weapp/ui';
import { getLabel } from '@weapp/utils';
import { toJS } from 'mobx';
import { observer } from 'mobx-react';
import React from 'react';

export interface ListFilterProps extends React.Attributes {
  onChange: (val: any) => void;
  value?: any;
  dataset: DataSet;
  disabled?: boolean;
  placeholder?: string;
  filter?: any;
  title?: string;
}

@observer
export default class EBFilter extends React.PureComponent<ListFilterProps> {
  render() {
    let { value } = this.props;
    const {
      onChange, dataset, title, filter,
    } = this.props;
    const _value = typeof value === 'string' && value ? JSON.parse(value) : value;
    if (!value || (Array.isArray(toJS(value)) && toJS(value).length === 0)) {
      value = {};
    }
    if (!Array.isArray(toJS(value))) {
      return (
        <CorsComponent
          weId={`${this.props.weId || ''}_b0dgq0`}
          app="@weapp/components"
          compName="ConditionSet"
          dataSet={dataset}
          value={value}
          onChange={(val: any) => onChange(toJS(val))}
          fieldFilter={filter}
          placeholder={getLabel('87541', '点击设置过滤条件')}
          title={title || getLabel('99497', '日历固定查询条件')}
        />
      );
    }

    return (
      <CorsComponent
        weId={`${this.props.weId || ''}_30wszt`}
        app="@weapp/ebdcoms"
        compName="FilterInput"
        title={title || getLabel('56236', '列表固定查询条件')}
        {...this.props}
        filter={filter}
        value={_value}
        dataset={dataset}
        onChange={(val: any) => onChange(toJS(val))}
      />
    );
  }
}
