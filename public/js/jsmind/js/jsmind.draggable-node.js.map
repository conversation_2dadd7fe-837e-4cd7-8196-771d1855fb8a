{"version": 3, "file": "jsmind.draggable-node.js", "sources": ["../src/plugins/jsmind.draggable-node.js"], "sourcesContent": ["import jsMind from 'jsmind';\n\nif (!jsMind) {\n    throw new Error('jsMind is not defined');\n}\n\nconst $ = jsMind.$;\n\nconst clear_selection =\n    'getSelection' in $.w\n        ? function () {\n              $.w.getSelection().removeAllRanges();\n          }\n        : function () {\n              $.d.selection.empty();\n          };\n\nconst DEFAULT_OPTIONS = {\n    line_width: 5,\n    line_color: 'rgba(0,0,0,0.3)',\n    line_color_invalid: 'rgba(255,51,51,0.6)',\n    lookup_delay: 200,\n    lookup_interval: 100,\n    scrolling_trigger_width: 20,\n    scrolling_step_length: 10,\n    shadow_node_class_name: 'jsmind-draggable-shadow-node',\n};\n\nclass DraggableNode {\n    constructor(jm, options) {\n        var opts = {};\n        jsMind.util.json.merge(opts, DEFAULT_OPTIONS);\n        jsMind.util.json.merge(opts, options);\n\n        this.version = '0.4.0';\n        this.jm = jm;\n        this.options = opts;\n        this.e_canvas = null;\n        this.canvas_ctx = null;\n        this.shadow = null;\n        this.shadow_p_x = 0;\n        this.shadow_p_y = 0;\n        this.shadow_w = 0;\n        this.shadow_h = 0;\n        this.active_node = null;\n        this.target_node = null;\n        this.target_direct = null;\n        this.client_w = 0;\n        this.client_h = 0;\n        this.offset_x = 0;\n        this.offset_y = 0;\n        this.hlookup_delay = 0;\n        this.hlookup_timer = 0;\n        this.capture = false;\n        this.moved = false;\n        this.canvas_draggable = jm.get_view_draggable();\n        this.view_panel = jm.view.e_panel;\n        this.view_panel_rect = null;\n    }\n    init() {\n        this.create_canvas();\n        this.create_shadow();\n        this.event_bind();\n    }\n    resize() {\n        this.jm.view.e_nodes.appendChild(this.shadow);\n        this.e_canvas.width = this.jm.view.size.w;\n        this.e_canvas.height = this.jm.view.size.h;\n    }\n    create_canvas() {\n        var c = $.c('canvas');\n        this.jm.view.e_panel.appendChild(c);\n        var ctx = c.getContext('2d');\n        this.e_canvas = c;\n        this.canvas_ctx = ctx;\n    }\n    create_shadow() {\n        var s = $.c('jmnode');\n        s.style.visibility = 'hidden';\n        s.style.zIndex = '3';\n        s.style.cursor = 'move';\n        s.style.opacity = '0.7';\n        s.className = this.options.shadow_node_class_name;\n        this.shadow = s;\n    }\n    reset_shadow(el) {\n        var s = this.shadow.style;\n        this.shadow.innerHTML = el.innerHTML;\n        s.left = el.style.left;\n        s.top = el.style.top;\n        s.width = el.style.width;\n        s.height = el.style.height;\n        s.backgroundImage = el.style.backgroundImage;\n        s.backgroundSize = el.style.backgroundSize;\n        s.transform = el.style.transform;\n        this.shadow_w = this.shadow.clientWidth;\n        this.shadow_h = this.shadow.clientHeight;\n    }\n    show_shadow() {\n        if (!this.moved) {\n            this.shadow.style.visibility = 'visible';\n        }\n    }\n    hide_shadow() {\n        this.shadow.style.visibility = 'hidden';\n    }\n    magnet_shadow(shadow_p, node_p, invalid) {\n        this.canvas_ctx.lineWidth = this.options.line_width;\n        this.canvas_ctx.strokeStyle = invalid\n            ? this.options.line_color_invalid\n            : this.options.line_color;\n        this.canvas_ctx.lineCap = 'round';\n        this.clear_lines();\n        this.canvas_lineto(shadow_p.x, shadow_p.y, node_p.x, node_p.y);\n    }\n    clear_lines() {\n        this.canvas_ctx.clearRect(0, 0, this.jm.view.size.w, this.jm.view.size.h);\n    }\n    canvas_lineto(x1, y1, x2, y2) {\n        this.canvas_ctx.beginPath();\n        this.canvas_ctx.moveTo(x1, y1);\n        this.canvas_ctx.lineTo(x2, y2);\n        this.canvas_ctx.stroke();\n    }\n    event_bind() {\n        var jd = this;\n        var container = this.jm.view.container;\n        $.on(container, 'mousedown', function (e) {\n            if (e.button === 0) {\n                jd.dragstart.call(jd, e);\n            }\n        });\n        $.on(container, 'mousemove', function (e) {\n            if (e.movementX !== 0 || e.movementY !== 0) {\n                jd.drag.call(jd, e);\n            }\n        });\n        $.on(container, 'mouseup', function (e) {\n            jd.dragend.call(jd, e);\n        });\n        $.on(container, 'touchstart', function (e) {\n            jd.dragstart.call(jd, e);\n        });\n        $.on(container, 'touchmove', function (e) {\n            jd.drag.call(jd, e);\n        });\n        $.on(container, 'touchend', function (e) {\n            jd.dragend.call(jd, e);\n        });\n    }\n    dragstart(e) {\n        if (!this.jm.get_editable()) {\n            return;\n        }\n        if (this.capture) {\n            return;\n        }\n        this.active_node = null;\n        this.view_draggable = this.jm.get_view_draggable();\n\n        var jview = this.jm.view;\n        var el = this.find_node_element(e.target);\n        if (!el) {\n            console.log('throw away');\n            return;\n        }\n        if (this.view_draggable) {\n            this.jm.disable_view_draggable();\n        }\n        var nodeid = jview.get_binded_nodeid(el);\n        if (!!nodeid) {\n            var node = this.jm.get_node(nodeid);\n            if (!node.isroot) {\n                this.reset_shadow(el);\n                this.view_panel_rect = this.view_panel.getBoundingClientRect();\n                this.active_node = node;\n                this.offset_x =\n                    (e.clientX || e.touches[0].clientX) / jview.zoom_current - el.offsetLeft;\n                this.offset_y =\n                    (e.clientY || e.touches[0].clientY) / jview.zoom_current - el.offsetTop;\n                this.client_hw = Math.floor(el.clientWidth / 2);\n                this.client_hh = Math.floor(el.clientHeight / 2);\n                if (this.hlookup_delay != 0) {\n                    $.w.clearTimeout(this.hlookup_delay);\n                }\n                if (this.hlookup_timer != 0) {\n                    $.w.clearInterval(this.hlookup_timer);\n                }\n                var jd = this;\n                this.hlookup_delay = $.w.setTimeout(function () {\n                    jd.hlookup_delay = 0;\n                    jd.hlookup_timer = $.w.setInterval(function () {\n                        jd.lookup_target_node.call(jd);\n                    }, jd.options.lookup_interval);\n                }, this.options.lookup_delay);\n                jd.capture = true;\n            }\n        }\n    }\n    drag(e) {\n        if (!this.jm.get_editable()) {\n            return;\n        }\n        if (this.capture) {\n            e.preventDefault();\n            this.show_shadow();\n            this.moved = true;\n            clear_selection();\n            var jview = this.jm.view;\n            var px = (e.clientX || e.touches[0].clientX) / jview.zoom_current - this.offset_x;\n            var py = (e.clientY || e.touches[0].clientY) / jview.zoom_current - this.offset_y;\n            // scrolling container axisY if drag nodes exceeding container\n            if (\n                e.clientY - this.view_panel_rect.top < this.options.scrolling_trigger_width &&\n                this.view_panel.scrollTop > this.options.scrolling_step_length\n            ) {\n                this.view_panel.scrollBy(0, -this.options.scrolling_step_length);\n                this.offset_y += this.options.scrolling_step_length / jview.zoom_current;\n            } else if (\n                this.view_panel_rect.bottom - e.clientY < this.options.scrolling_trigger_width &&\n                this.view_panel.scrollTop <\n                    this.view_panel.scrollHeight -\n                        this.view_panel_rect.height -\n                        this.options.scrolling_step_length\n            ) {\n                this.view_panel.scrollBy(0, this.options.scrolling_step_length);\n                this.offset_y -= this.options.scrolling_step_length / jview.zoom_current;\n            }\n            // scrolling container axisX if drag nodes exceeding container\n            if (\n                e.clientX - this.view_panel_rect.left < this.options.scrolling_trigger_width &&\n                this.view_panel.scrollLeft > this.options.scrolling_step_length\n            ) {\n                this.view_panel.scrollBy(-this.options.scrolling_step_length, 0);\n                this.offset_x += this.options.scrolling_step_length / jview.zoom_current;\n            } else if (\n                this.view_panel_rect.right - e.clientX < this.options.scrolling_trigger_width &&\n                this.view_panel.scrollLeft <\n                    this.view_panel.scrollWidth -\n                        this.view_panel_rect.width -\n                        this.options.scrolling_step_length\n            ) {\n                this.view_panel.scrollBy(this.options.scrolling_step_length, 0);\n                this.offset_x -= this.options.scrolling_step_length / jview.zoom_current;\n            }\n            this.shadow.style.left = px + 'px';\n            this.shadow.style.top = py + 'px';\n            clear_selection();\n        }\n    }\n    dragend(e) {\n        if (!this.jm.get_editable()) {\n            return;\n        }\n        if (this.view_draggable) {\n            this.jm.enable_view_draggable();\n        }\n        if (this.capture) {\n            if (this.hlookup_delay != 0) {\n                $.w.clearTimeout(this.hlookup_delay);\n                this.hlookup_delay = 0;\n                this.clear_lines();\n            }\n            if (this.hlookup_timer != 0) {\n                $.w.clearInterval(this.hlookup_timer);\n                this.hlookup_timer = 0;\n                this.clear_lines();\n            }\n            if (this.moved) {\n                var src_node = this.active_node;\n                var target_node = this.target_node;\n                var target_direct = this.target_direct;\n                this.move_node(src_node, target_node, target_direct);\n            }\n            this.hide_shadow();\n        }\n        this.view_panel_rect = null;\n        this.moved = false;\n        this.capture = false;\n    }\n    find_node_element(el) {\n        if (\n            el === this.jm.view.e_nodes ||\n            el === this.jm.view.e_panel ||\n            el === this.jm.view.container\n        ) {\n            return null;\n        }\n        if (el.tagName.toLowerCase() === 'jmnode') {\n            return el;\n        }\n        return this.find_node_element(el.parentNode);\n    }\n    lookup_target_node() {\n        let sx = this.shadow.offsetLeft;\n        let sy = this.shadow.offsetTop;\n        if (sx === this.shadow_p_x && sy === this.shadow_p_y) {\n            return;\n        }\n        this.shadow_p_x = sx;\n        this.shadow_p_y = sy;\n\n        let target_direction =\n            this.shadow_p_x + this.shadow_w / 2 >= this.get_root_x()\n                ? jsMind.direction.right\n                : jsMind.direction.left;\n        let overlapping_node = this.lookup_overlapping_node_parent(target_direction);\n        let target_node = overlapping_node || this.lookup_close_node(target_direction);\n        if (!!target_node) {\n            let points = this.calc_point_of_node(target_node, target_direction);\n            let invalid = jsMind.node.inherited(this.active_node, target_node);\n            this.magnet_shadow(points.sp, points.np, invalid);\n            this.target_node = target_node;\n            this.target_direct = target_direction;\n        }\n    }\n    get_root_x() {\n        let root = this.jm.get_root();\n        let root_location = root.get_location();\n        let root_size = root.get_size();\n        return root_location.x + root_size.w / 2;\n    }\n\n    lookup_overlapping_node_parent(direction) {\n        let shadowRect = this.shadow.getBoundingClientRect();\n        let x = shadowRect.x + (shadowRect.width * (1 - direction)) / 2;\n        let deltaX = (this.jm.options.layout.hspace + this.jm.options.layout.pspace) * direction;\n        let deltaY = shadowRect.height;\n        let points = [\n            [x, shadowRect.y],\n            [x, shadowRect.y + deltaY / 2],\n            [x, shadowRect.y + deltaY],\n            [x + deltaX / 2, shadowRect.y],\n            [x + deltaX / 2, shadowRect.y + deltaY / 2],\n            [x + deltaX / 2, shadowRect.y + deltaY],\n            [x + deltaX, shadowRect.y],\n            [x + deltaX, shadowRect.y + deltaY / 2],\n            [x + deltaX, shadowRect.y + deltaY],\n        ];\n        for (const p of points) {\n            let n = this.lookup_node_parent_by_location(p[0], p[1]);\n            if (!!n) {\n                return n;\n            }\n        }\n    }\n\n    lookup_node_parent_by_location(x, y) {\n        return $.d\n            .elementsFromPoint(x, y)\n            .filter(\n                x => x.tagName === 'JMNODE' && x.className !== this.options.shadow_node_class_name\n            )\n            .map(el => this.jm.view.get_binded_nodeid(el))\n            .map(id => id && this.jm.mind.nodes[id])\n            .map(n => n && n.parent)\n            .find(n => n);\n    }\n\n    lookup_close_node(direction) {\n        return Object.values(this.jm.mind.nodes)\n            .filter(n => n.direction == direction || n.isroot)\n            .filter(n => this.jm.layout.is_visible(n))\n            .filter(n => this.shadow_on_target_side(n, direction))\n            .map(n => ({ node: n, distance: this.shadow_to_node(n, direction) }))\n            .reduce(\n                (prev, curr) => {\n                    return prev.distance < curr.distance ? prev : curr;\n                },\n                { node: this.jm.get_root(), distance: Number.MAX_VALUE }\n            ).node;\n    }\n\n    shadow_on_target_side(node, dir) {\n        return (\n            (dir == jsMind.direction.right && this.shadow_to_right_of_node(node) > 0) ||\n            (dir == jsMind.direction.left && this.shadow_to_left_of_node(node) > 0)\n        );\n    }\n\n    shadow_to_right_of_node(node) {\n        return this.shadow_p_x - node.get_location().x - node.get_size().w;\n    }\n\n    shadow_to_left_of_node(node) {\n        return node.get_location().x - this.shadow_p_x - this.shadow_w;\n    }\n\n    shadow_to_base_line_of_node(node) {\n        return this.shadow_p_y + this.shadow_h / 2 - node.get_location().y - node.get_size().h / 2;\n    }\n\n    shadow_to_node(node, dir) {\n        let distance_x =\n            dir === jsMind.direction.right\n                ? Math.abs(this.shadow_to_right_of_node(node))\n                : Math.abs(this.shadow_to_left_of_node(node));\n        let distance_y = Math.abs(this.shadow_to_base_line_of_node(node));\n        return distance_x + distance_y;\n    }\n\n    calc_point_of_node(node, dir) {\n        let ns = node.get_size();\n        let nl = node.get_location();\n        let node_x = node.isroot\n            ? nl.x + ns.w / 2\n            : nl.x + (ns.w * (1 + dir)) / 2 + this.options.line_width * dir;\n        let node_y = nl.y + ns.h / 2;\n        let shadow_x =\n            this.shadow_p_x + (this.shadow_w * (1 - dir)) / 2 - this.options.line_width * dir;\n        let shadow_y = this.shadow_p_y + this.shadow_h / 2;\n        return {\n            sp: { x: shadow_x, y: shadow_y },\n            np: { x: node_x, y: node_y },\n        };\n    }\n\n    move_node(src_node, target_node, target_direct) {\n        var shadow_h = this.shadow.offsetTop;\n        if (!!target_node && !!src_node && !jsMind.node.inherited(src_node, target_node)) {\n            // lookup before_node\n            var sibling_nodes = target_node.children;\n            var sc = sibling_nodes.length;\n            var node = null;\n            var delta_y = Number.MAX_VALUE;\n            var node_before = null;\n            var beforeid = '_last_';\n            while (sc--) {\n                node = sibling_nodes[sc];\n                if (node.direction == target_direct && node.id != src_node.id) {\n                    var dy = node.get_location().y - shadow_h;\n                    if (dy > 0 && dy < delta_y) {\n                        delta_y = dy;\n                        node_before = node;\n                        beforeid = '_first_';\n                    }\n                }\n            }\n            if (!!node_before) {\n                beforeid = node_before.id;\n            }\n            this.jm.move_node(src_node.id, beforeid, target_node.id, target_direct);\n        }\n        this.active_node = null;\n        this.target_node = null;\n        this.target_direct = null;\n    }\n    jm_event_handle(type, data) {\n        if (type === jsMind.event_type.resize) {\n            this.resize();\n        }\n    }\n}\n\nvar draggable_plugin = new jsMind.plugin('draggable_node', function (jm, options) {\n    var jd = new DraggableNode(jm, options);\n    jd.init();\n    jm.add_event_listener(function (type, data) {\n        jd.jm_event_handle.call(jd, type, data);\n    });\n});\n\njsMind.register_plugin(draggable_plugin);\n"], "names": ["jsMind", "Error", "$", "clear_selection", "w", "getSelection", "removeAllRanges", "d", "selection", "empty", "DEFAULT_OPTIONS", "line_width", "line_color", "line_color_invalid", "lookup_delay", "lookup_interval", "scrolling_trigger_width", "scrolling_step_length", "shadow_node_class_name", "DraggableNode", "jm", "options", "_classCallCheck", "opts", "util", "json", "merge", "this", "version", "e_canvas", "canvas_ctx", "shadow", "shadow_p_x", "shadow_p_y", "shadow_w", "shadow_h", "active_node", "target_node", "target_direct", "client_w", "client_h", "offset_x", "offset_y", "hlookup_delay", "hlookup_timer", "capture", "moved", "canvas_draggable", "get_view_draggable", "view_panel", "view", "e_panel", "view_panel_rect", "key", "value", "create_canvas", "create_shadow", "event_bind", "e_nodes", "append<PERSON><PERSON><PERSON>", "width", "size", "height", "h", "c", "ctx", "getContext", "s", "style", "visibility", "zIndex", "cursor", "opacity", "className", "el", "innerHTML", "left", "top", "backgroundImage", "backgroundSize", "transform", "clientWidth", "clientHeight", "shadow_p", "node_p", "invalid", "lineWidth", "strokeStyle", "lineCap", "clear_lines", "canvas_lineto", "x", "y", "clearRect", "x1", "y1", "x2", "y2", "beginPath", "moveTo", "lineTo", "stroke", "jd", "container", "on", "e", "button", "dragstart", "call", "movementX", "movementY", "drag", "dragend", "get_editable", "view_draggable", "jview", "find_node_element", "target", "disable_view_draggable", "nodeid", "get_binded_nodeid", "node", "get_node", "isroot", "reset_shadow", "getBoundingClientRect", "clientX", "touches", "zoom_current", "offsetLeft", "clientY", "offsetTop", "client_hw", "Math", "floor", "client_hh", "clearTimeout", "clearInterval", "setTimeout", "setInterval", "lookup_target_node", "console", "log", "preventDefault", "show_shadow", "px", "py", "scrollTop", "scrollBy", "bottom", "scrollHeight", "scrollLeft", "right", "scrollWidth", "enable_view_draggable", "src_node", "move_node", "hide_shadow", "tagName", "toLowerCase", "parentNode", "sx", "sy", "target_direction", "get_root_x", "direction", "lookup_overlapping_node_parent", "lookup_close_node", "points", "calc_point_of_node", "inherited", "magnet_shadow", "sp", "np", "root", "get_root", "root_location", "get_location", "root_size", "get_size", "shadowRect", "deltaX", "layout", "hspace", "pspace", "deltaY", "_i", "_points", "length", "p", "n", "lookup_node_parent_by_location", "_this", "elementsFromPoint", "filter", "_newArrowCheck", "bind", "map", "id", "mind", "nodes", "parent", "find", "_this2", "Object", "values", "is_visible", "shadow_on_target_side", "distance", "shadow_to_node", "reduce", "prev", "curr", "Number", "MAX_VALUE", "dir", "shadow_to_right_of_node", "shadow_to_left_of_node", "abs", "shadow_to_base_line_of_node", "ns", "nl", "node_x", "node_y", "sibling_nodes", "children", "sc", "delta_y", "node_before", "beforeid", "dy", "type", "data", "event_type", "resize", "draggable_plugin", "plugin", "init", "add_event_listener", "jm_event_handle", "register_plugin"], "mappings": "61CAEA,IAAKA,EACD,MAAM,IAAIC,MAAM,yBAGpB,IAAMC,EAAIF,EAAOE,EAEXC,EACF,iBAAkBD,EAAEE,EACd,WACIF,EAAEE,EAAEC,eAAeC,iBACvB,EACA,WACIJ,EAAEK,EAAEC,UAAUC,OAClB,EAEJC,EAAkB,CACpBC,WAAY,EACZC,WAAY,kBACZC,mBAAoB,sBACpBC,aAAc,IACdC,gBAAiB,IACjBC,wBAAyB,GACzBC,sBAAuB,GACvBC,uBAAwB,gCAGtBC,EAAa,WA8Bd,SA7BD,SAAAA,EAAYC,EAAIC,gGAASC,MAAAH,GACrB,IAAII,EAAO,CAAA,EACXvB,EAAOwB,KAAKC,KAAKC,MAAMH,EAAMb,GAC7BV,EAAOwB,KAAKC,KAAKC,MAAMH,EAAMF,GAE7BM,KAAKC,QAAU,QACfD,KAAKP,GAAKA,EACVO,KAAKN,QAAUE,EACfI,KAAKE,SAAW,KAChBF,KAAKG,WAAa,KAClBH,KAAKI,OAAS,KACdJ,KAAKK,WAAa,EAClBL,KAAKM,WAAa,EAClBN,KAAKO,SAAW,EAChBP,KAAKQ,SAAW,EAChBR,KAAKS,YAAc,KACnBT,KAAKU,YAAc,KACnBV,KAAKW,cAAgB,KACrBX,KAAKY,SAAW,EAChBZ,KAAKa,SAAW,EAChBb,KAAKc,SAAW,EAChBd,KAAKe,SAAW,EAChBf,KAAKgB,cAAgB,EACrBhB,KAAKiB,cAAgB,EACrBjB,KAAKkB,SAAU,EACflB,KAAKmB,OAAQ,EACbnB,KAAKoB,iBAAmB3B,EAAG4B,qBAC3BrB,KAAKsB,WAAa7B,EAAG8B,KAAKC,QAC1BxB,KAAKyB,gBAAkB,IAC3B,KAAC,CAAA,CAAAC,IAAA,OAAAC,MACD,WACI3B,KAAK4B,gBACL5B,KAAK6B,gBACL7B,KAAK8B,YACT,GAAC,CAAAJ,IAAA,SAAAC,MACD,WACI3B,KAAKP,GAAG8B,KAAKQ,QAAQC,YAAYhC,KAAKI,QACtCJ,KAAKE,SAAS+B,MAAQjC,KAAKP,GAAG8B,KAAKW,KAAKzD,EACxCuB,KAAKE,SAASiC,OAASnC,KAAKP,GAAG8B,KAAKW,KAAKE,CAC7C,GAAC,CAAAV,IAAA,gBAAAC,MACD,WACI,IAAIU,EAAI9D,EAAE8D,EAAE,UACZrC,KAAKP,GAAG8B,KAAKC,QAAQQ,YAAYK,GACjC,IAAIC,EAAMD,EAAEE,WAAW,MACvBvC,KAAKE,SAAWmC,EAChBrC,KAAKG,WAAamC,CACtB,GAAC,CAAAZ,IAAA,gBAAAC,MACD,WACI,IAAIa,EAAIjE,EAAE8D,EAAE,UACZG,EAAEC,MAAMC,WAAa,SACrBF,EAAEC,MAAME,OAAS,IACjBH,EAAEC,MAAMG,OAAS,OACjBJ,EAAEC,MAAMI,QAAU,MAClBL,EAAEM,UAAY9C,KAAKN,QAAQH,uBAC3BS,KAAKI,OAASoC,CAClB,GAAC,CAAAd,IAAA,eAAAC,MACD,SAAaoB,GACT,IAAIP,EAAIxC,KAAKI,OAAOqC,MACpBzC,KAAKI,OAAO4C,UAAYD,EAAGC,UAC3BR,EAAES,KAAOF,EAAGN,MAAMQ,KAClBT,EAAEU,IAAMH,EAAGN,MAAMS,IACjBV,EAAEP,MAAQc,EAAGN,MAAMR,MACnBO,EAAEL,OAASY,EAAGN,MAAMN,OACpBK,EAAEW,gBAAkBJ,EAAGN,MAAMU,gBAC7BX,EAAEY,eAAiBL,EAAGN,MAAMW,eAC5BZ,EAAEa,UAAYN,EAAGN,MAAMY,UACvBrD,KAAKO,SAAWP,KAAKI,OAAOkD,YAC5BtD,KAAKQ,SAAWR,KAAKI,OAAOmD,YAChC,GAAC,CAAA7B,IAAA,cAAAC,MACD,WACS3B,KAAKmB,QACNnB,KAAKI,OAAOqC,MAAMC,WAAa,UAEvC,GAAC,CAAAhB,IAAA,cAAAC,MACD,WACI3B,KAAKI,OAAOqC,MAAMC,WAAa,QACnC,GAAC,CAAAhB,IAAA,gBAAAC,MACD,SAAc6B,EAAUC,EAAQC,GAC5B1D,KAAKG,WAAWwD,UAAY3D,KAAKN,QAAQV,WACzCgB,KAAKG,WAAWyD,YAAcF,EACxB1D,KAAKN,QAAQR,mBACbc,KAAKN,QAAQT,WACnBe,KAAKG,WAAW0D,QAAU,QAC1B7D,KAAK8D,cACL9D,KAAK+D,cAAcP,EAASQ,EAAGR,EAASS,EAAGR,EAAOO,EAAGP,EAAOQ,EAChE,GAAC,CAAAvC,IAAA,cAAAC,MACD,WACI3B,KAAKG,WAAW+D,UAAU,EAAG,EAAGlE,KAAKP,GAAG8B,KAAKW,KAAKzD,EAAGuB,KAAKP,GAAG8B,KAAKW,KAAKE,EAC3E,GAAC,CAAAV,IAAA,gBAAAC,MACD,SAAcwC,EAAIC,EAAIC,EAAIC,GACtBtE,KAAKG,WAAWoE,YAChBvE,KAAKG,WAAWqE,OAAOL,EAAIC,GAC3BpE,KAAKG,WAAWsE,OAAOJ,EAAIC,GAC3BtE,KAAKG,WAAWuE,QACpB,GAAC,CAAAhD,IAAA,aAAAC,MACD,WACI,IAAIgD,EAAK3E,KACL4E,EAAY5E,KAAKP,GAAG8B,KAAKqD,UAC7BrG,EAAEsG,GAAGD,EAAW,aAAa,SAAUE,GAClB,IAAbA,EAAEC,QACFJ,EAAGK,UAAUC,KAAKN,EAAIG,EAE9B,IACAvG,EAAEsG,GAAGD,EAAW,aAAa,SAAUE,GACf,IAAhBA,EAAEI,WAAmC,IAAhBJ,EAAEK,WACvBR,EAAGS,KAAKH,KAAKN,EAAIG,EAEzB,IACAvG,EAAEsG,GAAGD,EAAW,WAAW,SAAUE,GACjCH,EAAGU,QAAQJ,KAAKN,EAAIG,EACxB,IACAvG,EAAEsG,GAAGD,EAAW,cAAc,SAAUE,GACpCH,EAAGK,UAAUC,KAAKN,EAAIG,EAC1B,IACAvG,EAAEsG,GAAGD,EAAW,aAAa,SAAUE,GACnCH,EAAGS,KAAKH,KAAKN,EAAIG,EACrB,IACAvG,EAAEsG,GAAGD,EAAW,YAAY,SAAUE,GAClCH,EAAGU,QAAQJ,KAAKN,EAAIG,EACxB,GACJ,GAAC,CAAApD,IAAA,YAAAC,MACD,SAAUmD,GACN,GAAK9E,KAAKP,GAAG6F,iBAGTtF,KAAKkB,QAAT,CAGAlB,KAAKS,YAAc,KACnBT,KAAKuF,eAAiBvF,KAAKP,GAAG4B,qBAE9B,IAAImE,EAAQxF,KAAKP,GAAG8B,KAChBwB,EAAK/C,KAAKyF,kBAAkBX,EAAEY,QAClC,GAAK3C,EAAL,CAII/C,KAAKuF,gBACLvF,KAAKP,GAAGkG,yBAEZ,IAAIC,EAASJ,EAAMK,kBAAkB9C,GACrC,GAAM6C,EAAQ,CACV,IAAIE,EAAO9F,KAAKP,GAAGsG,SAASH,GAC5B,IAAKE,EAAKE,OAAQ,CACdhG,KAAKiG,aAAalD,GAClB/C,KAAKyB,gBAAkBzB,KAAKsB,WAAW4E,wBACvClG,KAAKS,YAAcqF,EACnB9F,KAAKc,UACAgE,EAAEqB,SAAWrB,EAAEsB,QAAQ,GAAGD,SAAWX,EAAMa,aAAetD,EAAGuD,WAClEtG,KAAKe,UACA+D,EAAEyB,SAAWzB,EAAEsB,QAAQ,GAAGG,SAAWf,EAAMa,aAAetD,EAAGyD,UAClExG,KAAKyG,UAAYC,KAAKC,MAAM5D,EAAGO,YAAc,GAC7CtD,KAAK4G,UAAYF,KAAKC,MAAM5D,EAAGQ,aAAe,GACpB,GAAtBvD,KAAKgB,eACLzC,EAAEE,EAAEoI,aAAa7G,KAAKgB,eAEA,GAAtBhB,KAAKiB,eACL1C,EAAEE,EAAEqI,cAAc9G,KAAKiB,eAE3B,IAAI0D,EAAK3E,KACTA,KAAKgB,cAAgBzC,EAAEE,EAAEsI,YAAW,WAChCpC,EAAG3D,cAAgB,EACnB2D,EAAG1D,cAAgB1C,EAAEE,EAAEuI,aAAY,WAC/BrC,EAAGsC,mBAAmBhC,KAAKN,EAC/B,GAAGA,EAAGjF,QAAQN,gBAClB,GAAGY,KAAKN,QAAQP,cAChBwF,EAAGzD,SAAU,CACjB,CACJ,CAhCA,MAFIgG,QAAQC,IAAI,aAPhB,CA0CJ,GAAC,CAAAzF,IAAA,OAAAC,MACD,SAAKmD,GACD,GAAK9E,KAAKP,GAAG6F,gBAGTtF,KAAKkB,QAAS,CACd4D,EAAEsC,iBACFpH,KAAKqH,cACLrH,KAAKmB,OAAQ,EACb3C,IACA,IAAIgH,EAAQxF,KAAKP,GAAG8B,KAChB+F,GAAMxC,EAAEqB,SAAWrB,EAAEsB,QAAQ,GAAGD,SAAWX,EAAMa,aAAerG,KAAKc,SACrEyG,GAAMzC,EAAEyB,SAAWzB,EAAEsB,QAAQ,GAAGG,SAAWf,EAAMa,aAAerG,KAAKe,SAGrE+D,EAAEyB,QAAUvG,KAAKyB,gBAAgByB,IAAMlD,KAAKN,QAAQL,yBACpDW,KAAKsB,WAAWkG,UAAYxH,KAAKN,QAAQJ,uBAEzCU,KAAKsB,WAAWmG,SAAS,GAAIzH,KAAKN,QAAQJ,uBAC1CU,KAAKe,UAAYf,KAAKN,QAAQJ,sBAAwBkG,EAAMa,cAE5DrG,KAAKyB,gBAAgBiG,OAAS5C,EAAEyB,QAAUvG,KAAKN,QAAQL,yBACvDW,KAAKsB,WAAWkG,UACZxH,KAAKsB,WAAWqG,aACZ3H,KAAKyB,gBAAgBU,OACrBnC,KAAKN,QAAQJ,wBAErBU,KAAKsB,WAAWmG,SAAS,EAAGzH,KAAKN,QAAQJ,uBACzCU,KAAKe,UAAYf,KAAKN,QAAQJ,sBAAwBkG,EAAMa,cAI5DvB,EAAEqB,QAAUnG,KAAKyB,gBAAgBwB,KAAOjD,KAAKN,QAAQL,yBACrDW,KAAKsB,WAAWsG,WAAa5H,KAAKN,QAAQJ,uBAE1CU,KAAKsB,WAAWmG,UAAUzH,KAAKN,QAAQJ,sBAAuB,GAC9DU,KAAKc,UAAYd,KAAKN,QAAQJ,sBAAwBkG,EAAMa,cAE5DrG,KAAKyB,gBAAgBoG,MAAQ/C,EAAEqB,QAAUnG,KAAKN,QAAQL,yBACtDW,KAAKsB,WAAWsG,WACZ5H,KAAKsB,WAAWwG,YACZ9H,KAAKyB,gBAAgBQ,MACrBjC,KAAKN,QAAQJ,wBAErBU,KAAKsB,WAAWmG,SAASzH,KAAKN,QAAQJ,sBAAuB,GAC7DU,KAAKc,UAAYd,KAAKN,QAAQJ,sBAAwBkG,EAAMa,cAEhErG,KAAKI,OAAOqC,MAAMQ,KAAOqE,EAAK,KAC9BtH,KAAKI,OAAOqC,MAAMS,IAAMqE,EAAK,KAC7B/I,GACJ,CACJ,GAAC,CAAAkD,IAAA,UAAAC,MACD,SAAQmD,GACJ,GAAK9E,KAAKP,GAAG6F,eAAb,CAMA,GAHItF,KAAKuF,gBACLvF,KAAKP,GAAGsI,wBAER/H,KAAKkB,QAAS,CAWd,GAV0B,GAAtBlB,KAAKgB,gBACLzC,EAAEE,EAAEoI,aAAa7G,KAAKgB,eACtBhB,KAAKgB,cAAgB,EACrBhB,KAAK8D,eAEiB,GAAtB9D,KAAKiB,gBACL1C,EAAEE,EAAEqI,cAAc9G,KAAKiB,eACvBjB,KAAKiB,cAAgB,EACrBjB,KAAK8D,eAEL9D,KAAKmB,MAAO,CACZ,IAAI6G,EAAWhI,KAAKS,YAChBC,EAAcV,KAAKU,YACnBC,EAAgBX,KAAKW,cACzBX,KAAKiI,UAAUD,EAAUtH,EAAaC,EAC1C,CACAX,KAAKkI,aACT,CACAlI,KAAKyB,gBAAkB,KACvBzB,KAAKmB,OAAQ,EACbnB,KAAKkB,SAAU,CAzBf,CA0BJ,GAAC,CAAAQ,IAAA,oBAAAC,MACD,SAAkBoB,GACd,OACIA,IAAO/C,KAAKP,GAAG8B,KAAKQ,SACpBgB,IAAO/C,KAAKP,GAAG8B,KAAKC,SACpBuB,IAAO/C,KAAKP,GAAG8B,KAAKqD,UAEb,KAEsB,WAA7B7B,EAAGoF,QAAQC,cACJrF,EAEJ/C,KAAKyF,kBAAkB1C,EAAGsF,WACrC,GAAC,CAAA3G,IAAA,qBAAAC,MACD,WACI,IAAI2G,EAAKtI,KAAKI,OAAOkG,WACjBiC,EAAKvI,KAAKI,OAAOoG,UACrB,GAAI8B,IAAOtI,KAAKK,YAAckI,IAAOvI,KAAKM,WAA1C,CAGAN,KAAKK,WAAaiI,EAClBtI,KAAKM,WAAaiI,EAElB,IAAIC,EACAxI,KAAKK,WAAaL,KAAKO,SAAW,GAAKP,KAAKyI,aACtCpK,EAAOqK,UAAUb,MACjBxJ,EAAOqK,UAAUzF,KAEvBvC,EADmBV,KAAK2I,+BAA+BH,IACrBxI,KAAK4I,kBAAkBJ,GAC7D,GAAM9H,EAAa,CACf,IAAImI,EAAS7I,KAAK8I,mBAAmBpI,EAAa8H,GAC9C9E,EAAUrF,EAAOyH,KAAKiD,UAAU/I,KAAKS,YAAaC,GACtDV,KAAKgJ,cAAcH,EAAOI,GAAIJ,EAAOK,GAAIxF,GACzC1D,KAAKU,YAAcA,EACnBV,KAAKW,cAAgB6H,CACzB,CAhBA,CAiBJ,GAAC,CAAA9G,IAAA,aAAAC,MACD,WACI,IAAIwH,EAAOnJ,KAAKP,GAAG2J,WACfC,EAAgBF,EAAKG,eACrBC,EAAYJ,EAAKK,WACrB,OAAOH,EAAcrF,EAAIuF,EAAU9K,EAAI,CAC3C,GAAC,CAAAiD,IAAA,iCAAAC,MAED,SAA+B+G,GAgB3B,IAfA,IAAIe,EAAazJ,KAAKI,OAAO8F,wBACzBlC,EAAIyF,EAAWzF,EAAKyF,EAAWxH,OAAS,EAAIyG,GAAc,EAC1DgB,GAAU1J,KAAKP,GAAGC,QAAQiK,OAAOC,OAAS5J,KAAKP,GAAGC,QAAQiK,OAAOE,QAAUnB,EAC3EoB,EAASL,EAAWtH,OAYxB4H,EAAA,EAAAC,EAXa,CACT,CAAChG,EAAGyF,EAAWxF,GACf,CAACD,EAAGyF,EAAWxF,EAAI6F,EAAS,GAC5B,CAAC9F,EAAGyF,EAAWxF,EAAI6F,GACnB,CAAC9F,EAAI0F,EAAS,EAAGD,EAAWxF,GAC5B,CAACD,EAAI0F,EAAS,EAAGD,EAAWxF,EAAI6F,EAAS,GACzC,CAAC9F,EAAI0F,EAAS,EAAGD,EAAWxF,EAAI6F,GAChC,CAAC9F,EAAI0F,EAAQD,EAAWxF,GACxB,CAACD,EAAI0F,EAAQD,EAAWxF,EAAI6F,EAAS,GACrC,CAAC9F,EAAI0F,EAAQD,EAAWxF,EAAI6F,IAEVC,EAAAC,EAAAC,OAAAF,IAAE,CAAnB,IAAMG,EAACF,EAAAD,GACJI,EAAInK,KAAKoK,+BAA+BF,EAAE,GAAIA,EAAE,IACpD,GAAMC,EACF,OAAOA,CAEf,CACJ,GAAC,CAAAzI,IAAA,iCAAAC,MAED,SAA+BqC,EAAGC,GAAG,IAAAoG,EAAArK,KACjC,OAAOzB,EAAEK,EACJ0L,kBAAkBtG,EAAGC,GACrBsG,OACG,SAAAvG,GAAC,OAAAwG,OAAAH,GAAkB,WAAdrG,EAAEmE,SAAwBnE,EAAElB,YAAc9C,KAAKN,QAAQH,sBAAsB,EAAAkL,KACtFzK,OACC0K,IAAI,SAAA3H,GAAE,OAAAyH,OAAAH,GAAIrK,KAAKP,GAAG8B,KAAKsE,kBAAkB9C,EAAG,EAAA0H,KAACzK,OAC7C0K,IAAI,SAAAC,GAAE,OAAAH,OAAAH,GAAIM,GAAM3K,KAAKP,GAAGmL,KAAKC,MAAMF,EAAG,EAAAF,KAACzK,OACvC0K,IAAI,SAAAP,GAAC,OAAAK,OAAAH,GAAIF,GAAKA,EAAEW,MAAM,EAAAL,KAACzK,OACvB+K,KAAK,SAAAZ,GAAC,OAAAK,OAAAH,GAAIF,CAAC,EAAAM,KAACzK,MACrB,GAAC,CAAA0B,IAAA,oBAAAC,MAED,SAAkB+G,GAAW,IAAAsC,EAAAhL,KACzB,OAAOiL,OAAOC,OAAOlL,KAAKP,GAAGmL,KAAKC,OAC7BN,OAAO,SAAAJ,GAAC,OAAAK,OAAAQ,GAAIb,EAAEzB,WAAaA,GAAayB,EAAEnE,MAAM,EAAAyE,KAACzK,OACjDuK,OAAO,SAAAJ,GAAC,OAAAK,OAAAQ,GAAIhL,KAAKP,GAAGkK,OAAOwB,WAAWhB,EAAE,EAAAM,KAACzK,OACzCuK,OAAO,SAAAJ,GAAC,OAAAK,OAAAQ,GAAIhL,KAAKoL,sBAAsBjB,EAAGzB,EAAU,EAAA+B,KAACzK,OACrD0K,IAAI,SAAAP,GAAC,OAAAK,OAAAQ,GAAK,CAAElF,KAAMqE,EAAGkB,SAAUrL,KAAKsL,eAAenB,EAAGzB,KAAa+B,KAAAzK,OACnEuL,OACG,SAACC,EAAMC,GACH,OADYjB,OAAAQ,GACLQ,EAAKH,SAAWI,EAAKJ,SAAWG,EAAOC,GACjDhB,KACDzK,MAAA,CAAE8F,KAAM9F,KAAKP,GAAG2J,WAAYiC,SAAUK,OAAOC,YAC/C7F,IACV,GAAC,CAAApE,IAAA,wBAAAC,MAED,SAAsBmE,EAAM8F,GACxB,OACKA,GAAOvN,EAAOqK,UAAUb,OAAS7H,KAAK6L,wBAAwB/F,GAAQ,GACtE8F,GAAOvN,EAAOqK,UAAUzF,MAAQjD,KAAK8L,uBAAuBhG,GAAQ,CAE7E,GAAC,CAAApE,IAAA,0BAAAC,MAED,SAAwBmE,GACpB,OAAO9F,KAAKK,WAAayF,EAAKwD,eAAetF,EAAI8B,EAAK0D,WAAW/K,CACrE,GAAC,CAAAiD,IAAA,yBAAAC,MAED,SAAuBmE,GACnB,OAAOA,EAAKwD,eAAetF,EAAIhE,KAAKK,WAAaL,KAAKO,QAC1D,GAAC,CAAAmB,IAAA,8BAAAC,MAED,SAA4BmE,GACxB,OAAO9F,KAAKM,WAAaN,KAAKQ,SAAW,EAAIsF,EAAKwD,eAAerF,EAAI6B,EAAK0D,WAAWpH,EAAI,CAC7F,GAAC,CAAAV,IAAA,iBAAAC,MAED,SAAemE,EAAM8F,GAMjB,OAJIA,IAAQvN,EAAOqK,UAAUb,MACnBnB,KAAKqF,IAAI/L,KAAK6L,wBAAwB/F,IACtCY,KAAKqF,IAAI/L,KAAK8L,uBAAuBhG,KAC9BY,KAAKqF,IAAI/L,KAAKgM,4BAA4BlG,GAE/D,GAAC,CAAApE,IAAA,qBAAAC,MAED,SAAmBmE,EAAM8F,GACrB,IAAIK,EAAKnG,EAAK0D,WACV0C,EAAKpG,EAAKwD,eACV6C,EAASrG,EAAKE,OACZkG,EAAGlI,EAAIiI,EAAGxN,EAAI,EACdyN,EAAGlI,EAAKiI,EAAGxN,GAAK,EAAImN,GAAQ,EAAI5L,KAAKN,QAAQV,WAAa4M,EAC5DQ,EAASF,EAAGjI,EAAIgI,EAAG7J,EAAI,EAI3B,MAAO,CACH6G,GAAI,CAAEjF,EAHNhE,KAAKK,WAAcL,KAAKO,UAAY,EAAIqL,GAAQ,EAAI5L,KAAKN,QAAQV,WAAa4M,EAG3D3H,EAFRjE,KAAKM,WAAaN,KAAKQ,SAAW,GAG7C0I,GAAI,CAAElF,EAAGmI,EAAQlI,EAAGmI,GAE5B,GAAC,CAAA1K,IAAA,YAAAC,MAED,SAAUqG,EAAUtH,EAAaC,GAC7B,IAAIH,EAAWR,KAAKI,OAAOoG,UAC3B,GAAM9F,GAAiBsH,IAAa3J,EAAOyH,KAAKiD,UAAUf,EAAUtH,GAAc,CAQ9E,IANA,IAAI2L,EAAgB3L,EAAY4L,SAC5BC,EAAKF,EAAcpC,OACnBnE,EAAO,KACP0G,EAAUd,OAAOC,UACjBc,EAAc,KACdC,EAAW,SACRH,KAEH,IADAzG,EAAOuG,EAAcE,IACZ7D,WAAa/H,GAAiBmF,EAAK6E,IAAM3C,EAAS2C,GAAI,CAC3D,IAAIgC,EAAK7G,EAAKwD,eAAerF,EAAIzD,EAC7BmM,EAAK,GAAKA,EAAKH,IACfA,EAAUG,EACVF,EAAc3G,EACd4G,EAAW,UAEnB,CAEED,IACFC,EAAWD,EAAY9B,IAE3B3K,KAAKP,GAAGwI,UAAUD,EAAS2C,GAAI+B,EAAUhM,EAAYiK,GAAIhK,EAC7D,CACAX,KAAKS,YAAc,KACnBT,KAAKU,YAAc,KACnBV,KAAKW,cAAgB,IACzB,GAAC,CAAAe,IAAA,kBAAAC,MACD,SAAgBiL,EAAMC,GACdD,IAASvO,EAAOyO,WAAWC,QAC3B/M,KAAK+M,QAEb,+FAAC,CAvac,GA0afC,EAAmB,IAAI3O,EAAO4O,OAAO,kBAAkB,SAAUxN,EAAIC,GACrE,IAAIiF,EAAK,IAAInF,EAAcC,EAAIC,GAC/BiF,EAAGuI,OACHzN,EAAG0N,oBAAmB,SAAUP,EAAMC,GAClClI,EAAGyI,gBAAgBnI,KAAKN,EAAIiI,EAAMC,EACtC,GACJ,IAEAxO,EAAOgP,gBAAgBL"}