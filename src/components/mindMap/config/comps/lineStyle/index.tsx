import React, { useMemo, useState } from 'react';
import { Select } from '@weapp/ui';
import { getLabel } from '@weapp/utils';
import { ebdfClsPrefix } from '../../../../../constants';
import { MIND_VIEW, MIND_LINE_STYLE } from '../../../../../constants/enum';
import { MindMapConfigData } from '../../../types';
import './index.less';

interface IProps {
  weId?: string;
  value: boolean;
  onChange?: (value: MIND_LINE_STYLE) => void;
  onConfigChange?: (configs: any) => void;
  config: MindMapConfigData;
}

const cls = `${ebdfClsPrefix}-layoutSet`;

const getLineStyles = () => [
  {
    id: 'curve',
    content: getLabel('288327', '曲线'),
  },
  {
    id: 'straight',
    content: getLabel('288328', '直线'),
  },
  {
    id: 'direct',
    content: getLabel('288329', '直连'),
  },
];

const LineStyle = (props: IProps) => {
  const { config } = props;
  const [layoutValue, setLayoutValue] = useState(config?.mindLayout || MIND_VIEW.LOGICAL_STRUCTURE);

  const lineStyles = useMemo(() => {
    const layout = props.config?.mindLayout || MIND_VIEW.LOGICAL_STRUCTURE;
    if (layout !== layoutValue) {
      setLayoutValue(layout);
    }
    if (layout === MIND_VIEW.ORGANIZATION_STRUCTURE) {
      return getLineStyles().filter((i) => i.id !== 'curve');
    }
    return getLineStyles();
  }, [layoutValue, props]);

  const line = useMemo(() => {
    const lineValue = props.config?.lineStyle || MIND_LINE_STYLE.CURVE;
    return lineValue;
  }, [props]);

  const setLineStyle = (lineStyle: any) => {
    props.onConfigChange?.({ lineStyle });
  };

  return (
    <div className={cls}>
      <Select weId={`${props.weId || ''}_542ljn`} data={lineStyles} value={line} onChange={setLineStyle} />
    </div>
  );
};

LineStyle.displayName = 'LineStyle';

export default LineStyle;
