/**
 * <AUTHOR>
 * @createTime 2022 -11 -28
 */
import { Dialog, MDialog } from '@weapp/ui';
import { getLabel } from '@weapp/utils';
import { getLocaleValueString } from '../locale/utils';

export interface EbConfirmProps {
  title?: string;
  content: string;
  okText?: string;
  cancelText?: string;
  onCancel?: Function;
  onOk?: Function;
}

export const confirmAction = (info: EbConfirmProps, isMobile: boolean, resolve: Function) => {
  let {
    title = getLabel('105946', '信息确认'),
    okText = getLabel('40565', '确定'),
    cancelText = getLabel('53937', '取消'),
    content = '',
  } = info;
  title = getLocaleValueString(title);
  okText = getLocaleValueString(okText);
  cancelText = getLocaleValueString(cancelText);
  content = getLocaleValueString(content);
  if (isMobile) {
    MDialog.prompt({
      title,
      mask: true,
      maskClosable: false,
      message: content,
      // description: "说明性文字",
      prompt: false,
      onOk: () => {
        resolve?.();
      },
      onClose: () => {
        resolve?.({ interrupt: true });
        info.onCancel?.();
      },
      okText,
      cancelText,
    });
  } else {
    Dialog.confirm({
      title,
      content,
      okText,
      cancelText,
      onOk: () => {
        resolve?.();
      },
      onCancel: () => {
        resolve?.({ interrupt: true });
        info.onCancel?.();
      },
    });
  }
};

export default {};
