import { observer } from 'mobx-react';
import React, { PureComponent } from 'react';
import { withRouter } from 'react-router-dom';
import { CorsComponent } from '@weapp/ui';


const mockJson = () => {
  const appId = '920881378315517953';
  const objId = '920881438343708679';
  const id = '992818794746552321';
  const pageId = '992818794746552322';
  return {
    appId,
    id,
    objId,
    pageId,
  };
};
@observer
class EbFormEngine extends PureComponent<any, any> {
  render() {
    return <>
       <CorsComponent weId={`${this.props.weId || ''}_guvsjq`}
        app="@weapp/ebdboard"
        compName="AdvanceFormEngine"
        type='MindMap'
        advancedView='mindMap'
        warehouse='@weapp/ebdmindmap'
        customJson={mockJson()}
      />
    </>
  }
}

export default withRouter(EbFormEngine);
