import cleanup from 'rollup-plugin-cleanup';
import terser from '@rollup/plugin-terser';
import json from '@rollup/plugin-json';
import babel from '@rollup/plugin-babel';
import resolve from '@rollup/plugin-node-resolve';
import commonjs from '@rollup/plugin-commonjs';
import filesize from 'rollup-plugin-filesize';
import replace from '@rollup/plugin-replace';
import copy from 'rollup-plugin-copy';
import { outPath } from './localConfig.js';

const babelConfig = {
  // 转义自身代码
  exclude: 'node_modules/**',
  babelHelpers: 'bundled',
  presets: [
    [
      '@babel/env',
      {
        targets: {
          browsers: '> 0.5%, ie >= 10',
        },
        modules: false,
        // polyfill按需引入
        useBuiltIns: 'usage',
      },
    ],
  ],
};
const rollUpConfig = [
  {
    input: '../simple-mind-map/full.js',
    output: {
      name: 'simpleMindMap',
      file: 'dist/mind.js',
      format: 'umd',
      banner: '',
      sourcemap: false,
    },
    plugins: [
      // replace({
      // 	__env__: JSON.stringify(process.env.ENV) // 注意, 这里需要将变量序列化
      // }),
      json(),
      // 处理外部依赖
      resolve(),
      // 支持基于 CommonJS 模块引入
      commonjs(),
      // cleanup({
      //   comments: 'none',
      // }),
      // babel(babelConfig),
      // terser({
      //   output: {
      //     comments: 'all',
      //   }
      // }),
      filesize(),
      copy({
        targets: [{ src: 'dist/mind.js', dest: outPath }],
        hook: 'writeBundle',
        verbose: true,
      }),
    ],
  },
];
export default rollUpConfig;
