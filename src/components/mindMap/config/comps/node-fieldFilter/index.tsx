import React from 'react';
import { observer } from 'mobx-react';
import { Switch, Button, Dialog, Checkbox, Table, ITableColumn, Icon, Select, Locale, LocaleLangType } from '@weapp/ui';
import { getLabel, isEmpty, cloneDeep } from '@weapp/utils';
// import isEmpty from 'lodash-es/isEmpty';
// import cloneDeep from 'lodash-es/cloneDeep';
import { FieldFilterSetProps, FieldFilterSetListProps } from './types';
import { ebdfClsPrefix } from '../../../../../constants';
import ebdcoms from '../../../../../utils/ebdcoms';
import utils, { removeHtmlTag } from '../../../../../utils';
import { sortNodeTreeInfo } from './utils';
import './index.less';

interface Props {
  value?: FieldFilterSetProps;
  config?: any & FieldFilterSetProps;
  onChange?: (changeData: FieldFilterSetProps) => void;
  onConfigChange?: (changeData: any) => void;
  isRoot?: boolean;
  appid?: string;
  pageId?: string;
}
const cls = `${ebdfClsPrefix}-fieldFilterSet`;
const { LocaleEx } = ebdcoms.get();
const positionArr = () => [
  {
    id: 'tile',
    content: getLabel('55658', '平铺'),
  },
  {
    id: 'drop',
    content: getLabel('97635', '下拉'),
  },
];
const DefaultPosition = 'tile';
@observer
class NodeFieldFilterSet extends React.PureComponent<Props> {
  isCn = window.TEAMS?.locale?.lang === 'zh_CN';
  state = {
    visible: false,
    enable: false,
    saveLocalEnable: true,
    controlList: [] as FieldFilterSetListProps[],
    fieldList: [] as FieldFilterSetListProps[],
  };
  componentDidMount() {
    const { fieldFilterSet } = this.props.config;
    if (!isEmpty(fieldFilterSet)) {
      const _fieldFilterSet = cloneDeep(fieldFilterSet);
      this.setState({ enable: _fieldFilterSet.enable, saveLocalEnable: _fieldFilterSet.saveLocalEnable });
    }
    this.setInitControlList();
  }
  setInitControlList = (props = this.props) => {
    const { fieldFilterSet = {} } = props.config;
    const _fieldFilterSet = cloneDeep(fieldFilterSet);
    const { controlList = [] } = _fieldFilterSet;
    let list: any = [];
    const nodeTreeInfo = sortNodeTreeInfo(props.config);
    for (const key in nodeTreeInfo) {
      const { showFields = {} } = nodeTreeInfo[key];
      // 表单高级视图入口 的showFields为字符串 需要兼容
      const _showFields = typeof showFields === 'string' ? (JSON.parse(showFields || '{}')) : showFields;
      const cardLayout = _showFields.cardLayout;
      if (!isEmpty(cardLayout)) {
        cardLayout.grid.forEach((grids: any) => {
          grids.forEach((row: any) => {
            row.forEach((i: any) => {
              // 设置过值的老数据
              const controlItem = controlList.find((k: any) => k.id === i.field.uid) || {};
              const { visible = false, position = DefaultPosition } = controlItem || {}
              const _props = {
                visible,
                position
              }
              // 单独处理自定义文本
              // 过滤掉变量类型的字段（无法支持）
              if (i.field.id === '-1') {
                const inValid = ['{', '}', '[', ']']
                const isValid = inValid.every((v) => i.field.content.indexOf(v) === -1)
                if (isValid) {
                  list.push({
                    text: controlItem.text || removeHtmlTag(i.field.content),
                    nodeText: nodeTreeInfo[key].name,
                    id: i.field.uid,
                    ..._props,
                  });
                }
              } else {
                const item = _showFields.field.find((f: any) => f.uid === i.field.uid);
                if (item && item.text) {
                  list.push({
                    text: controlItem.text || item.text,
                    nodeText: nodeTreeInfo[key].name,
                    id: item.uid,
                    ..._props,
                  });
                }
              }
            });
          });
        });
      }
    }
    this.setState({ fieldList: list });
  };
  changeVisible = (visible: boolean) => {
    this.setState({ visible });
    if (visible) {
      this.setInitControlList();
    }
  };
  onSure = () => {
    const { onChange, onConfigChange } = this.props;
    const { fieldList, enable, saveLocalEnable } = this.state;
    this.changeVisible(false);
    // 只使用勾选的
    // const controlList = fieldList.filter((i) => i.visible);
    const controlList = fieldList
    const payload: FieldFilterSetProps = {
      enable,
      saveLocalEnable,
      controlList,
      uniqueKey: utils.UUID(),
    };
    onConfigChange && onConfigChange({ fieldFilterSet: payload });
    setTimeout(() => {
      onChange && onChange(cloneDeep(payload));
    }, 0);
  };
  changeVal = (target: any, type: string, val: any) => {
    const { fieldList } = this.state;
    const idx = fieldList.findIndex((i) => i.id === target.id);
    if (idx > -1) {
      // @ts-ignore
      fieldList[idx][type] = val;
      this.setState({ fieldList: [...fieldList] });
    }
  };
  checkAll = () => {
    const { fieldList } = this.state;
    const _fieldList = [...fieldList];
    const isCheckAll = fieldList.every((i) => i.visible);
    _fieldList.forEach((i) => {
      i.visible = !isCheckAll;
    });
    this.setState({ fieldList: [..._fieldList] });
  };
  renderForm = () => {
    const { visible, enable, fieldList, saveLocalEnable } = this.state;
    // if (!visible) return null;
    const columns: ITableColumn[] = [
      {
        title: getLabel('144575', '字段名'),
        dataIndex: 'text',
        align: 'center',
        textAlign: 'center',
        width: this.isCn ?' 35%' : '30%',
        bodyRender: (data) => {
          return <>
            <LocaleEx weId={`${this.props.weId || ''}_rt3dc3`} targetId={this.props.pageId} value={data.text} disabled={data.hide} placeholder={getLabel('56234', '请输入')} onChange={(val: any) => this.changeVal(data, 'text', val)} />
          </>;
        },
      },
      {
        title: getLabel('87166', '节点名称'),
        dataIndex: 'nodeText',
        align: 'center',
        textAlign: 'center',
        width: '20%',
      },
      {
        title: getLabel('54603', '显示位置'),
        dataIndex: 'position',
        align: 'center',
        textAlign: 'center',
        isCustomTitle: true,
        width: this.isCn ? '30%' : '25',
        bodyRender: (data) => {
          const value = fieldList.find((i) => i.id === data.id);
          const isCn = window.TEAMS?.locale.lang === 'zh_CN';
          return <Select weId={`${this.props.weId || ''}_vwandj`} data={positionArr()} value={value?.position} onChange={(val) => this.changeVal(data, 'position', val)} style={{width: isCn ? 70 : 100}} />;
        },
      },
      {
        title: getLabel('56110', '是否显示'),
        dataIndex: 'enable',
        align: 'left',
        textAlign: 'center',
        width: this.isCn ? '15%' : '25',
        customRenderTh: (column: any) => {
          const isCheckAll = fieldList.every((i) => i.visible);
          return (
            <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
              <Checkbox weId={`${this.props.weId || ''}_7xo6ec`} value={isCheckAll} onChange={() => this.checkAll()} style={{ marginRight: '4px' }} />
              {column.title}
            </div>
          );
        },
        bodyRender: (data) => {
          return <Checkbox weId={`${this.props.weId || ''}_2vs427`} value={data.visible} onChange={() => this.changeVal(data, 'visible', !data.visible)} style={{ marginLeft: '-34px' }} />;
        },
      },
    ];
    const renderTable = () => {
      return <Table weId={`${this.props.weId || ''}_4dlo0e`} columns={columns} data={fieldList} />;
    };
    const renderSwitch = () => {
      return (
        <div className={`${cls}-switch`}>
          <div className={`${cls}-switch-left`}>
            <div className={`${cls}-switch-left-label`}>{getLabel('255817', '开启卡片字段显隐设置')}</div>
            <div className={`${cls}-switch-left-desc`}>{getLabel('255818', '开启后，可在页面上配置选中的字段是否显示')}</div>
          </div>
          <div className={`${cls}-switch-right`}>
            <Switch
              weId={`${this.props.weId || ''}_78b95p`}
              value={enable}
              onChange={() => {
                this.setState((prevState: any) => ({ enable: !prevState.enable }));
              }}
              size='sm'
            />
          </div>
        </div>
      );
    };
    const renderSaveSwitch = () => {
      return (
        <div className={`${cls}-switch`}>
          <div className={`${cls}-switch-left`}>
            <div className={`${cls}-switch-left-label`}>{getLabel('255819', '记住选中配置')}</div>
            <div className={`${cls}-switch-left-desc`}>{getLabel('255820', '开启后，会记住当前操作的字段勾选结果')}</div>
          </div>
          <div className={`${cls}-switch-right`}>
            <Switch
              weId={`${this.props.weId || ''}_78b95p`}
              value={saveLocalEnable}
              onChange={() => {
                this.setState((prevState: any) => ({ saveLocalEnable: !prevState.saveLocalEnable }));
              }}
              size='sm'
            />
          </div>
        </div>
      );
    };
    return (
      <Dialog
        weId={`${this.props.weId || ''}_p401ie`}
        title={getLabel('255252', '卡片字段显隐设置')}
        closable
        width={700}
        onClose={() => this.changeVisible(false)}
        destroyOnClose
        visible={visible}
        wrapClassName={`${cls}-dialog`}
        mask
        footer={[
          <Button weId={`${this.props.weId || ''}_9le4rz`} key='sure' type='primary' onClick={this.onSure}>
            {getLabel('40565', '确定')}
          </Button>,
          <Button weId={`${this.props.weId || ''}_4assuu`} key='cancel' onClick={() => this.changeVisible(false)}>
            {getLabel('53937', '取消')}
          </Button>,
        ]}
      >
        <div>
          {renderSwitch()}
          {enable && renderSaveSwitch()}
          {enable && renderTable()}
        </div>
      </Dialog>
    );
  };
  render() {
    const { visible } = this.state;
    return (
      <div className={cls}>
        {/* <Button onClick={() => this.changeVisible(true)} size={this.isCommonConfig() ? 'small' : 'middle'}>
          {getLabel('54205', '设置')}
        </Button> */}
        <Icon weId={`${this.props.weId || ''}_8k3tt7`} name='Icon-set-up-o' onClick={() => this.changeVisible(true)}></Icon>
        {visible && this.renderForm()}
      </div>
    );
  }
}
export default NodeFieldFilterSet;
