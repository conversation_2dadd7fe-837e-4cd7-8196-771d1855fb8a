---
key: MindMap
title: 思维导图
type: dataPresentation
person: 庞晓峰
assistant: [李劲松]
design: [设计图1]
issue: ""
demoInfo:
  - title: 基础用法
imports:
  - "./basic.tsx"
---

## API

### 一、参数说明

| 参数 | 说明 | 类型 | 是否必填 | 默认值 |
| - | - | - | - | - |
| config | 配置项 | Config | 是 | 参考【MindMapConfig 配置说明】 |

#### 1、MindMapConfig 配置说明

| 参数        | 说明                                                         | 类型          | 是否必填 | 默认值 |
| ----------- | ------------------------------------------------------------ | ------------- | -------- | -----: |
| id | 数据id                                                       | string     | 是       |        |
| title | 标题                                                         | string | ReactNode     | 是       |        |
| titleEnabled | 是否显示标题                                            | boolean     | 否       |        |
| footerEnabled | 是否显示底部按钮                                        | boolean     | 否       |        |
| funcSetting | 功能设置 |   参考FuncSettingProps   | 否       |        |   |
| nodeTreeInfo | 节点详情配置 | AnyObj | 是 | |
| nodeTreeList | 节点信息 | 参考NodeTreeListProps | 是 |  |
| isEngine | 是否是表单后台引擎 | boolean     | 否       |        |

#### 2、FuncSettingProps 配置说明
| 参数 | 说明 | 类型 | 是否必填 | 默认值 |
| - | - | - | - | - |
| base | 基础设置（新版本支持） | MindFuncBaseProps | 否 | - |
| toolbar | 工具栏配置 | MindFuncToolBarItemProps | 否 | - |

##### 2.1 MindFuncBaseProps 配置说明（新版本支持）
| 参数 | 说明 | 类型 | 是否必填 | 默认值 |
| - | - | - | - | - |
| isShowExpandNum | 是否显示带数量的收起按钮 | boolean | 否 | false |

##### 2.2 MindFuncToolBarItemProps 配置说明
| 参数 | 说明 | 类型 | 是否必填 | 默认值 |
| - | - | - | - | - |
| scale | 界面缩放 | boolean | 否 | true |
| expand_all | 全部展开 | boolean | 否 | false |
| collapse_all | 全部折叠 | boolean | 否 | false |
| goRoot | 回到根节点（新版本） | boolean | 否 | false |
| fullScreen | 全屏显示（新版本） | boolean | 否 | false |


#### 3、NodeTreeListProps 配置说明

| 参数 | 说明 | 类型 | 是否必填 | 默认值 |
| - | - | - | - | - |
| name | 节点名 | string | 是 | - |
| id | 节点id | string | 是 | - |
| pid | 父节点id | string | 否 | - |
| floor | 层级 | number | 否 | - |
| children | 子节点数据 | NodeTreeListProps[] | 否 | - |

### 二、事件说明

> 使用方式以低代码平台支持的能力为准

```
export enum EbdMindMapEventKeys {
  onDidMount = 'onDidMount', // 加载完成
  onResize = 'onResize', // 内容大小变化
  onNodeClick = 'onNodeClick', // 节点点击事件
  onSetExpandLevel = 'onSetExpandLevel', // 支持展开层级
  onChangeTargetNodeVisible = 'onChangeTargetNodeVisible', // 设置节点可见
}

```

#### 2.1 案例中的组件实例（comp）在代码块中的获取方法
```
const ebSdk = window.ebuilderSDK;  //全局SDK
const pageSdk = ebSdk.getPageSDK();  //当前页面SDK
const comp = pageSdk.getCompInstance("组件id");
//注：在事件动作中的javascript事件内不需要定义pageSdk（默认已存在直接使用即可）
```


#### 2.2 使用 events 触发思维导图事件
```

const ebSdk = window.ebuilderSDK;  //全局SDK
const pageSdk = ebSdk.getPageSDK();  //当前页面SDK
//注：在事件动作中的javascript事件内不需要定义pageSdk（默认已存在直接使用即可）
//1.加载完成
pageSdk.events.on(`${onDidMount}_${compId}`, '组件id', ({ store, jmInstance }) => {});
//2.插件更新时
pageSdk.events.on(`onPluDidUpdate_${compId}`,'组件id', (props) => {})
//3.设置思维导图默认值
pageSdk.events.emit(`onSetMindData_${compId}`,'组件id', '自定义思维导图值')
//4.内容大小变化
pageSdk.events.on(`onResize_${compId}`,'组件id', (sizeInfo) => {})
//5.节点点击事件
pageSdk.events.on(`onNodeClick_${compId}`,'组件id', (node, e) => {})
//6.设置展开层级
pageSdk.events.emit(`onSetExpandLevel_${compId}`,'组件id', 1)
//7.设置节点可见（暂时只支持隐藏根节点）
pageSdk.events.emit(`onChangeTargetNodeVisible_${compId}`,'组件id', { id: '-1', visible: 'true/false' })
```

### 三、插件包相关
#### 3.1 插件包相关API
> 此方案Design视图不支持

> 根据具体的业务场景，可通过扩展组件插件包或者 ecode 的方式，使用组件预留的自定义 API，对组件功能做自定义复写

| 参数               | 描述                                     | 类型                                                                                                 | 最低支持版本 | 最低支持项目版本 |
| ------------------ | ---------------------------------------- | ---------------------------------------------------------------------------------------------------- | ------------ | ---------------- |
| getData  | 自定义拦截思维导图数据并返回新处理的数据 API       | (data: TreeDataItem) => TreeDataItem                                                         | 10.0.2402.01 |                  |
| onDidMount       | 加载完成 API | (store: any, jmInstance: 思维导图实例) => void                                                    | 10.0.2312.02 |                  |
| onResize  | 容器内容大小变化 API                 | (sizeInfo: {wrapperWidth: number, wrapperHeight: number}) => () => void | 10.0.2312.02 |                  |
| onNodeClick      | 节点点击事件 API             | ({node: 当前节点, e: any}) => () => void            | 10.0.2312.02 |                  |
| onSetExpandLevel      | 设置展开层级 API             | (setFunc: Function) => () => void            | 10.0.2312.02 |                  |
| onChangeTargetNodeVisible      | 隐藏指定节点 API（暂时只支持隐藏根节点）             | (setFunc: Function) => () => void            | 10.0.2312.02 |                  |
| renderCardBefore      | 自定义渲染卡片左侧区域 API             | (node: AnyObj) => () => ReactNode            | 10.0.2312.02 |                  |
| renderCardAfter | 自定义渲染卡片右侧区域 API   | (node: AnyObj) => () => ReactNode                                | 10.0.2312.02 |                  |
| renderContentTop | 自定义渲染头部区域 API   | () => () => ReactNode                                | 10.0.2312.02 |                  |
| renderContentBottom | 自定义渲染底部区域 API   | () => () => ReactNode                                | 10.0.2312.02 |                  |
| renderContentBefore | 自定义渲染左侧区域 API   | () => () => ReactNode                                | 10.0.2312.02 |                  |
| renderContentAfter | 自定义渲染右侧区域 API   | () => () => ReactNode                                | 10.0.2312.02 |                  |
| renderCustomMindMap | 自定义渲染思维导图   | () => (mindData: MindData, ele: ReactElement) => ReactNode                                | 10.0.2405.01 |                  |


##### 3.1.1 TreeDataItem声明
```
export interface TreeDataItem {
  id: string,
  content: string,
  parentid: string,
  disabled?: boolean,
  disabledCheck?: boolean,
  isLeaf?: boolean,
  customParameters: AnyObj,
  children: TreeDataItem[]
  title?: string,
  icon?: any,
  floor: number
  defaultExpendDepth?: number
  appid?:string
}
```

##### 3.1.2 MindData声明
```
export interface MindData {
  /* 元数据，定义思维导图的名称、作者、版本等信息 */
  meta: {
    name: string;
    author?: string;
    version?: string;
  };
  /* 数据格式声明 */
  format: MindFormatType;
  data: TreeDataItem[];
  supportHtml?: boolean;
}
```

#### 3.2 声明自定义插件，及插件中需要自定义实现复写的 API
```
class DemoPlugin {
  // 插件名称，唯一标识，目前版本仅支持"mindMap_plugin"
   name = 'mindMap_plugin'
   // 具体自定义复写的API
   renderContent = (hook) => {
    //...
   }
 }
```

#### 3.3 注册自定义实现的插件到组件中
```
// 可通过组件类型针对某个类型的组件作用插件
eventEmitter.on('@weapp/designer', 'plugin_center.ready.MindMap', ({ registerPlugin }) => {
  registerPlugin(DemoPlugin);
});
// 或者通过组件id针对某个具体的组件作用插件
eventEmitter.on('@weapp/designer', 'plugin_center.ready.ea316003dc384190b1e961b6cc8b0d51', ({ registerPlugin }) => {
  registerPlugin(DemoPlugin);
});
```

#### 3.4 使用插件包触发思维导图事件案例
```
import { eventEmitter } from '@weapp/utils';

class MindMapPlugin {
  name = 'mindMap_plugin'; // 插件名称，唯一标识
  compId = 'e3526cdc93184f3eac8387c8e69d51b5'
  opts = {}; //此为组件实例
  constructor(opts) {
    // 可以通过opts.com.props拿到组件实例props
    // 可以通过opts.com.forceUpdate强制更新
    // 通过opts.com.props.config获取组件配置
    this.opts = opts;
  }
  onDidMount = (args) => {};
  onResize = (args) => {};
  getData = (data) => {
    // 拿到数据后操作返回新的数据...
    /*
      1.插件机制规范，返回值放在数组内
      2.getData返回值第一个为更新后的data
    */
    return data
  };
  onNodeClick = (data) => {};
  // 指定层级
  onSetExpandLevel = (hook) => {
    // hook(-1)
  };
  // 隐藏指定节点API（暂时只支持隐藏根节点）
  onChangeTargetNodeVisible = (hook) => {
    // hook({id: '-1', visible: false})
  };
  renderCardContent = (props) => {
    return (...args) => (
      <>
        <div>自定义渲染</div>
        {props.renderMain(...args)}
      </>
    );
  };
  renderCardBefore = () => {
    return (datas, ...args) => (
      <>
        <div>自定义渲染-左边</div>
      </>
    );
  };
  renderCardAfter = () => {
    return (datas, ...args) => (
      <>
        <div>自定义渲染-右边</div>
      </>
    );
  };
  renderContentTop = () => {
    return () => (
      <>
        <div>自定义渲染-头部</div>
      </>
    );
  };
  renderContentBottom = () => {
    return () => (
      <>
        <div>自定义渲染-底部</div>
      </>
    );
  };
  renderContentBefore = () => {
    return () => (
      <>
        <div>自定义渲染-内容-左侧</div>
      </>
    );
  };
  renderContentAfter = () => {
    return () => (
      <>
        <div>自定义渲染-内容-右侧</div>
      </>
    );
  };
  renderCustomMindMap = () => {
    const _this = this;
    return (mindData, ele) => {
      if (_this.opts.com.props.id === compId) {
        return <div style={{ width: '100%', height: '100%' }}>
          自定义渲染思维导图
        </div>;
      } else {
        return ele
      }
    };
  };
}
eventEmitter.once('@weapp/designer', `plugin_center.ready.${compId}`, ({ registerPlugin }) => {
  registerPlugin(MindMapPlugin);
});

```