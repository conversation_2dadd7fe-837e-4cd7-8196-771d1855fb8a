import { Attributes } from 'react';

export type TreeData = {
  name: any;
  pid: string;
  id: string;
  floor: number;
  children?: TreeData[];
  [key: string]: any;
};

export interface TreeBoxProps extends Attributes {
  data: TreeData[];
  selector: any;
  onCreate: (data: TreeData) => void;
  onRemove: (data: TreeData) => void;
  onNodeOptClick: (data: TreeData) => void;
}

export interface TreeNodeProps extends Omit<TreeBoxProps, 'data'> {
  node: TreeData;
  data?: any;
}

export interface TableTreeProps extends TreeBoxProps {}
