import { Provider } from 'mobx-react';
import React, { PureComponent } from 'react';
import Content from './Content';
import { MindMapEngineStore } from './store';

export default class MindMapEngine extends PureComponent<any> {
  store = new MindMapEngineStore();

  render() {
    return (
      <Provider weId={`${this.props.weId || ''}_fjts78`} store={this.store}>
        <Content weId={`${this.props.weId || ''}_n6e2tt`} isEngine {...this.props} />
      </Provider>
    );
  }
}
