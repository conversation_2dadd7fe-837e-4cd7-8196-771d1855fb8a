import { middleware, isEmpty, isEqual, cloneDeep } from '@weapp/utils';
import { Checkbox, Select, Locale } from '@weapp/ui';
import { observer } from 'mobx-react';
import React, { PureComponent } from 'react';
import { When } from 'react-if';
import { withRouter } from 'react-router-dom';
// import { isEmpty, isEqual, cloneDeep } from 'lodash-es';
import { appName, ebdfClsPrefix, EbdMindMapEventKeys } from '../../../../../constants';
import { MindMapViewProps } from '../types';
import { FieldsVisibleSetItemProps } from './types';
import { FieldFilterSetProps } from '../../../config/comps/node-fieldFilter/types';
import { sortNodeTreeInfo } from '../../../config/comps/node-fieldFilter/utils';
import './style/index.less';

const cls = `${ebdfClsPrefix}-view-comps-fieldsVisibleSet`;
const INITIAL_STATE = () => ({
  tileFields: [] as FieldsVisibleSetItemProps[],
  dropFields: [] as FieldsVisibleSetItemProps[],
  allFields: [] as FieldsVisibleSetItemProps[],
  tileKeys: [] as string[],
  dropKeys: [] as string[],
});
const formatValueForSelect = (data: FieldsVisibleSetItemProps[]) => {
  return data.map((i) => {
    return {
      id: i.id,
      content: `${i.text}（${i.nodeText}）`,
    };
  });
};
@middleware(appName, 'FieldsVisibleSet')
@observer
class FieldsVisibleSet extends PureComponent<MindMapViewProps & React.Attributes & { disabled?: boolean }> {
  state = INITIAL_STATE();
  componentDidMount() {
    this.init(this.props, true);
  }
  componentWillReceiveProps(nextProps: Readonly<MindMapViewProps & React.Attributes & { disabled?: boolean }>) {
    if (!isEqual(this.props.config.nodeTreeInfo, nextProps.config.nodeTreeInfo) || !isEqual(this.props.config.nodeTreeList, nextProps.config.nodeTreeList) || !isEqual(this.props.config.fieldFilterSet, nextProps.config.fieldFilterSet)) {
      this.init(nextProps);
    }
  }
  componentWillUnmount() {
    this.setState({ ...INITIAL_STATE() });
  }
  reset = () => {
    this.setState({ allFields: [] });
  };
  init = (props = this.props, isInit?: boolean) => {
    const { config, isDesign } = props;
    const fieldFilterSet: FieldFilterSetProps = config.fieldFilterSet!;
    const { controlList, saveLocalEnable } = fieldFilterSet;
    const { events, compId } = this.props;
    const _controlList = controlList.filter((k) => k.visible);
    // if (isEmpty(controlList)) return;
    let fields: any = [];
    const nodeTreeInfo = sortNodeTreeInfo(config);
    for (const key in nodeTreeInfo) {
      const { showFields = {} } = nodeTreeInfo[key];
      // 表单高级视图入口 的showFields为字符串 需要兼容
      const _showFields = typeof showFields === 'string' ? JSON.parse(showFields || '{}') : showFields;
      if (_showFields && _showFields.field) {
        if (!isEmpty(_showFields)) {
          _showFields.cardLayout.grid.forEach((grids: any) => {
            grids.forEach((row: any) => {
              row.forEach((i: any) => {
                // 要带顺序
                const validItem = _controlList.find((s: any) => s.id === i.field.uid);
                if (validItem) {
                  fields.push({
                    // text: validItem.text,
                    text: validItem.text?.nameAlias ? validItem.text.nameAlias : validItem.text,
                    nodeText: nodeTreeInfo[key].name,
                    selected: true,
                    nodeId: key,
                    id: i.field.uid,
                  });
                }
              });
            });
          });
        }
      } else {
        this.reset();
      }
    }
    // 区分下拉和平铺
    const tileFields = fields.filter((i: any) =>
      _controlList
        .filter((k) => k.position === 'tile')
        .map((d) => d.id)
        .includes(i.id)
    );
    const dropFields = fields.filter((i: any) =>
      _controlList
        .filter((k) => k.position === 'drop')
        .map((d) => d.id)
        .includes(i.id)
    );
    let tileKeys = tileFields.map((i: any) => i.id);
    let dropKeys = dropFields.map((i: any) => i.id);
    // 第一次初始化 且开启了记住选中配置  同步下本地选择后的配置
    // 如果后台更新了配置 导致字段对上不 删除本地配置
    if (isInit && saveLocalEnable && !isDesign) {
      const localKeysInfoStr = localStorage.getItem(`ebdMindMap_controlFieldsVisibleKeys_${compId}`) || '{}';
      const localKeysInfo = JSON.parse(localKeysInfoStr);
      if (!isEmpty(localKeysInfo)) {
        if (isEqual(localKeysInfo.controlList, _controlList)) {
          let keys: string[] = localKeysInfo.keys;
          let newTileKeys: string[] = [];
          let newDropKeys: string[] = [];
          keys.forEach((i) => {
            if (tileKeys.includes(i)) {
              newTileKeys.push(i);
            }
            if (dropKeys.includes(i)) {
              newDropKeys.push(i);
            }
          });
          tileKeys = newTileKeys;
          dropKeys = newDropKeys;
        } else {
          localStorage.removeItem(`ebdMindMap_controlFieldsVisibleKeys_${compId}`);
        }
      }
    }
    this.setState({ tileFields, dropFields, tileKeys, dropKeys, allFields: fields }, () => {
      // 触发视图更新
      events && events.emit(EbdMindMapEventKeys.controlFieldsVisible, compId, this.getSelectKeys());
    });
  };
  getSelectKeys = () => {
    const { tileKeys, dropKeys } = this.state;
    return [...tileKeys, ...dropKeys];
  };
  setTileKeys = (id: string) => {
    let _tileKeys = this.state.tileKeys;
    const checked = _tileKeys.includes(id);
    if (checked) {
      _tileKeys = _tileKeys.filter((i) => i !== id);
    } else {
      _tileKeys = [..._tileKeys, id];
    }
    this.setState({ tileKeys: _tileKeys }, this.emit);
  };
  emit = () => {
    let _selectKeys = this.getSelectKeys();
    const { events, compId, config } = this.props;
    const fieldFilterSet: FieldFilterSetProps = config.fieldFilterSet!;
    const { saveLocalEnable, controlList } = fieldFilterSet;
    this.setState({ selectKeys: _selectKeys }, () => {
      // 1、根据配置判断是否同步到本地
      if (saveLocalEnable) {
        const payload = {
          controlList,
          keys: _selectKeys,
        };
        localStorage.setItem(`ebdMindMap_controlFieldsVisibleKeys_${compId}`, JSON.stringify(payload));
      }
    });
    // 2、预留二开触发事件
    events && events.emit(EbdMindMapEventKeys.controlFieldsVisible, compId, _selectKeys);
  };
  setDropKeys = (keys: any) => {
    this.setState({ dropKeys: keys }, this.emit);
  };
  render() {
    const { tileFields, dropFields, tileKeys, dropKeys } = this.state;
    if (isEmpty(tileFields) && isEmpty(dropFields)) return null;
    return (
      <div className={cls}>
        {tileFields.map((i: any) => {
          return (
            <div className={`${cls}-item`} key={i.id}>
              <Checkbox weId={`${this.props.weId || ''}_78b95p`} value={tileKeys.includes(i.id)} onClick={() => this.setTileKeys(i.id)} disabled={this.props.disabled}>
                {i.text}（{i.nodeText}）
              </Checkbox>
            </div>
          );
        })}
        <When weId={`${this.props.weId || ''}_tip5e5`} condition={!isEmpty(dropFields)}>
          <Select weId={`${this.props.weId || ''}_r2h71s`} multiple allowSelectAll disabled={this.props.disabled} data={formatValueForSelect(dropFields)} value={dropKeys} onChange={(dropSelectKeys) => this.setDropKeys(dropSelectKeys)} />
        </When>
      </div>
    );
  }
}

export default withRouter(FieldsVisibleSet);
