/**
 * <AUTHOR>
 * @createTime 2021-11-15
 */
import { CorsComponent, utils } from '@weapp/ui';
import { getLabel, isEmpty } from '@weapp/utils';
import React, { PureComponent } from 'react';
// import isEmpty from 'lodash-es/isEmpty';
import {toJS} from 'mobx';
import { DataSetItem, DataSetProps } from './types';

const { isEqual } = utils;
export default class DataSet extends PureComponent<DataSetProps> {
  onChange = (val: DataSetItem) => {
    const { value, onChange } = this.props;

    if (!isEqual(val, value)) {
      onChange(val);
    }
  };

  render() {
    // @ts-ignore
    const { value, config, rootProps = {}, isEngine } = this.props;
    const rootPropsValue = rootProps.value ?? [];
    // @ts-ignore
    const currentFloor = rootPropsValue.filter((i: any) => i.pid === config.pid && i.id !== config.id);
    // *有下级节点，本级节点新增无法配置标准业务数据源 因为无法以标准业务数据源建立下级关系
    // *同级节点已经配置过数据源了 不能再配置标准业务数据源
    // @ts-ignore
    const showBizData = isEmpty(config?.children) && isEmpty(currentFloor)
    const dataSourceJson = {
      showBizData,
      showEteamsData: true, // 显示数据仓库数据,默认显示
      showLogicData: true,  // 业务模块
      showExternalData: false,  // 外部数据源
      showCustomData: false,  // 显示自定义数据
      showExternalDataSet: false,  // 数据集合
      showLocalds: true,  // 数据集
    }
    // 表单高级视图下不显示数据加工和标准业务数据源
    if (isEngine) {
      dataSourceJson.showEteamsData = false
      dataSourceJson.showBizData = false
    }

    return (
      <CorsComponent
        weId={`${this.props.weId || ''}_3766hu`}
        app="@weapp/ebdcoms"
        compName="DataSetView"
        {...dataSourceJson}
        placeholder={getLabel('57052', '选择数据源')}
        {...this.props}
        value={value?.id ? value : undefined}
        onChange={this.onChange}
        useLogicFormData
        comType={config.type}
      />
    );
  }
}
