import loadjs from 'loadjs';
import { isEmpty } from '@weapp/utils';
import { getAssetsJsPath } from '../utils';
import { EbdMindMapEventKeys, MindDefaultPluginName } from '../../../constants';
import { invoke, isIE } from '../../../utils';
import { MindDataV2, MindDataV2Data } from './types';

export const loadJsMindMapDepend = async (draggable: boolean, callback: Function) => {
  const asyncLoadJsMindCss = getAssetsJsPath('jsmind/style/jsmind.css');
  const asyncLoadJsMind = getAssetsJsPath('jsmind/js/jsmind.js');
  const asyncLoadFiels = [asyncLoadJsMindCss, asyncLoadJsMind];
  loadjs(asyncLoadFiels, {
    success: async () => {
      callback();
    },
  });
};
export const loadMindMapDepend = async (draggable: boolean, callback: Function) => {
  const asyncLoadMind = getAssetsJsPath(`simpleMindMap/mind.js?v=${new Date().getTime()}`);
  const asyncLoadPolyfillSvg = getAssetsJsPath(`simpleMindMap/polyfill.svg.min.js?v=${new Date().getTime()}`);
  const asyncLoadPolyfillEs5 = getAssetsJsPath(`simpleMindMap/polyfill.es5.min.js?v=${new Date().getTime()}`);
  let asyncLoadFiles = [asyncLoadMind];
  if (isIE()) {
    asyncLoadFiles = [asyncLoadPolyfillEs5, asyncLoadPolyfillSvg, asyncLoadMind];
  }
  loadjs(asyncLoadFiles, {
    success: async () => {
      callback();
    },
  });
};
// 格式化数据 适配v2 mindMap 只生成骨架 具体卡片渲染字段走自定义渲染
export const formatDataToV2Mind = (data: any[] = [], commonNodeStyleSet: any): MindDataV2 => {
  const _format = (item: any, index: number): MindDataV2 => {
    let children: MindDataV2Data[] = [];
    if (!isEmpty(item.children)) {
      children = item.children.map((i: any, index: number) => _format(i, index + 1));
    }
    // mark 这一步骤看后续能否优化 是否会影响性能
    const dataItem = data.find((i) => i.id === item.id) || {};
    // 默认用公共配置
    let lineColor = commonNodeStyleSet?.lineColor || '#ddd';
    let fill = commonNodeStyleSet?.expandColor;
    if (dataItem.nodeStyle && dataItem.nodeStyle.styleType === 'custom') {
      lineColor = dataItem.nodeStyle.lineColor || commonNodeStyleSet?.lineColor || '#ddd';
      fill = dataItem.nodeStyle.expandColor;
    }
    const { children: _children, ..._dataInfo } = item;
    return {
      data: {
        ..._dataInfo,
        text: item.topic,
        topic: item.content,
        index,
        lineColor,
        expandBtnStyle: {
          fill,
        },
      },
      children,
    };
  };
  console.log('****data****', data);
  const rootNode = data.find((i) => i.isroot);
  if (!rootNode) return { data: {} };
  return _format(rootNode, 1);
};
export const handleOffEvent = (_this: any, isGlobal?: boolean) => {
  const { events, compId } = _this.props;
  if (!events || !events.off) {
    return;
  }
  events.off(`${EbdMindMapEventKeys.onSetExpandLevel}_${compId}`);
  events.off(`${EbdMindMapEventKeys.onChangeTargetNodeVisible}_${compId}`);
  // 清除全局
  if (isGlobal) {
    events.off(`${EbdMindMapEventKeys.onChangeNodeEnable}_${compId}`);
    events.off(`${EbdMindMapEventKeys.onPluDidUpdate}_${compId}`);
    events.off(`${EbdMindMapEventKeys.onDidMount}_${compId}`);
    events.off(`${EbdMindMapEventKeys.onSetMindData}_${compId}`);
    events.off(`${EbdMindMapEventKeys.onResize}_${compId}`);
    events.off(`${EbdMindMapEventKeys.onNodeClick}_${compId}`);
  }
};
export const handleBindEvent = (_this: any) => {
  // 注册展开层级事件
  const { events, compId, pluginCenter } = _this.props;
  const expand2level = (number: number) => {
    _this.expand2level(number);
  };
  // 隐藏的层级
  const changeTargetNodeVisible = (payload: { id: string; visible: boolean }) => {
    const { id, visible } = payload;
    // 隐藏的是根节点
    if (`${id}` === '-1') {
      _this.jm.view.hideRootNodeVisible(!visible);
    }
    // else {
    //   // 暂时只支持删除指定节点及其下属节点
    //   if (visible) {
    //     // todo 删除后的节点 恢复显示
    //   } else {
    //     _this.jm.remove_node(id);
    //   }
    // }
  };
  const changeEnableEdit = (enable: boolean) => {
    if (enable) {
      return _this.jm.enable_edit();
    }
    _this.jm.disable_edit();
  };
  events && events.on(`${EbdMindMapEventKeys.onSetExpandLevel}_${compId}`, expand2level);
  events && events.on(`${EbdMindMapEventKeys.onChangeTargetNodeVisible}_${compId}`, changeTargetNodeVisible);
  events && events.on(`${EbdMindMapEventKeys.onChangeNodeEnable}_${compId}`, changeEnableEdit);
  invoke(pluginCenter, MindDefaultPluginName, EbdMindMapEventKeys.onSetExpandLevel, { args: [expand2level] });
  invoke(pluginCenter, MindDefaultPluginName, EbdMindMapEventKeys.onChangeTargetNodeVisible, { args: [changeTargetNodeVisible] });
  invoke(pluginCenter, MindDefaultPluginName, EbdMindMapEventKeys.onChangeNodeEnable, { args: [changeEnableEdit] });
};

/**
 * @Desc: 全屏
 */
export const fullScreen = (element?: any) => {
  if (!element) return;
  if (element.requestFullScreen) {
    element.requestFullScreen();
  } else if (element.webkitRequestFullScreen) {
    element.webkitRequestFullScreen();
  } else if (element.mozRequestFullScreen) {
    element.mozRequestFullScreen();
  }
};

/**
 * 获取children的层级
 */
export const getChildrenLevel = (data: any[]) => {
  if (!data) return 0;
  let level = 0;
  data.forEach((item: any) => {
    if (item.children) {
      level = Math.max(level, getChildrenLevel(item.children as any[] || []) + 1);
    }
  });
  return level;
};

export default {};
