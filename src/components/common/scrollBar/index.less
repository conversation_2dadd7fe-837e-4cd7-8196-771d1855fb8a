@import (reference) "../../../style/prefix.less";

.@{prefix}-main-scrollbar {
  .scrollbar {
    position: absolute;
    background-color: #eee;
    border-radius: 10px;
    overflow: hidden;
    transition: opacity 0.2s linear;
    &.hide{
      opacity: 0;
    }
    &.show{
      opacity: 1;
    }

    &.verticalScrollbar {
      width: 8px;
      top: 15%;
      bottom: 100px;
      right: 20px;
      height: 70%;

      .scrollbarInner {
        width: 8px;
        left: 0;
      }
    }

    &.horizontalScrollbar {
      height: 8px;
      left: 120px;
      right: 120px;
      bottom: 20px;

      .scrollbarInner {
        height: 8px;
        top: 0;
      }
    }

    .scrollbarInner {
      position: absolute;
      background-color: #ccc;
      border-radius: 10px;
      cursor: pointer;

      &:hover {
        background-color: #bbb;
      }
    }
  }
}