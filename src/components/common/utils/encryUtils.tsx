import { DatePicker, Encry, MEncry } from '@weapp/ui';
import React from 'react';
import { EtComponentKey } from '../constants/EtComponent';
const { desensitization, encryption } = Encry;

/**
 * 加密字段
 */
export const EntryKsys = [
  EtComponentKey.Text,
  EtComponentKey.TextArea,
  EtComponentKey.NumberComponent,
  EtComponentKey.Money,
  EtComponentKey.Email,
  EtComponentKey.Phone,
  EtComponentKey.Mobile,
  EtComponentKey.IDCard,
];

/**
 * 拦截加密值判断是否加密
 * @param value
 * @returns
 */

// eslint-disable-next-line max-len
export const isEntryEnable = (value: string): React.ReactNode | string => !!(desensitization(value) || encryption(value));

/**
 * 拦截加密值
 * @param value
 * @returns
 */
export const wrapEntry = (value: string, isMobile?: boolean): React.ReactNode | string => {
  if (isMobile) {
    return <MEncry weId="_sa6ubo" value={value} />;
  }
  return <Encry weId="_l6pila" value={value} />;
};

// 非加密非转换
export const noWrapEntryDom = (dom: string, parent: any) => {
  if (toString.call(dom) === '[object Object]') {
    return React.cloneElement(
      parent,
      {
        ...parent.props,
      },
      dom,
    );
  }
  return React.cloneElement(parent, {
    ...parent.props,
    dangerouslySetInnerHTML: { __html: DatePicker.getLocaleDateString(dom) },
  });
};

/**
 * 加密正则
 */
// eslint-disable-next-line max-len
export const EncryReg = /desensitization_{2}[\dA-Fa-f]{8}(?:\b-[\dA-Fa-f]{4}){3}\b-[\dA-Fa-f]{12}/g;
