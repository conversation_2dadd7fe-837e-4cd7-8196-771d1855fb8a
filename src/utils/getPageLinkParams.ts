// 参考：weapp-ebdcontainercoms/src/components/nlist/base/config/event-action-config/getPageLinkParams.ts

import { getLabel, isEmpty, cloneDeep } from '@weapp/utils';
// import isEmpty from 'lodash-es/isEmpty';
// import cloneDeep from 'lodash-es/cloneDeep';
import ebdcoms from './ebdcoms';
import { DataSourceValueType } from '../types/common';

/** 判断是否为eb表单-流程类型数据源 */
export const isEbuilderApproval = (dataset?: DataSourceValueType) => {
  if (!dataset) return false;

  const { type, ebuilderDataConfig } = dataset || {};

  return type === 'FORM' && ebuilderDataConfig?.type === 'APPROVAL';
};
export const IDKEY = 'id';
export const FORMFIELDS = 'FORMFIELDS';
export const DefIdentifyIdVal = {
  name: IDKEY,
  type: FORMFIELDS,
  value: { id: IDKEY, content: '', type: IDKEY },
};

const getParamsOpts = (fieldDatas: any[], dataset?: any, text?: string) => {
  const data: any[] = [];
  const dataGroupTitle: any[] = [];
  // 主键字段
  let mainPrimaryKeyField: any;
  // name为id的添加索引
  let nameIdFieldIndex: number = -1;

  fieldDatas.forEach((fieldData) => {
    const { id, name, fields = [] } = fieldData;

    dataGroupTitle.push({ id, content: name });

    fields.forEach((field: any) => {
      if (field) {
        if (field.isDetailtable) {
          field.fields?.forEach((f: any) => {
            data.push({
              ...f,
              id: f.id,
              content: f.text,
              type: id,
              fieldType: f?.type,
            });
          });
        } else if (field.mainPrimaryKey) {
          // 如果是主键字段，记录下
          mainPrimaryKeyField = field;
        } else {
          if (field.name === IDKEY) {
            // 如果符合id固定值的name记录下
            nameIdFieldIndex = data.length;
          }
          data.push({
            ...field,
            id: field.id,
            content: field.text,
            type: id,
            fieldType: field?.type,
          });
        }
      }
    });
  });
  let primaryKey = '';
  let primaryContent = '';
  // 模拟数据不额外添加
  if (dataset?.type !== 'mock') {
    if (mainPrimaryKeyField) {
      // 先判断是否包含主键字段然后添加
      dataGroupTitle.unshift({ id: IDKEY, content: getLabel('70531', '主键字段') });
      primaryKey = mainPrimaryKeyField.id;
      primaryContent = mainPrimaryKeyField.text || getLabel('55955', '数据ID');
      data.unshift({
        id: primaryKey,
        content: primaryContent,
        type: IDKEY,
        objId: mainPrimaryKeyField.objId,
        name: mainPrimaryKeyField.name,
      });
    } else if (nameIdFieldIndex > -1) {
      // 再判断是否包含name是id字段然后添加
      dataGroupTitle.unshift({ id: IDKEY, content: getLabel('70531', '主键字段') });
      const [nameIdField] = data.splice(nameIdFieldIndex, 1);
      primaryKey = IDKEY;
      primaryContent = nameIdField.content || getLabel('55955', '数据ID');
      data.unshift({ ...nameIdField, id: IDKEY, type: IDKEY });
    }
  }
  const paramOpts = [
    {
      type: FORMFIELDS,
      text: text || getLabel('92103', '当前列表字段'),
      ref: {
        type: 'select',
        item: '',
        data,
        dataGroupTitle,
      },
      noPrimaryKey: !primaryKey,
    },
  ];
  const defIdentifyIdVal = {
    name: IDKEY,
    type: FORMFIELDS,
    value: {
      id: primaryKey,
      content: primaryContent,
      type: IDKEY,
      objId: mainPrimaryKeyField?.objId,
      name: mainPrimaryKeyField?.name,
    },
  };

  return { paramOpts, defIdentifyIdVal };
};

const getFieldDatas = (dataset: DataSourceValueType, fieldDatas: any[]) => {
  // 是否为eb表单-流程分类数据源
  const isEbApproval = isEbuilderApproval(dataset);
  const _fieldDatas = cloneDeep(fieldDatas);

  if (isEbApproval) {
    return _fieldDatas.map((fieldData) => {
      const hasId = fieldData.fields.find(
        (field: any) => field.mainPrimaryKey || field.name === 'id',
      );

      if (!hasId) {
        return {
          ...fieldData,
          fields: [
            {
              id: 'id',
              name: 'id',
              text: getLabel('55955', '数据ID'),
              type: 'string',
            },
            ...fieldData.fields,
          ],
        };
      }

      return fieldData;
    });
  }

  return _fieldDatas;
};

const getLinkConfig = (dataset: DataSourceValueType, paramOpts: any, defIdentifyIdVal: any) => {
  let pageLinkMode = '';
  // 判断列表开启了数据分组，支持数据列表钻取，设置mode给pageLink
  if (dataset?.dataGroupConfig && !isEmpty(dataset?.dataGroupConfig)) {
    pageLinkMode = 'dataList';
  }
  return {
    showLinkField: true,
    paramOpts,
    defIdentifyIdVal,
    mode: pageLinkMode,
    dataset,
    simplyDataFields: true,
  };
};
// eslint-disable-next-line max-len
export const getPageLinkConfig = (
  dataset: DataSourceValueType[],
  fieldDatasFormater?: any,
) => new Promise(async (reslove) => {
  if (dataset?.[0] && dataset[0]?.id) {
    const { dsUtils } = await ebdcoms.get();
    dsUtils
      .getFields(dataset[0], '', false, true)
      .then((fieldDatas: any) => {
        // eslint-disable-next-line max-len
        const _fieldDatas = fieldDatasFormater
          ? fieldDatasFormater(dataset[0], fieldDatas)
          : getFieldDatas(dataset[0], fieldDatas);

        const { paramOpts: newParamOpts, defIdentifyIdVal: newDefIdentifyIdVal } = getParamsOpts(
          _fieldDatas,
          dataset,
        );
        const pageLinkConfig = getLinkConfig(dataset[0], newParamOpts, newDefIdentifyIdVal);
        reslove(pageLinkConfig);
      })
      .catch(() => {
        reslove({});
      });
    return;
  }
  reslove({});
});

export default {};
