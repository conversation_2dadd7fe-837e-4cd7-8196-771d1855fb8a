import React, { Suspense, Component, ComponentProps } from 'react';
import ReactDOM from 'react-dom';
import { BrowserRouter as Router, Route } from 'react-router-dom';
import { corsImport, appInfo, getLocale } from '@weapp/utils';

import './style/index.less';
import { needLayout } from './utils';
import { RouterMain } from './lib';
import RouteViewComs from './routes'
import reportWebVitals from './reportWebVitals';
import { menuConfig } from './mock';
import ebdcoms from './utils/ebdcoms';
import { CorsComponent } from '@weapp/ui';
console.log('%c [ mindmap ]-13', 'font-size:13px; background:pink; color:#bf2c9f;')
const root = appInfo('@weapp/ebdmindmap').publicUrl;
function noop() { }

export class Main extends Component<ComponentProps<any>, any> {
  state = {
    module: <RouteViewComs weId={`${this.props.weId || ''}_4mp2v9`} isLib={false} />,
  };

  componentDidMount() {
    if (needLayout()) {
      this.loadLayout();
    }
  }

  loadLayout() {
    // 可以根据业务模块的实际情况 定义模块根加载路径
    corsImport('@weapp/layout').then(
      (app) => {
        if (app.SpComs) {
          this.setState(
            {
              module: React.createElement(
                app.SpComs,
                {},
                <RouteViewComs weId={`${this.props.weId || ''}_3uxw6w`} needLayout isLib={false} />,
              ),
            },
            () => {},
          );
        }
      },
      (error) => {
        throw new Error(error);
      },
    );
  }

  render() {
    return <>{this.state.module}</>;
  }
}
class WithLayout extends Component<ComponentProps<any>, any> {

  state = {
    module: <Main weId={`${this.props.weId || ''}_7twox1`} key={`msrrf0`} />
  }

  componentDidMount(){
    if (needLayout()) {
      this.loadLayout();
    }
  }

  loadLayout() {
    corsImport(`@weapp/layout`).then((app) => {
      if (app.SpComs) {
        this.setState({
          module: React.createElement(
            app.SpComs,
            {},
            <Main weId={`${this.props.weId || ''}_se3980`}
              key={`msrrf0`}
            />
          )
        });
        app?.mainStore?.updateAside(menuConfig, needLayout());
      }
    }, (error) => {
      console.error(error);
    });
  }

  render(){
    return this.state.module;
  }
}

Promise.all([getLocale('@weapp/ebdmindmap'), ebdcoms.load()]).then(() => {
  ReactDOM.render(
    <React.StrictMode weId={`_u3n3tk`}>
      <Router weId={`_7oza3o`}>
        <WithLayout weId={`_6s088f`} />
      </Router>
    </React.StrictMode>,
    document.getElementById('root')
  );
});

// ReactDOM.render(
//   <Router weId={`p37d9s`}>
//     <CorsComponent weId="tpwtve" app="@weapp/designer-demo" compName="FlowDesignerDemo" />
//   </Router>,
//   document.getElementById('root'),
// );
// If you want to start measuring performance in your app, pass a function
// to log results (for example: reportWebVitals(console.log))
// or send to an analytics endpoint. Learn more: https://bit.ly/CRA-vitals
reportWebVitals();
