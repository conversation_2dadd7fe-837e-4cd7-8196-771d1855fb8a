import { EtComponentKey } from '../constants/EtComponent';
import { SysFieldType } from '../constants';

/**
 * 解析系统字段的字段类型
 * @param fieldNameOrId 系统常量
 */
export function getSystemFieldType(fieldNameOrId: string, type: EtComponentKey) {
  switch (type) {
    case EtComponentKey.Date: // 创建时间fieldId: "3"
      return EtComponentKey.DateComponent;
    default:
      break;
  }

  switch (fieldNameOrId) {
    case 'creator': // 创建者fieldId: "2"
    case '2': // 创建者fieldId: "2"
    case '6': // 最后更新人fieldId: "6"
    case '102':
      return EtComponentKey.Employee;
    case 'create_time': // 创建时间fieldId: "3"
    case '3': // 创建时间fieldId: "3"
    case '103':
      return EtComponentKey.DateComponent;
    case 'id': // 数据ID fieldId: "5"
    case '5': // 数据ID fieldId: "5"
    case '105':
    case '16': // 审批编号
      return EtComponentKey.Text;
    case 'data_status': // 数据状态 fieldId: "8"
    case '8': // 数据状态 fieldId: "8"
    case '108':
      return EtComponentKey.Select;
    case 'name': // 标题 fieldId: "1"
    case '1': // 标题 fieldId: "1"
    case '101':
      return EtComponentKey.Text;
    case 'update_time': // 标题 fieldId: "4"
    case '4': // 最后更新时间
    case '104':
      return EtComponentKey.DateComponent;
    case 'flow_status': // 流程状态 fieldId: "9"
    case '9': // 流程状态 fieldId: "9"
    case '109':
      return EtComponentKey.Select;
    case 'current_step': // 当前阶段 fieldId: "10"
    case '10': // 当前阶段 fieldId: "10"
    case '1010':
      return EtComponentKey.Text;
    case '15':
      return SysFieldType.Classification;
    default:
      return type;
  }
}

export default getSystemFieldType;
