/**
 * <AUTHOR>
 * @createTime 2021-11-17
 */
import React, { useEffect, useState } from 'react';
import { Input } from '@weapp/ui';

const RequiredInput: React.ComponentType<any> = (props) => {
  const { value, placeholder } = props;
  const [text, setText] = useState('');
  useEffect(() => {
    setText(value);
  }, [value]);
  const handleChange = (val: React.ReactText) => {
    setText(`${val}`.trim());
  };
  const onBlur = (val: React.ReactText) => {
    props.onChange(`${val}`.trim());
  };
  return (<Input
    weId={`${props.weId || ''}_lypukd`}
    style={{ width: '100%' }}
    onChange={handleChange}
    onBlur={onBlur}
    value={text}
    placeholder={placeholder}
  />
  );
};
export default RequiredInput;
