import React, { useEffect, useState } from 'react';

export default function FPSMonitor() {
  const [fps, setFps] = useState(0);
  const isLocal = window.location.host === 'localhost:3737'

  useEffect(() => {
    if (!isLocal) {
      return;
    }
    let frameCount = 0;
    let lastTime = performance.now();
    let frameId: number;

    const measureFPS = () => {
      const now = performance.now();
      const delta = now - lastTime;
      frameCount++;

      if (delta > 1000) {
        const fps = Math.round((frameCount * 1000) / delta);
        setFps(fps);
        frameCount = 0;
        lastTime = now;
      }

      frameId = requestAnimationFrame(measureFPS);
    };

    measureFPS();

    return () => cancelAnimationFrame(frameId);
  }, []);
  if (!isLocal) {
    return <div />;
  }

  return <div style={{display: 'flex', justifyContent: 'flex-end', alignItems: 'center', fontSize: 14, color: '#111'}}>FPS: {fps}</div>;
}