import { CorsComponent } from '@weapp/ui';
import { getLabel } from '@weapp/utils';

const config = [
  {
    typeName: get<PERSON><PERSON><PERSON>('55830', '数据展示类'),
    name: get<PERSON><PERSON><PERSON>('55988', '思维导图'),
    component: "MindMap",
    type: "dataPresentation",
    package: "weapp-ebdmindmap",
    id: "weapp-ebdmindmap_MindMap"
  },
];

const EbdDoc = (props: any) => {
  return <CorsComponent weId={`${props.weId || ''}_8j848s`} app="@weapp/ebddoc" compName="LayoutCom" config={config} />;
};

export default EbdDoc;
