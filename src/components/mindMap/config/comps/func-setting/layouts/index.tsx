/**
 * 布局切换组件
 */
import { Switch, Button, ButtonProps } from '@weapp/ui';
import { ebdfClsPrefix } from '../../../../../../constants';
import './index.less';

interface Props {
  compKey?: string;
  value: boolean;
  title: string;
  onCheck?: (value: boolean) => void;
  customRenderRight?: JSX.Element;
  desc?: string;
  btnProps?: ButtonProps;
}
const cls = `${ebdfClsPrefix}-common-setting-card`;
const SettingCard = (props: Props) => {
  return (
    <div className={cls}>
    </div>
  );
};
export default SettingCard;
