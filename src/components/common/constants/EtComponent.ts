import { TypesBrowserData, TypesBrowserOption } from '@weapp/ui';
import { getLabel } from '@weapp/utils';
import { SysFieldType } from './';
export enum EtComponentKey {
  Number = 'Number', // 数据id
  Text = 'Text', // 单行文本
  TextArea = 'TextArea', // 多行文本

  RadioBox = 'RadioBox', // 单选框
  CheckBox = 'CheckBox', // 复选框
  Select = 'Select', // 下拉
  ComboSelect = 'ComboSelect', // 级联下拉
  Cascader = 'Cascader', // 级联

  DateComponent = 'DateComponent', // 日期
  DateInterval = 'DateInterval', // 日期区间
  TimeComponent = 'TimeComponent', // 时间控件

  SerialNumber = 'SerialNumber', // 编号

  NumberComponent = 'NumberComponent', // 数字输入框
  Money = 'Money', // 金额

  Mobile = 'Mobile', // 手机
  Phone = 'Phone', // 电话
  Email = 'Email', // 邮箱
  IDCard = 'IDCard', // 身份证控件

  ImageComponent = 'ImageComponent', // 图片
  radioboximg = 'radioboximg', // 图片单选（表单组件）
  checkboximg = 'checkboximg', // 图片多选（表单组件）
  ImageRadioBox = 'ImageRadioBox', // 图片单选框
  ImageCheckBox = 'ImageCheckBox', // 图片多选框
  FileComponent = 'FileComponent', // 附件

  Employee = 'Employee', // 用户选择
  EmployeeScope = 'EmployeeScope', // 人员范围选择
  EmployeeOrganization = 'EmployeeOrganization', // 人员组织多选
  Creator = 'creator', // 创建者
  Updater = 'updater', // 最后修改人和修改时间字段
  Department = 'Department', // 部门选择
  Subcompany = 'Subcompany', // 分部
  Raty = 'Raty', // 评分控件
  ProgressBar = 'ProgressBar', // 进度条
  Task = 'Task', // 任务组件
  Mainline = 'Mainline', // 关联项目
  Ebuilder = 'Ebuilder', // Ebuilder

  EinvoiceComponent = 'EinvoiceComponent', // 发票
  SignatureComponent = 'SignatureComponent', // 签名
  AudioRecord = 'AudioRecord', // 语音
  VideoRecord = 'VideoRecord', // 拍视频

  PaaS = 'PaaS', // 关联e-builder
  FormComponent = 'FormComponent', // 关联表单数据
  PositionComponent = 'PositionComponent', // 地理位置
  Document = 'Document', // 关联文档
  Workflow = 'Workflow', // 关联审批
  AgendaComponent = 'AgendaComponent', // 关联日程
  CustomerComponent = 'CustomerComponent', // 关联客户
  ClueComponent = 'ClueComponent', // 关联线索
  OrderComponent = 'OrderComponent', // 关联订单
  ContactComponent = 'ContactComponent', // 客户联系人
  ChanceComponent = 'ChanceComponent', // 关联商机
  ProductionComponent = 'ProductionComponent', // 关联产品
  ContractComponent = 'ContractComponent', // 关联合同
  ActivityComponent = 'ActivityComponent', // 关联活动
  WorkreportComponent = 'WorkreportComponent', // 关联报告
  CompetitorComponent = 'CompetitorComponent', // 关联对手
  KpiFlowComponent = 'KpiFlowComponent', // 关联绩效
  QuoteComponent = 'QuoteComponent', // 关联报价
  AttendComponent = 'AttendComponent', // 关联考勤
  DataSource = 'DataSource', // 关联数据源
  TreeSelect = 'TreeSelect', // 树形控件
  ODocReceiveComponent = 'ODocReceiveComponent', // 公文收发文单位
  RelateODoc = 'RelateODoc', // 关联公文
  CashBook = 'CashBook', // 记账本费用
  Country = 'Country', // 国家
  Province = 'Province', // 省份
  City = 'City', // 城市
  District = 'District', // 区县

  Date = 'Date', // 日期字段
  Monitor = 'Monitor', // 运算控件

  Switch = 'Switch', // 开关

  // 筛选分组合并等特殊场景添加分类概念，后端约定一些类型，方便后面解析高级搜索等
  GroupCustom = 'GroupCustom',

  /** *********** 以下为202205之后新增的组件类型 *************** */
  // 关联浏览
  RelateBrowser = 'RelateBrowser',
  /**
   * 数据源改造
   * */
  String = 'String',
}

export const InputType = [
  EtComponentKey.Text,
  EtComponentKey.TextArea,
  EtComponentKey.Email,
  EtComponentKey.Mobile,
  EtComponentKey.Phone,
  EtComponentKey.IDCard,
  EtComponentKey.SerialNumber,
  EtComponentKey.PositionComponent,
];

export const SelectType = [EtComponentKey.CheckBox, EtComponentKey.Select, EtComponentKey.RadioBox];
export const NumberType = [
  EtComponentKey.NumberComponent,
  EtComponentKey.Money,
  EtComponentKey.Number,
];

export const BrowserType = [
  EtComponentKey.Employee,
  EtComponentKey.Department,
  EtComponentKey.Task,
  EtComponentKey.Mainline,
  EtComponentKey.Ebuilder,
  EtComponentKey.PaaS,
  EtComponentKey.FormComponent,
  EtComponentKey.Document,
  EtComponentKey.Workflow,
  EtComponentKey.AgendaComponent,
  EtComponentKey.CustomerComponent,
  EtComponentKey.ClueComponent,
  EtComponentKey.OrderComponent,
  EtComponentKey.ContactComponent,
  EtComponentKey.ChanceComponent,
  EtComponentKey.ProductionComponent,
  EtComponentKey.ContractComponent,
  EtComponentKey.CompetitorComponent,
  EtComponentKey.KpiFlowComponent,
  EtComponentKey.QuoteComponent,
  EtComponentKey.AttendComponent,
  EtComponentKey.DataSource,
  EtComponentKey.WorkreportComponent,
  EtComponentKey.ActivityComponent,
  EtComponentKey.TreeSelect,
  EtComponentKey.ODocReceiveComponent,
  EtComponentKey.RelateODoc,
  EtComponentKey.CashBook,
  EtComponentKey.RelateBrowser,
  EtComponentKey.Subcompany,
];
// 列表字段需要处理链接字段的类型
export const BrowserLinkType = [
  EtComponentKey.Task,
  EtComponentKey.Mainline,
  EtComponentKey.Ebuilder,
  EtComponentKey.PaaS,
  EtComponentKey.FormComponent,
  EtComponentKey.Document,
  EtComponentKey.Workflow,
  EtComponentKey.AgendaComponent,
  EtComponentKey.CustomerComponent,
  EtComponentKey.ClueComponent,
  EtComponentKey.OrderComponent,
  EtComponentKey.ContactComponent,
  EtComponentKey.ChanceComponent,
  EtComponentKey.ProductionComponent,
  EtComponentKey.ContractComponent,
  EtComponentKey.CompetitorComponent,
  EtComponentKey.KpiFlowComponent,
  EtComponentKey.QuoteComponent,
  EtComponentKey.AttendComponent,
  EtComponentKey.DataSource,
  EtComponentKey.WorkreportComponent,
  EtComponentKey.ActivityComponent,
];

/**
 * from 后端yyk
 * 无法解析显示值的字段
 * 附件,FileComponent
 * 图片,ImageComponent
 * 图片单选框,ImageRadioBox
 * 图片多选框,ImageCheckBox
 * 树形控件,TreeSelect
 * 数据源,DataSource
 * 运算控件,Monitor
 * 评分,Raty
 * 签名,SignatureComponent
 * 进度条,ProgressBar
 *
 * 多行文本 TextArea  多行文本值存在formdata_text表 可能包含html格式 先去掉
 */
export const ShowNameFailed = [
  EtComponentKey.ImageComponent,
  EtComponentKey.ImageRadioBox,
  EtComponentKey.ImageCheckBox,
  EtComponentKey.TreeSelect,
  EtComponentKey.DataSource,
  EtComponentKey.Monitor,
  EtComponentKey.Raty,
  EtComponentKey.SignatureComponent,
  EtComponentKey.ProgressBar,
  EtComponentKey.TextArea,
  EtComponentKey.FileComponent,
];

/**
 * 特殊字段类型。列表无法直接展示，需要前端自定义
 * 进度条，评分，地图，图片，附件，复选框，电子发票，签名，语音，拍视频，密级
 */
export const CustomToShowField = [
  EtComponentKey.Raty,
  EtComponentKey.ProgressBar,
  EtComponentKey.PositionComponent,
  EtComponentKey.ImageComponent,
  EtComponentKey.FileComponent,
  EtComponentKey.TextArea,
  EtComponentKey.CheckBox,
  EtComponentKey.RadioBox,
  EtComponentKey.EinvoiceComponent,
  EtComponentKey.Switch,
  EtComponentKey.SignatureComponent,
  EtComponentKey.AudioRecord,
  EtComponentKey.VideoRecord,
  SysFieldType.Classification,
];

/**
 * 对多重数据返回结构自定义
 *
 */
export const CustomToMultiplField = [
  EtComponentKey.CheckBox,
  EtComponentKey.Department,
  EtComponentKey.Subcompany,
];

// 需要额外传参的浏览框
export const EbOtherParams = [
  EtComponentKey.Ebuilder,
  EtComponentKey.FormComponent,
  EtComponentKey.Workflow,
  EtComponentKey.Task,
  EtComponentKey.TreeSelect,
  EtComponentKey.ODocReceiveComponent,
  EtComponentKey.RelateODoc,
  EtComponentKey.CashBook,
  EtComponentKey.DataSource,
];

// 特殊字段:筛选处渲染成范围
export const SpacialScopeType = [
  EtComponentKey.Raty,
  EtComponentKey.ProgressBar,
  EtComponentKey.Monitor,
];

/**
 * h5分类搜索屏蔽的字段类型
 */
export const H5GroupShieldFieldTypes = [
  EtComponentKey.Ebuilder,
  EtComponentKey.Employee,
  EtComponentKey.EmployeeScope,
  EtComponentKey.EmployeeOrganization,
  EtComponentKey.Department,
  EtComponentKey.Subcompany,
  EtComponentKey.RelateBrowser,
];

export const specialNeedTransformTypes = [
  EtComponentKey.Employee,
  EtComponentKey.Creator,
  EtComponentKey.Updater,
  EtComponentKey.Department,
  EtComponentKey.Subcompany,
  EtComponentKey.EmployeeScope,
  EtComponentKey.EmployeeOrganization,
];

export const needCustomTitleType = [
  EtComponentKey.ComboSelect,
  EtComponentKey.Workflow,
  EtComponentKey.EinvoiceComponent,
  EtComponentKey.Switch,
  EtComponentKey.SignatureComponent,
  EtComponentKey.VideoRecord,
  EtComponentKey.AudioRecord,
];

export const transformType = [EtComponentKey.Select];

export const DateType = [
  EtComponentKey.Date,
  EtComponentKey.DateComponent,
  EtComponentKey.TimeComponent,
];

export const DateDftNumberType = [EtComponentKey.DateComponent];

export const typeBrowserKeys = [EtComponentKey.EmployeeScope, EtComponentKey.EmployeeOrganization];

export const browserTypes: () => TypesBrowserOption[] = () =>  [
  { id: 'user', content: getLabel('52090', '人员') },
  { id: 'dept', content: getLabel('52087', '部门') },
  { id: 'subcompany', content: getLabel('52088', '分部') },
  { id: 'group', content: getLabel('160244', '常用组') },
  { id: 'role', content: getLabel('52091', '角色') },
  { id: 'position', content: getLabel('52093', '岗位') },
  { id: 'all', content: getLabel('52092', '所有人') },
  { id: 'external', content: getLabel('144639', '外部联系人') },
];

export const typeBrowserProps: TypesBrowserData = {
  user: {
    dataParams: {
      test: 'resource',
    },
  },
  dept: {
    dataParams: {
      test: 'department',
    },
  },
};
