import { appInfo, getLabel } from '@weapp/utils';
import { MIND_VIEW, MIND_LINE_STYLE } from './enum';
import { isIE } from '../utils';
const { publicDomain, publicUrl } = appInfo('@weapp/ebdfpage');
/**
 * ebcoms项目统一类名前缀
 */
export const clsPrefix = 'ebcoms';
export const ebdfClsPrefix = 'weapp-ebdmindmap';
/**
 * eb组件类名前缀
 */
export const ebcomPrefix = 'ebcom';

export const ebdMindMapComsClsPrefix = 'weapp-ebdmindmapcoms';

export const ebdMindMapComsName = 'weappEbdmindMapcoms';

export const appName = 'weappEbdMindMap';

export const mindMapViewClsPrefix = `${ebdfClsPrefix}-mindmapv`;
export const mindMapUrlParamsCache = `${appName}UrlParamsCache`;

/**
 * @doc https://weapp.eteams.cn/sp/techdoc/doc/738623955723059200
 * @desc 【修改】加载自己模块 '@weapp/[模块名]' 的二级路径埋点
 * */
export const staticPath = `${publicDomain}${publicUrl}`; // 内部静态文件资源地址
/* 路由根路径 */
export const root = publicUrl || window.publicUrlweappEbdfpage || window.publicUrlstatic || window.publicUrl || '';
export const routeName = 'ebdmindmap';
export const spRouteName = `${root}/sp/${routeName}/`;
export const mobileRouteName = `${root}/mobile/${routeName}/`;

/**
 * 布局等右侧滑宽度缓存key
 */
export const EbFSlideRightWidthKey = 'EbFSlideRightWidthKey';

/**
 * 视图类型
 * */
export enum ModuleType {
  MindMap = 'mindMapView', // 思维导图
  Table = 'tableView', // 表格
}

/** eb后台公用头部图标 */
export const dlgIconName = 'Icon-e-builder-o';

/**
 * 1、按钮参数缓存key，方便按钮动作参数解析url等参数
 * 2、获取默认值后缓存，方便布局参数获取
 */
export const EbdfUrlParamsCacheKey = 'EbdfUrlParamsCacheKey';
export const EbdfDfValueParamsCacheKey = 'EbdfDfValueParamsCacheKey';

/**
 * 网关标识
 */
export const EbuilderFormModule = 'ebuilder/form';

/**
 * 高级搜索，快捷，筛选三者独立，本地缓存搜索状态
 */

export enum ListSearchCacheParams {
  CurrentSearchState = 'currentSearchState',
  MCurrentSearchState = 'mcurrentSearchState',
}
/**
 * 节点尺寸默认大小
 * @param isRoot 是否为根节点
 * @returns Object
 */
export const INIT_NODE_SIZE = (isRoot?: boolean) => ({
  width: {
    type: 'fixed',
    size: isRoot ? 76 : 166,
  },
  height: {
    type: 'fixed',
    size: isRoot ? 37 : 96,
  },
  sizeType: isRoot ? 'custom' : 'common',
});

/**
 * 节点样式默认值
 * @param icCustom 是否为节点自定义配置
 * @returns Object
 */
export const INIT_NODE_STYLE = (icCustom?: boolean) => {
  return {
    styleType: icCustom ? 'custom' : 'common',
    background: '',
    mouseEnterBackground: '',
    borderColor: '',
    mouseEnterBorderColor: '',
    lineColor: '',
    expandColor: '',
    color: '',
  };
};
//显示字段、条件设计器、动作、屏蔽标签字段
export const NotUseFields = [
  '11', //"公共标签"
  '12', //  "我的标签",
];
export const MindDefaultPluginName = 'mindMap_plugin';
export enum EbdMindMapEventKeys {
  onDidMount = 'onDidMount', // 加载完成
  onDidUpdate = 'onDidUpdate', // 更新时
  onPluDidUpdate = 'onPluDidUpdate', // 插件更新时
  onDestroy = 'onDestroy', // 销毁
  getData = 'getData', // 设置值
  onResize = 'onResize', // 内容大小变化
  onSetMindData = 'onSetMindData', // 设置思维导图默认值
  onNodeClick = 'onNodeClick', // 节点点击事件
  onSetExpandLevel = 'onSetExpandLevel', // 设置展开层级
  onChangeTargetNodeVisible = 'onChangeTargetNodeVisible', // 设置节点可见
  onChangeNodeEnable = 'onChangeNodeEnable', // 设置是否可编辑
  renderCardContent = 'renderCardContent', // 自定义渲染单个卡片
  renderCardBefore = 'renderCardBefore', // 自定义渲染卡片左侧区域
  renderCardAfter = 'renderCardAfter', // 自定义渲染卡片右侧区域
  renderContentTop = 'renderContentTop', // 自定义渲染头部区域
  renderContentBottom = 'renderContentBottom', // 自定义渲染底部区域
  renderContentBefore = 'renderContentBefore', // 自定义渲染左侧区域
  renderContentAfter = 'renderContentAfter', // 自定义渲染右侧区域
  renderCustomMindMap = 'renderCustomMindMap', // 自定义复写整体思维导图
  controlFieldsVisible = 'ebdMindmap.control.fields.visible', // 前台控制显示字段
  getThemeConfig = 'getThemeConfig', // 设置主题相关
  getRefreshCompIsMulti = 'getRefreshCompIsMulti', // 定制当前刷新组件默认是否多选
  getRefreshCompParams = 'getRefreshCompParams', // 定制当前刷新组件传参
  getMindSize = 'getMindSize', // 自定义设置导图尺寸
}
// 高级版思维导图默认最大缩放节点数
export const A_MIND_ZOOM_MAX_COUNT = 250;
// 高级版思维导图连线宽度
export const getMindLineWidth = (totalLen: number) => {
  if (totalLen > 500 && totalLen <= 800) {
    return 0.8;
  }
  if (totalLen > 800) {
    return 0.6;
  }
  return 1;
};
interface AD_MIND_IPROPS {
  key: string;
  options?: any;
  totalLen?: number;
  lineStyle?: MIND_LINE_STYLE;
}
// 高级版思维导图默认配置
export const ADVANCE_MIND_DEFAULT_CONFIG = ({ key, options, totalLen, lineStyle }: AD_MIND_IPROPS) => {
  const baseConfig = {
    // 只读模式
    readonly: true,
    // 自定义渲染卡片内容
    isUseCustomNodeContent: true,
    // ie下屏蔽鼠标放大缩小事件；有兼容性问题
    // disableMouseWheelZoom: isIE(),
    // 每次改动缩放比例
    scaleRatio: 0.1,
    // 布局
    layout: key,
    // 限制画布拖住范围
    // isLimitMindMapInCanvas: true,
    // 一直显示节点展开收起按钮
    alwaysShowExpandBtn: true,
    // 屏蔽鼠标放大缩小事件
    disableMouseWheelZoom: true,
    // 是否禁止拖动画布
    // isDisableDrag: true,
    // 是否禁止双指移动画布
    // isDisableTwoFingerMoveCanvas: true,
    // 鼠标双击回到根节点
    enableDblclickBackToRootNode: false,
    // 展开收缩按钮尺寸
    expandBtnSize: 14,
    // 展开收起图标样式
    expandBtnStyle: {
      color: '#C6CBD3',
    },
  };
  let themeConfig = {} as any;
  const commonThemeConfig = {
    // 背景色
    backgroundColor: 'transparent',
    // 连线色
    lineColor: '#ddd',
    hoverRectColor: 'transparent',
    lineWidth: getMindLineWidth(totalLen!),
    // 该配置设为true，那么根节点的连线起始位置也会在节点右侧（或左侧）
    rootLineStartPositionKeepSameInCurve: true,
  };
  switch (key) {
    case MIND_VIEW.LOGICAL_STRUCTURE:
      themeConfig = {
        ...commonThemeConfig,
        // 连线风格:曲线（curve）、直线（straight）、直连（direct）
        lineStyle,
        // 根节点样式
        root: {
          fontSize: 12,
        },
        // 二级节点样式
        second: {
          marginY: 10, // 垂直距离
        },
        // 三级及以下节点样式
        node: {
          marginY: 10, // 垂直距离
          marginX: 100, // 水平距离
        },
      };
      break;
    case MIND_VIEW.ORGANIZATION_STRUCTURE:
      themeConfig = {
        lineStyle,
        ...commonThemeConfig,
      };
      break;
    case MIND_VIEW.MIND_MAP:
      const mindMapMarginY = 10;
      themeConfig = {
        ...commonThemeConfig,
        second: {
          marginY: mindMapMarginY,
        },
        node: {
          marginY: mindMapMarginY,
        },
      };
      break;
    default:
      themeConfig = {
        ...commonThemeConfig,
      };
      break;
  }
  return { ...baseConfig, themeConfig, ...options };
};

export const MindMapDefaultLineColor = '#ddd';

export enum MindMapCardFieldAvatarSize {
  mini = 20,
  small = 24,
  medium = 30,
  large = 46,
}
