import { toJS } from 'mobx';
import { observer } from 'mobx-react';
import React from 'react';
import './index.less';
import TableTree from './TableTree';
import { TreeBoxProps } from './types';

@observer
class TreeBox extends React.PureComponent<TreeBoxProps> {
  render() {
    const {
      data, onRemove, onNodeOptClick, onCreate, selector,
    } = this.props;

    return (
      <div className="tree-box ">
        <TableTree
          weId={`${this.props.weId || ''}_7aq1tx`}
          data={toJS(data)}
          onRemove={onRemove}
          onNodeOptClick={onNodeOptClick}
          onCreate={onCreate}
          selector={selector}
        />
      </div>
    );
  }
}

export default TreeBox;
