!function(t,e){"object"==typeof exports&&"undefined"!=typeof module?e(require("core-js/modules/es.array.filter.js"),require("core-js/modules/es.array.find.js"),require("core-js/modules/es.array.map.js"),require("core-js/modules/es.number.constructor.js"),require("core-js/modules/es.object.to-string.js"),require("core-js/modules/es.object.values.js"),require("jsmind")):"function"==typeof define&&define.amd?define(["core-js/modules/es.array.filter.js","core-js/modules/es.array.find.js","core-js/modules/es.array.map.js","core-js/modules/es.number.constructor.js","core-js/modules/es.object.to-string.js","core-js/modules/es.object.values.js","jsmind"],e):e(null,null,null,null,null,null,(t="undefined"!=typeof globalThis?globalThis:t||self).jsMind)}(this,(function(t,e,i,o,s,n,l){"use strict";function a(t){var e=function(t,e){if("object"!=typeof t||!t)return t;var i=t[Symbol.toPrimitive];if(void 0!==i){var o=i.call(t,e||"default");if("object"!=typeof o)return o;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==typeof e?e:e+""}function h(t,e){for(var i=0;i<e.length;i++){var o=e[i];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(t,a(o.key),o)}}function r(t,e){if(t!==e)throw new TypeError("Cannot instantiate an arrow function")}if(!l)throw new Error("jsMind is not defined");var _=l.$,d="getSelection"in _.w?function(){_.w.getSelection().removeAllRanges()}:function(){_.d.selection.empty()},c={line_width:5,line_color:"rgba(0,0,0,0.3)",line_color_invalid:"rgba(255,51,51,0.6)",lookup_delay:200,lookup_interval:100,scrolling_trigger_width:20,scrolling_step_length:10,shadow_node_class_name:"jsmind-draggable-shadow-node"},u=function(){return t=function t(e,i){!function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,t);var o={};l.util.json.merge(o,c),l.util.json.merge(o,i),this.version="0.4.0",this.jm=e,this.options=o,this.e_canvas=null,this.canvas_ctx=null,this.shadow=null,this.shadow_p_x=0,this.shadow_p_y=0,this.shadow_w=0,this.shadow_h=0,this.active_node=null,this.target_node=null,this.target_direct=null,this.client_w=0,this.client_h=0,this.offset_x=0,this.offset_y=0,this.hlookup_delay=0,this.hlookup_timer=0,this.capture=!1,this.moved=!1,this.canvas_draggable=e.get_view_draggable(),this.view_panel=e.view.e_panel,this.view_panel_rect=null},(e=[{key:"init",value:function(){this.create_canvas(),this.create_shadow(),this.event_bind()}},{key:"resize",value:function(){this.jm.view.e_nodes.appendChild(this.shadow),this.e_canvas.width=this.jm.view.size.w,this.e_canvas.height=this.jm.view.size.h}},{key:"create_canvas",value:function(){var t=_.c("canvas");this.jm.view.e_panel.appendChild(t);var e=t.getContext("2d");this.e_canvas=t,this.canvas_ctx=e}},{key:"create_shadow",value:function(){var t=_.c("jmnode");t.style.visibility="hidden",t.style.zIndex="3",t.style.cursor="move",t.style.opacity="0.7",t.className=this.options.shadow_node_class_name,this.shadow=t}},{key:"reset_shadow",value:function(t){var e=this.shadow.style;this.shadow.innerHTML=t.innerHTML,e.left=t.style.left,e.top=t.style.top,e.width=t.style.width,e.height=t.style.height,e.backgroundImage=t.style.backgroundImage,e.backgroundSize=t.style.backgroundSize,e.transform=t.style.transform,this.shadow_w=this.shadow.clientWidth,this.shadow_h=this.shadow.clientHeight}},{key:"show_shadow",value:function(){this.moved||(this.shadow.style.visibility="visible")}},{key:"hide_shadow",value:function(){this.shadow.style.visibility="hidden"}},{key:"magnet_shadow",value:function(t,e,i){this.canvas_ctx.lineWidth=this.options.line_width,this.canvas_ctx.strokeStyle=i?this.options.line_color_invalid:this.options.line_color,this.canvas_ctx.lineCap="round",this.clear_lines(),this.canvas_lineto(t.x,t.y,e.x,e.y)}},{key:"clear_lines",value:function(){this.canvas_ctx.clearRect(0,0,this.jm.view.size.w,this.jm.view.size.h)}},{key:"canvas_lineto",value:function(t,e,i,o){this.canvas_ctx.beginPath(),this.canvas_ctx.moveTo(t,e),this.canvas_ctx.lineTo(i,o),this.canvas_ctx.stroke()}},{key:"event_bind",value:function(){var t=this,e=this.jm.view.container;_.on(e,"mousedown",(function(e){0===e.button&&t.dragstart.call(t,e)})),_.on(e,"mousemove",(function(e){0===e.movementX&&0===e.movementY||t.drag.call(t,e)})),_.on(e,"mouseup",(function(e){t.dragend.call(t,e)})),_.on(e,"touchstart",(function(e){t.dragstart.call(t,e)})),_.on(e,"touchmove",(function(e){t.drag.call(t,e)})),_.on(e,"touchend",(function(e){t.dragend.call(t,e)}))}},{key:"dragstart",value:function(t){if(this.jm.get_editable()&&!this.capture){this.active_node=null,this.view_draggable=this.jm.get_view_draggable();var e=this.jm.view,i=this.find_node_element(t.target);if(i){this.view_draggable&&this.jm.disable_view_draggable();var o=e.get_binded_nodeid(i);if(o){var s=this.jm.get_node(o);if(!s.isroot){this.reset_shadow(i),this.view_panel_rect=this.view_panel.getBoundingClientRect(),this.active_node=s,this.offset_x=(t.clientX||t.touches[0].clientX)/e.zoom_current-i.offsetLeft,this.offset_y=(t.clientY||t.touches[0].clientY)/e.zoom_current-i.offsetTop,this.client_hw=Math.floor(i.clientWidth/2),this.client_hh=Math.floor(i.clientHeight/2),0!=this.hlookup_delay&&_.w.clearTimeout(this.hlookup_delay),0!=this.hlookup_timer&&_.w.clearInterval(this.hlookup_timer);var n=this;this.hlookup_delay=_.w.setTimeout((function(){n.hlookup_delay=0,n.hlookup_timer=_.w.setInterval((function(){n.lookup_target_node.call(n)}),n.options.lookup_interval)}),this.options.lookup_delay),n.capture=!0}}}else console.log("throw away")}}},{key:"drag",value:function(t){if(this.jm.get_editable()&&this.capture){t.preventDefault(),this.show_shadow(),this.moved=!0,d();var e=this.jm.view,i=(t.clientX||t.touches[0].clientX)/e.zoom_current-this.offset_x,o=(t.clientY||t.touches[0].clientY)/e.zoom_current-this.offset_y;t.clientY-this.view_panel_rect.top<this.options.scrolling_trigger_width&&this.view_panel.scrollTop>this.options.scrolling_step_length?(this.view_panel.scrollBy(0,-this.options.scrolling_step_length),this.offset_y+=this.options.scrolling_step_length/e.zoom_current):this.view_panel_rect.bottom-t.clientY<this.options.scrolling_trigger_width&&this.view_panel.scrollTop<this.view_panel.scrollHeight-this.view_panel_rect.height-this.options.scrolling_step_length&&(this.view_panel.scrollBy(0,this.options.scrolling_step_length),this.offset_y-=this.options.scrolling_step_length/e.zoom_current),t.clientX-this.view_panel_rect.left<this.options.scrolling_trigger_width&&this.view_panel.scrollLeft>this.options.scrolling_step_length?(this.view_panel.scrollBy(-this.options.scrolling_step_length,0),this.offset_x+=this.options.scrolling_step_length/e.zoom_current):this.view_panel_rect.right-t.clientX<this.options.scrolling_trigger_width&&this.view_panel.scrollLeft<this.view_panel.scrollWidth-this.view_panel_rect.width-this.options.scrolling_step_length&&(this.view_panel.scrollBy(this.options.scrolling_step_length,0),this.offset_x-=this.options.scrolling_step_length/e.zoom_current),this.shadow.style.left=i+"px",this.shadow.style.top=o+"px",d()}}},{key:"dragend",value:function(t){if(this.jm.get_editable()){if(this.view_draggable&&this.jm.enable_view_draggable(),this.capture){if(0!=this.hlookup_delay&&(_.w.clearTimeout(this.hlookup_delay),this.hlookup_delay=0,this.clear_lines()),0!=this.hlookup_timer&&(_.w.clearInterval(this.hlookup_timer),this.hlookup_timer=0,this.clear_lines()),this.moved){var e=this.active_node,i=this.target_node,o=this.target_direct;this.move_node(e,i,o)}this.hide_shadow()}this.view_panel_rect=null,this.moved=!1,this.capture=!1}}},{key:"find_node_element",value:function(t){return t===this.jm.view.e_nodes||t===this.jm.view.e_panel||t===this.jm.view.container?null:"jmnode"===t.tagName.toLowerCase()?t:this.find_node_element(t.parentNode)}},{key:"lookup_target_node",value:function(){var t=this.shadow.offsetLeft,e=this.shadow.offsetTop;if(t!==this.shadow_p_x||e!==this.shadow_p_y){this.shadow_p_x=t,this.shadow_p_y=e;var i=this.shadow_p_x+this.shadow_w/2>=this.get_root_x()?l.direction.right:l.direction.left,o=this.lookup_overlapping_node_parent(i)||this.lookup_close_node(i);if(o){var s=this.calc_point_of_node(o,i),n=l.node.inherited(this.active_node,o);this.magnet_shadow(s.sp,s.np,n),this.target_node=o,this.target_direct=i}}}},{key:"get_root_x",value:function(){var t=this.jm.get_root(),e=t.get_location(),i=t.get_size();return e.x+i.w/2}},{key:"lookup_overlapping_node_parent",value:function(t){for(var e=this.shadow.getBoundingClientRect(),i=e.x+e.width*(1-t)/2,o=(this.jm.options.layout.hspace+this.jm.options.layout.pspace)*t,s=e.height,n=0,l=[[i,e.y],[i,e.y+s/2],[i,e.y+s],[i+o/2,e.y],[i+o/2,e.y+s/2],[i+o/2,e.y+s],[i+o,e.y],[i+o,e.y+s/2],[i+o,e.y+s]];n<l.length;n++){var a=l[n],h=this.lookup_node_parent_by_location(a[0],a[1]);if(h)return h}}},{key:"lookup_node_parent_by_location",value:function(t,e){var i=this;return _.d.elementsFromPoint(t,e).filter(function(t){return r(this,i),"JMNODE"===t.tagName&&t.className!==this.options.shadow_node_class_name}.bind(this)).map(function(t){return r(this,i),this.jm.view.get_binded_nodeid(t)}.bind(this)).map(function(t){return r(this,i),t&&this.jm.mind.nodes[t]}.bind(this)).map(function(t){return r(this,i),t&&t.parent}.bind(this)).find(function(t){return r(this,i),t}.bind(this))}},{key:"lookup_close_node",value:function(t){var e=this;return Object.values(this.jm.mind.nodes).filter(function(i){return r(this,e),i.direction==t||i.isroot}.bind(this)).filter(function(t){return r(this,e),this.jm.layout.is_visible(t)}.bind(this)).filter(function(i){return r(this,e),this.shadow_on_target_side(i,t)}.bind(this)).map(function(i){return r(this,e),{node:i,distance:this.shadow_to_node(i,t)}}.bind(this)).reduce(function(t,i){return r(this,e),t.distance<i.distance?t:i}.bind(this),{node:this.jm.get_root(),distance:Number.MAX_VALUE}).node}},{key:"shadow_on_target_side",value:function(t,e){return e==l.direction.right&&this.shadow_to_right_of_node(t)>0||e==l.direction.left&&this.shadow_to_left_of_node(t)>0}},{key:"shadow_to_right_of_node",value:function(t){return this.shadow_p_x-t.get_location().x-t.get_size().w}},{key:"shadow_to_left_of_node",value:function(t){return t.get_location().x-this.shadow_p_x-this.shadow_w}},{key:"shadow_to_base_line_of_node",value:function(t){return this.shadow_p_y+this.shadow_h/2-t.get_location().y-t.get_size().h/2}},{key:"shadow_to_node",value:function(t,e){return(e===l.direction.right?Math.abs(this.shadow_to_right_of_node(t)):Math.abs(this.shadow_to_left_of_node(t)))+Math.abs(this.shadow_to_base_line_of_node(t))}},{key:"calc_point_of_node",value:function(t,e){var i=t.get_size(),o=t.get_location(),s=t.isroot?o.x+i.w/2:o.x+i.w*(1+e)/2+this.options.line_width*e,n=o.y+i.h/2;return{sp:{x:this.shadow_p_x+this.shadow_w*(1-e)/2-this.options.line_width*e,y:this.shadow_p_y+this.shadow_h/2},np:{x:s,y:n}}}},{key:"move_node",value:function(t,e,i){var o=this.shadow.offsetTop;if(e&&t&&!l.node.inherited(t,e)){for(var s=e.children,n=s.length,a=null,h=Number.MAX_VALUE,r=null,_="_last_";n--;)if((a=s[n]).direction==i&&a.id!=t.id){var d=a.get_location().y-o;d>0&&d<h&&(h=d,r=a,_="_first_")}r&&(_=r.id),this.jm.move_node(t.id,_,e.id,i)}this.active_node=null,this.target_node=null,this.target_direct=null}},{key:"jm_event_handle",value:function(t,e){t===l.event_type.resize&&this.resize()}}])&&h(t.prototype,e),i&&h(t,i),Object.defineProperty(t,"prototype",{writable:!1}),t;var t,e,i}(),p=new l.plugin("draggable_node",(function(t,e){var i=new u(t,e);i.init(),t.add_event_listener((function(t,e){i.jm_event_handle.call(i,t,e)}))}));l.register_plugin(p)}));
//# sourceMappingURL=jsmind.draggable-node.js.map
