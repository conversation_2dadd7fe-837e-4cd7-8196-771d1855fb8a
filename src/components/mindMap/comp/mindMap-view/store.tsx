import { AnyObj } from '@weapp/ui';
import { qs, forIn, isEmpty } from '@weapp/utils';
import { DataSet } from '@weapp/ebdcoms';
import { action, observable, runInAction, toJS } from 'mobx';
import axios from 'axios';
import utils, { invoke, canLoadData } from '../../../../utils';
import { ajax, format2FormData } from '../../../common/utils/ajax';
import { Direction } from '../../../common/mind-map/types';
import { getCardParams } from '../../../common/utils/cardFn';
import { MindPluginCenter } from '../../types';
import { dfDialogWidthParams, getPercentWidth, WidthParams } from '../../../common/dialogWidthUtil';
import { BaseParamsProps, MindMapDataItem, TreeDataItem, MindMapDataPayload } from './types';
import { setDataByName, getNormalBusinessData, formatDataWithTreeStructure } from './utils';
import { MindDefaultPluginName, EbdMindMapEventKeys } from '../../../../constants';
import { MockMindMapData, MockMindMapNodeData } from '../../../../constants/mock';

type ViewComProps = any;
class NewViewBaseStore {
  cardPageRef: any = null;
  @observable viewComInfo: ViewComProps | null = null;
  @observable loading: boolean = true;
  @observable comServicePath: string = 'ebuilder/coms'; // ebuilder/coms映射路径
  @observable searchParams: AnyObj = {}; // url参数:关联参数hideTop也通过这里传

  @action
  editOrShowListData = (type: string, objid: string, dataid: string, dialogWidthParams: WidthParams) => {
    const { cardPageRef } = this;
    cardPageRef?.openFormCardPage(type, objid, dataid, dialogWidthParams);
  };

  @action
  setComServicePath = (comServicePath: string) => {
    this.comServicePath = comServicePath || 'ebuilder/coms';
  };
}
export class MindMapViewStore extends NewViewBaseStore {
  config: any = {}; // 导图配置
  @observable compId: string = ''; // compId

  @observable pageId: string = '';

  @observable isMobile: boolean = false;

  @observable isDesign: boolean = false;
  @observable pluginCenter: MindPluginCenter = {}; // 引入插件包机制
  @observable title: string = '';
  @observable direction: Direction = Direction.Right;

  @observable visible: boolean = false; // 提供弹框显隐控制

  @observable isclick: boolean = false;

  @observable slideModalUrl: string | undefined = '';

  @observable dialogWidthParams: WidthParams = dfDialogWidthParams;

  @observable hoverNodeId: string = '';
  @observable wrapperWidth = 0;
  @observable wrapperHeight = 0;
  @observable events: any = {};
  @observable cancelTokenSource: any = {};
  @observable defaultExpendDepth: number = 0; // 默认全部展开
  @observable legendMap: any = new Map(); // 图例数据map
  @observable showFieldKeys = [] as string[]; // 显示字段

  @action
  setState = (params: { [key: string]: any }) => {
    forIn(params, (val: any, key: string) => {
      this[key as keyof this] = val;
    });
  };
  // 设置导图数据
  setMindMapData = (mindMapDatas: TreeDataItem[], mindMapList?: MindMapDataItem[], cb?: Function) => {
    const params = {
      datas: mindMapDatas,
      config: this.config,
      direction: this.direction,
      isDesign: this.isDesign,
      showFieldKeys: this.showFieldKeys,
    };
    const _mindMapList = isEmpty(mindMapList) ? formatDataWithTreeStructure(params) : mindMapList;
    // console.log('****mindMapDatas****', mindMapDatas);
    console.log('****_mindMapList****', _mindMapList);
    // @ts-ignore
    // const [firstNode, ...reset] = _mindMapList;
    // console.log('*****firstNode***', firstNode);
    // console.log('****reset****', reset);
    const payload = {
      mindMapDatas: toJS(mindMapDatas),
      mindMapList: toJS(_mindMapList) ?? [],
      config: toJS(this.config),
    };
    cb && cb(payload);
  };
  @action
  init = async (params: ViewComProps, uParams: AnyObj, cb?: Function) => {
    this.reset();
    this.setState({ searchParams: uParams });
    this.baseInit(params); // 获取基础配置
    // 文档地址单独处理  取mock数据
    if (params.isDoc) {
      this.setMindMapData(MockMindMapData() as any, [], cb);
      this.loading = false;
    } else {
      this.loadData(params.config, cb);
    }
  };
  @action
  reset = () => {
    this.setState({
      // wrapperWidth: 0,
      legendMap: new Map(),
      loading: false,
      showFieldKeys: [],
    });
    this.config = {};
    this.cardPageRef = null;
  };
  /**
   * 处理标准业务数据源
   */
  @action
  loadNormalDataSet = async (config: any, mindMapDatas: TreeDataItem[] = []) => {
    // 处理标准业务数据 下拿数据并赋值
    // 获取每个节点的数据
    let { nodeTreeInfo, nodeTreeList } = config;
    let fetchArr = [];
    for (let i in nodeTreeInfo) {
      const parentNodeInList = nodeTreeList.find((k: any) => k.id === i);
      // 不是合法节点不处理
      if (parentNodeInList) {
        const item = nodeTreeInfo[i];
        const dataset: DataSet = item.dataset ?? {};
        if (dataset.type === 'BIZ') {
          fetchArr.push({ item: { ...item, infoId: i, pid: parentNodeInList.pid }, fetch: getNormalBusinessData(dataset, item.showFields, this.viewComInfo, item.dataFilter) });
        }
      }
    }
    if (isEmpty(fetchArr)) {
      return mindMapDatas;
    }
    const response = await Promise.all(fetchArr.map((i) => i.fetch));
    fetchArr.forEach((payload, index) => {
      const res = response[index];
      const item = payload.item;
      const parentParentNode = nodeTreeList.find((k: any) => k.id === item.pid);
      // 1是找parentid 利用接口返回的mindMapDatas 拿当前层级 找到该mindMapDatas层级下的数据
      // 2 是填充数据到上级节点
      setDataByName(mindMapDatas[0], item.infoId, res, parentParentNode, nodeTreeList);
    });
    return mindMapDatas;
  };
  cancelRequest = () => {
    if (this.cancelTokenSource.func && this.cancelTokenSource.compId === this.compId) {
      this.cancelTokenSource.func.cancel('Request canceled');
    }
  };
  @action
  loadData = async (config: any, cb?: Function) => {
    let data: MindMapDataPayload;
    const canLoad = canLoadData(config);
    // 没合法配置 不请求
    if (!canLoad) {
      this.cancelRequest();
      return;
    }
    if (canLoad === 'noData') {
      this.cancelRequest();
      // 如果是空的 填充默认数据
      this.setMindMapData(MockMindMapNodeData() as any, [], cb);
      return;
    }
    this.loading = true;
    if (this.isDesign) {
      data = await this.getDesignData(config);
    } else {
      data = await this.getData(config);
    }
    // 标准业务数据源处理
    const _mindMapDatas = await this.loadNormalDataSet(config, data.mindMapDatas);
    this.setMindMapData(_mindMapDatas || [], data.mindMapList, cb);
  };

  @action
  baseInit = (params: BaseParamsProps) => {
    this.viewComInfo = params; // compId，pageId，config,isMobile,isDesign
    this.config = params.config;
    this.compId = params.compId;
    this.pageId = params.pageId;
    this.isMobile = params.isMobile;
    this.isDesign = params.isDesign;
    this.events = params.events!;
    this.pluginCenter = params.pluginCenter!;
    if (params?.comServicePath) {
      this.setComServicePath(params.comServicePath);
    }
  };
  @action
  getDesignData = async (config: any) => {
    this.cancelRequest();
    this.cancelTokenSource = {
      func: axios.CancelToken.source(),
      compId: this.compId,
    };
    const res = await ajax({
      url: `/api/${this.comServicePath}/mindmap/previewInitData`,
      method: 'POST',
      data: qs.stringify({
        pageId: this.pageId,
        compId: this.compId,
        config: JSON.stringify(toJS(config)),
        extParam: JSON.stringify({}),
      }),
      ebBusinessId: this.pageId,
      cancelToken: this.cancelTokenSource?.func?.token,
      error: (info: any) => {},
    });
    const { mindMapDatas = [], defaultExpendDepth = 0 } = res || {};
    runInAction(() => {
      this.defaultExpendDepth = defaultExpendDepth;
      this.legendMap = this.getMindlegendMapData(mindMapDatas);
    });
    return { mindMapDatas };
  };

  @action
  getData = async (config: any) => {
    this.cancelRequest();
    this.cancelTokenSource = {
      func: axios.CancelToken.source(),
      compId: this.compId,
    };
    const params = {
      ...this.searchParams,
    };
    const res = await ajax({
      url: `/api/${this.comServicePath}/mindmap/getInitData`,
      method: 'POST',
      data: format2FormData({
        pageId: this.pageId,
        compId: this.compId,
        extParam: JSON.stringify({
          ...params,
        }),
        ...params,
      }),
      ebBusinessId: this.pageId,
      cancelToken: this.cancelTokenSource?.func?.token,
      error: (info: any) => {},
    });
    let { mindMapDatas, defaultExpendDepth } = res;
    runInAction(() => {
      this.defaultExpendDepth = defaultExpendDepth;
      this.legendMap = this.getMindlegendMapData(mindMapDatas);
    });
    // 插件包：拦截分组数据并返回新处理的数据
    const [_mindMapDatas] = invoke(this.pluginCenter, MindDefaultPluginName, EbdMindMapEventKeys.getData, {
      args: [mindMapDatas],
    }) || [mindMapDatas];
    return {
      mindMapDatas: _mindMapDatas,
    };
  };

  getMindlegendMapData = (datas: TreeDataItem[]): MindMapDataItem[] => {
    let legendMap: any = new Map();
    datas.forEach((item: TreeDataItem) => {
      const loop = (data: any) => {
        const legend = data?.customParameters?.legend || [];
        legend.forEach((legendItem: any) => {
          const { isMatch = true, id = '' } = legendItem;
          if (legendMap.has(legendItem.id)) {
            const time = legendMap.get(legendItem.id).time;
            legendMap.set(legendItem.id, {
              ...legendItem,
              time: isMatch ? time + 1 : time,
            });
          } else {
            legendMap.set(legendItem.id, {
              ...legendItem,
              time: isMatch ? 1 : 0,
            });
          }
        });
        const child = data.children;
        if (child) {
          for (let i = 0; i < child.length; i++) {
            loop(child[i]);
          }
        }
      };

      loop(item);
    });
    return legendMap;
  };

  @action
  setDirection = (direction: Direction) => {
    this.direction = direction;
  };

  /**
   * 打开侧滑窗口
   * @param visible 侧滑显示隐藏
   * @param url  页面地址
   * @param percentage 侧滑百分比
   * @param mode  类型，表单or 流程
   */
  @action
  showSlideModal = (visible: boolean = true, url: string = '', percentage: string = '50') => {
    if (!this.isclick) {
      this.isclick = true;
      const _this = this;
      setTimeout(() => {
        runInAction(() => {
          _this.isclick = false;
        });
      }, 300); // 300ms内仅能点击一次
      if (!visible) {
        this.slideModalUrl = '';
      }
      const dialogWidthParams = getPercentWidth(this.wrapperWidth, percentage);
      this.dialogWidthParams = dialogWidthParams;
      const qsParams = utils.getQueryParams(url!) || {};
      /** *
       * 卡片：显示编辑兼容新的路由模式打开，考虑历史数据/page/card?type=xxxxx
       */
      if (url && utils.checkHref(url!)) {
        const data = getCardParams(url);
        this.editOrShowListData(data[0], data[1], data[2], dialogWidthParams);
        return;
      }
      if (url.indexOf('/page/card') > -1) {
        // 老数据
        this.editOrShowListData('0', qsParams.objid, qsParams.dataid, dialogWidthParams);
        return;
      }
      /**
       * 其他页面
       */
      this.visible = visible;
      if (visible) {
        this.slideModalUrl = url;
      }
    }
  };

  @action // 关闭弹窗
  onClose = () => {
    this.showSlideModal(false);
    this.dialogWidthParams = dfDialogWidthParams;
  };
}

export default new MindMapViewStore();
