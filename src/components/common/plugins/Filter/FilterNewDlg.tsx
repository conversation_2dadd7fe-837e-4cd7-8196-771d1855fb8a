import { DataSet, Filter } from '@weapp/ebdcoms';
import { CorsComponent } from '@weapp/ui';
import { getLabel } from '@weapp/utils';
import { observer } from 'mobx-react';
import React from 'react';

export interface ListFilterProps extends React.Attributes {
  value: Filter[];
  dataSet: DataSet;
  visible?: boolean;
  filter?: any; // 筛选数据函数
  onOk?: (fitlers: any[]) => void;
  onCancel?: () => void;
  title?: string;
}

@observer
export default class EBFilterNewDlg extends React.PureComponent<ListFilterProps> {
  render() {
    const { value = [], dataSet } = this.props;
    const _value = typeof value === 'string' && value ? JSON.parse(value) : value;
    return (
      // <CorsComponent
      //   weId={`${this.props.weId || ''}_30wszt`}
      //   app="@weapp/ebdcoms"
      //   compName="ConditionSet"
      //   title={getLabel('56236', '列表固定查询条件')}
      //   {...this.props}
      //   value={_value}
      //   dataset={dataset}
      // />
      <CorsComponent
        weId={`${this.props.weId || ''}_7e2o5m`}
        app="@weapp/components"
        compName="ConditionSet"
        title={getLabel('56236', '列表固定查询条件')}
        {...this.props}
        value={_value}
        dataSet={dataSet}
      />
    );
  }
}
