import { changeNumToHan } from './../../mindMap/utils';
import { ebdfClsPrefix } from "../../../constants";

/**
 * 列表按钮方法常量
 */
export enum ListButtonType {
    TableAdd = 'tableAdd',
    TableDelete = 'tableDelete', // 批量删除
    doImp = 'doImp',
    TableExp = 'tableExp', // 导出
    TableImp = 'tableImp', // 导入
    BatchEditValue = 'batchEditValue', // 批量编辑字段值
    BatchPrint = 'batchPrint', // 批量打印
    BatchAggregationPrint = 'batchAggregationPrint', // 批量打印
    BatchAdd = 'batchAdd', // 批量新增
    BatchEdit = 'batchEdit', // 批量编辑
    BatchFollow = 'batchFollow', // 批量关注
    BatchUnfollow = 'batchUnfollow', // 批量取消关注
    BatchMarkReadState = 'batchMarkReadState', // 批量已读
    MarkReadState = 'markReadState', // 标记为已读
    Follow = 'follow',
    Unfollow = 'unfollow',
    LikeBtn = 'likeBtn', // 赞
    DislikeBtn = 'disLikeBtn', // 踩
    CommentBtn = 'commentBtn', // 评论
    MyFollows = 'myFollows',
    BatchPubTags = 'batchPubTags', // 批量设置公共标签
    BatchMyTags = 'batchMyTags', // 批量设置我的标签
    SetTop = 'setTop', // 置顶
    CancelSetTop = 'cancelSetTop', // 取消置顶
    CustomBatch = 'customBatch', // 自定义批量操作
    BatchShare = 'batchShare', // 批量共享
    ShareToMe = 'shareToMe', // 共享给我的数据
    Personalization = 'personalization', // 个性化
    InitializeColumnWidth = 'initializeColumnWidth', // 初始化列宽
    MessageReceiveSet = 'messageReceiveSet', // 消息免打扰

    QuickOrder = 'quickOrder', // 快捷排序

    /**
     * 卡片按钮显示在行末
     * */
    DataEdit = 'edit', // 编辑
    DataCopy = 'copy', // 复制
    DataDelete = 'delete', // 删除
    DataPrint = 'print', // 打印
    DataShare = 'share', // 共享
    ExportPDF = 'exportPDF',
}


/**
 * 卡片按钮方法常量
 */
export enum CardButtonType {
    Comment = 'comment',
    View = 'view',
    OperationLog = 'operationLog', // 操作日志
    FormLog = 'formlog',
    Save = 'save',
    SaveAndCreate = 'saveAndCreate',
    SaveAndCopy = 'saveAndCopy',
    EditSave = 'editSave',
    TempSave = 'tempSave',
    Edit = 'edit',
    Copy = 'copy',
    Delete = 'delete',
    Print = 'print',
    Cancel = 'cancel',
    GoBack = 'goBack', // 返回
    Chart = 'chart', // 流程图
    Status = 'status', // 流程状态
    Follow = 'follow',
    Unfollow = 'unfollow',
    LikeBtn = 'likeBtn', // 赞
    DislikeBtn = 'disLikeBtn', // 踩
    CommentBtn = 'commentBtn', // 评论
    Remove = 'remove', // 彻底删除
    Restore = 'restore', // 还原
    Share = 'share', // 共享
    MattersForward = 'mattersForward', // 事项转发
    EncryptForward = 'encryptForward', // 加密转发
    MessageReceiveSet = 'messageReceiveSet', // 消息免打扰

    PrevData = 'prevData', // 上一条
    NextData = 'nextData', // 下一条
    ExportPDF = 'exportPDF', // 导出PDF
}


// listview 宽度缓存
export enum ListViewCache {
    Widths = 'listCacheWidths',
}

/**
 * 表格运行类前缀 pc
 */
export const tableViewClsPrefix = `${ebdfClsPrefix}-tablev`;


/**
 * 和ebdcoms里面的同步，异步引入有问题，自己同步定义一份
 */
export enum PageType {
    Workflow = 'WORKFLOW',
    Search = 'SEARCH',
    Card = 'CARD',
    ViewPort = 'VIEWPORT', // 下面的未来删掉   日历 看板 甘特图
    Calendar = 'CALENDAR', // 日历
    Board = 'BOARD', // 看板
    Gantt = 'GANTT', // 甘特图
    Layout = 'LAYOUT',
    Page = 'PAGE',
    Link = 'LINK',
    NULL = '',
    Report = 'REPORT',
    SystemPage = 'SYSTEMPAGE',
    MobilePortal = 'MOBILEPORTAL',
    Scene = 'SCENE',
}

// 新增数据默认值
export const COMP_DEFAULT_CONFIG = () => {
    return {
        funcSetting: {
            // 基础设置
            base: {
                isShowExpandNum: false, // 展开图标是否显示数量
                useScrollBar: true, // 开启滚动条，默认开启
            },
            // 底部工具栏
            toolbar: [
                { goRoot: false },
                { fullScreen: false },
                { scale: true },
                { expand_all: false },
                { collapse_all: false },
            ],
        }
    }
}


export enum SysFieldType {
 Classification = 'classification' // 密级
}

export enum ActionPositionType {
    // RightTop = 'rightTop', // 右上角
    // RightTopDropDown = 'rightTopDropDown', // 右上角下拉
    LineEnd = 'lineEnd', // 行末
    LineEndDropDown = 'LineEndDropDown', // 行末下拉
    // GridDropDown = 'gridDropDown' // 网格下拉
    Link = 'link', // 理解为链接字段，行上
    ListLine = 'listline' // 理解为行动作
}
//  视图类型
export enum ViewType {
    List = 'list', // 主表
    DetailList = 'detailList', // 主子表
    GridList = 'gridList', // 网格视图
    ExcelList = 'excelList', // 可编辑Excel列表
}