// import cloneDeep from 'lodash-es/cloneDeep';
import { cloneDeep } from "@weapp/utils"

function fromEntries<K extends string, V>(entries: [K, V][]): Record<K, V> {
  const obj: Record<K, V> = {} as Record<K, V>;
  for (const [key, value] of entries) {
    obj[key] = value;
  }
  return obj;
}
// 按floor排序
export const sortNodeTreeInfo = (config: any) => {
  const { nodeTreeInfo, nodeTreeList } = config;
  const _nodeTreeInfo = cloneDeep(nodeTreeInfo);
  // 以nodeTreeList中数据为准，过滤掉脏数据
  const validNodeTree = [] as any[]
  for (const key in _nodeTreeInfo) {
    // 有对应关系的才保留
    const node = nodeTreeList.find((n: any) => n.id === key && _nodeTreeInfo[n.pid]);
    if (node) {
      validNodeTree.push(_nodeTreeInfo[key]);
      _nodeTreeInfo[key].floor = node.floor;
    }
  }
  // 将对象转为数组
  const roomsArray = Object.entries(validNodeTree);
  // @ts-ignore
  roomsArray.sort(([, a], [, b]) => a.floor - b.floor);
  const sortedRooms = fromEntries(roomsArray);
  return sortedRooms as any
};