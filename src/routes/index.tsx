import React, { Component, Suspense } from 'react';
import { Route, withRouter, RouteComponentProps } from 'react-router-dom';
import RouteEbDesigner from '../components/ebDesigner/route';
import { root, routeName } from '../constants';
import { fomatParentPath, formatParentPath, isMobile } from '../utils';

class RouteViewComs extends Component<RouteComponentProps & { isLib: boolean; needLayout?: boolean; weId?: string }> {
  render(){
    let path = window.location.href.indexOf('sp') <= -1 ? root : `${root}/sp/`;
    const _isMobile = isMobile();
    return (
      <Route weId={`_rimf4m`} path={path}>
        <Suspense weId="byvqu9" fallback={<div />}>
          <RouteEbDesigner weId={`${this.props.weId || ''}_avd9bl`}/>
        </Suspense>
      </Route>
      // <Route weId={`${this.props.weId || ''}_r1k1tx`}
      //   path={`${parentPath}/ebdmindmapcoms`}
      //   component={Main}
      // />
    )
  }
}

export default withRouter(RouteViewComs);