@import (reference) '../../../../../style/index.less';


.@{prefix}-layoutSet {
  &-btn {
    display: flex;
    justify-content: flex-end;

    .ui-icon {
      cursor: pointer;
      color: var(--secondary-fc);
      transition: 0.3s;
      padding-right: 8px;
      &:hover{
        color: var(--primary);
      }
    }
  }

  &-content {
    background: #fff;
    display: flex;
    flex-wrap: wrap;

    &-item {
      width: 240px;
      padding: 8px;
      cursor: pointer;

      &-label {
        height: 24px;
        display: flex;
        align-items: start;

        &-wrapper {
          margin: var(--hd) calc(var(--hd)*16) var(--hd) 0;
          color: var(--checkbox-font-color);
          font-size: var(--checkbox-font-size);
          display: flex;
          align-items: center;
          height: 14px;
          line-height: 14px;

          .ui-checkbox-wrapper {
            margin-right: 8px;
            top: 1px;
          }
        }
      }

      &-content {
        border: var(--border-solid);
        height: 183px;
        width: 224px;
        overflow: hidden;
        transition: all .3s;
        display: flex;
        align-items: center;
        justify-content: center;

        &:hover {
          box-shadow: var(--box-shadow);
        }

        img {
          width: 60%;
          height: 60%;
        }
      }
    }
  }
}