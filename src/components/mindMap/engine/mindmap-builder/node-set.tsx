import {
  Button, Icon, ITreeData, Tree, TreeRootIdArray,
} from '@weapp/ui';
import { getLabel } from '@weapp/utils';
import { observer } from 'mobx-react';
import React, { PureComponent } from 'react';
import { When } from 'react-if';
import { ModuleType } from '../../../../constants';
import { getLocaleValueString } from '../../../common/locale/utils';
import { OccupyBuilderProps } from '../../../common/types/index'
import '../../config/index.less';
import '../index.less';
import MindMapNodeConfig from '../node-config';
import { MindMapEngineStore } from '../store';

@observer
class MindMapEngineContent extends PureComponent<any, any> {
  store = new MindMapEngineStore();

  constructor(props: any) {
    super(props);
    this.state = {
      expandedKeys: [],
      selectedKeys: [],
    };
  }

  componentDidMount() {
    this.setInfo(true);
  }

  componentDidUpdate(oldProps: OccupyBuilderProps) {
    const { appId: _appId, pageId: _pageId, clientType: _clientType } = oldProps;
    const { appId, pageId, clientType } = this.props;
    if (_appId !== appId || _pageId !== pageId || _clientType !== clientType) {
      this.setInfo(true);
    }
  }

  setInfo = (isAllTree?: boolean) => {
    const {
      appId, config, refreshFn, clientType,
    } = this.props;
    if (!(appId && config?.objId && config.viewId)) return;
    this.store.init(
      {
        objId: config.objId,
        appId,
        type: ModuleType.MindMap,
        name: '',
        id: config.viewId,
        clientType,
      },
      refreshFn,
      isAllTree,
    );
  };

  showSetting = () => {
    const {
      onNodeOptClick, treeData, nodeTreeInfo, selector,
    } = this.store;
    if (this.state.selectedKeys.length !== 0) {
      onNodeOptClick(selector);
      return;
    }
    const data = nodeTreeInfo ? { ...treeData[0], ...nodeTreeInfo[treeData[0].id] } : treeData[0];
    onNodeOptClick(data);
  };

  onRemove = (node: any) => () => {
    const { onTableTempRemove } = this.store;
    onTableTempRemove(node);
  };

  onCreate = (node: any) => () => {
    const { onNodeCreate } = this.store;
    onNodeCreate(node);
  };

  handleClick = (node: any) => () => {
    const { onNodeOptClick, nodeTreeInfo } = this.store;
    const data = nodeTreeInfo ? { ...node, ...nodeTreeInfo[node.id] } : node;
    onNodeOptClick(data);
  };

  handleSave = async () => {
    const { doSaveAllTree, needDeleteNodesCache, onTableRemove } = this.store;
    needDeleteNodesCache.forEach(async (node: any) => {
      await onTableRemove(node, true);
    });
    await doSaveAllTree();
  };

  customRenderNode = (node: ITreeData) => (
    <div className="mindmap-tree-warp-node">
      <div className="node-left" onClick={this.handleClick(node)}>
        {getLocaleValueString(node.content)}
      </div>
      <div className="node-right">
        <span className="treenode-operate-add" onClick={this.onCreate(node)}>
          <Icon weId={`${this.props.weId || ''}_oc9ok8`} name="Icon-add-to01" />
        </span>
        <When weId={`${this.props.weId || ''}_rp5ujs`} condition={node.pid && node.canDelete}>
          <span className="treenode-operate-del" onClick={this.onRemove(node)}>
            <Icon weId={`${this.props.weId || ''}_4nti0z`} name="Icon-delete" />
          </span>
        </When>
      </div>
    </div>
  );

  onExpand = (expandedKeys: TreeRootIdArray) => {
    this.setState({ expandedKeys });
  };

  onSelect = (data: ITreeData) => {
    this.setState({ selectedKeys: [data.id] });
  };

  render() {
    const {
      selector,
      closeNodeConfigAndClear,
      afterFormInit,
      fieldChange,
      actions,
      setActions,
      appId,
      dataset,
      getTreeData,
      visible,
      isSave,
    } = this.store;
    const { expandedKeys, selectedKeys } = this.state;
    let treeProps: any = {};
    if (!expandedKeys.length) {
      treeProps = {
        defaultExpandedLeavel: 4,
      };
    } else {
      treeProps = {
        expandedKeys,
        onExpand: this.onExpand,
      };
    }
    return (
      <>
        <Button weId={`${this.props.weId || ''}_rh246k`} type="link" onClick={this.showSetting}>
          <Icon weId={`${this.props.weId || ''}_nhbleb`} name="Icon-department02" className="btn-icon-left" />
          {getLabel('87164', '节点设置')}
        </Button>
        <MindMapNodeConfig
          weId={`${this.props.weId || ''}_hp0f6w`}
          leftContent={
            <Tree
              weId={`${this.props.weId || ''}_hyaufs`}
              {...treeProps}
              className="mindmap-tree-warp"
              data={getTreeData}
              customRenderNode={this.customRenderNode}
              expandOnlyArrow
              onSelect={this.onSelect}
              selectedKeys={selectedKeys}
            />
          }
          visible={visible}
          onCancel={closeNodeConfigAndClear}
          selector={selector}
          doSave={this.handleSave}
          afterFormInit={afterFormInit}
          fieldChange={fieldChange}
          actions={actions}
          setActions={setActions}
          appId={appId}
          dataset={dataset}
          refreshKey={selector.id}
          isSave={isSave}
        />
      </>
    );
  }
}

export default MindMapEngineContent;
