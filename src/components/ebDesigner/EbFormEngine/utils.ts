import { corsImport } from '@weapp/utils';

const engines: any = {};
const asyncInits: any = {};

const genEngine = (type: string, ebEngine: any) => ebEngine().then((asyncebEngine: any) => {
  const _ebEngine: any = asyncebEngine.default;

  // name为readonly，故使用displayName定义组件名称
  _ebEngine.displayName = `${type}Engine`;

  return _ebEngine;
});

const initEngine = (props: any) => {
  const { type } = props;

  return getEBComEngine(type).then((ebcoms) => {
    if (!ebcoms) return Promise.resolve(null);

    const asyncView = genEngine(type, ebcoms.Engine);

    return Promise.all([asyncView]).then(([View]) => {
      engines[type] = View;
    });
  });
};

/**
 * 异步加载视图配置页面
 * @returns
 */
export function getEBComEngine(type: string): Promise<any | null> {
  return corsImport('@weapp/ebdmindmap').then((mode) => {
    if (mode.ebcoms && mode.ebcoms[type]) {
      return mode.ebcoms[type];
    }
    return null;
  });
}

export function getExternalEngine(params: any): Promise<any> {
  const { type } = params;

  if (engines[type]) {
    // 组件缓存后，将组件请求的记录清除
    delete asyncInits[type];
    return Promise.resolve(engines[type]);
  }

  // 同一时间可能有多个调用获取组件信息，防止重复请求，故将请求缓存
  if (!asyncInits[type]) {
    asyncInits[type] = initEngine(params).then(() => engines[type]);
  }

  return asyncInits[type];
}
