import { observable, action, runInAction, computed, toJS } from 'mobx';
import { FormStore, FormItemProps, FormLayoutType, FormDatas } from '@weapp/ui';
import { getLabel, isEmpty } from '@weapp/utils';
// import isEmpty from 'lodash-es/isEmpty';

const TYPE_OPTION = () => [
  {
    id: 'fixed',
    content: getLabel('55521', '固定值'),
  },
  {
    id: 'adapt',
    content: getLabel('56749', '自适应'),
  },
];
const SIZE_FORM_LAYOUT: FormLayoutType[] = [[{ id: 'sizeType', label: `${getLabel('53872', '类型')}：`, labelSpan: 4, items: ['sizeType'], hide: false }]];
const FORM_LAYOUT: FormLayoutType[] = [[{ id: 'widthType', label: `${getLabel('54578', '宽度')}：`, labelSpan: 4, items: ['widthType', 'widthSize'], hide: true }], [{ id: 'heightType', label: `${getLabel('54597', '高度')}：`, labelSpan: 4, items: ['heightType', 'heightSize'], hide: true }]];
const SIZE_FORM_ITEMS = (): FormItemProps => ({
  sizeType: {
    itemType: 'RADIO',
    data: [
      { id: 'common', content: getLabel('56756', '通用') },
      { id: 'custom', content: getLabel('54002', '自定义') },
    ],
  },
});
const FORM_ITEMS = (): FormItemProps => ({
  widthType: {
    itemType: 'SELECT',
    options: TYPE_OPTION(),
  },
  widthSize: {
    itemType: 'INPUT',
    placeholder: `${getLabel('56234', '请输入')}`,
  },
  heightType: {
    itemType: 'SELECT',
    options: TYPE_OPTION(),
  },
  heightSize: {
    itemType: 'INPUT',
    placeholder: `${getLabel('56234', '请输入')}`,
  },
});
interface CascadeRules {
  [key: string]: any;
}
const cascadeRules: CascadeRules = {
  widthType: {
    fixed: ['widthSize'],
  },
  heightType: {
    fixed: ['heightSize'],
  },
};
const INIT_DATA = (): FormDatas => ({
  widthType: 'fixed',
  heightType: 'fixed',
  sizeType: 'common',
  widthSize: 88,
  heightSize: 88,
});
export class NodeSizeStore {
  @observable node = new FormStore();
  @observable formStore = new FormStore();
  @observable loading = false;
  @observable isCommon = false; // 是否为通用设置
  getFormItem = () => {
    if (!this.isCommon) return { ...SIZE_FORM_ITEMS(), ...FORM_ITEMS() };
    return FORM_ITEMS();
  };
  getFormLayout = () => {
    if (!this.isCommon) return [...SIZE_FORM_LAYOUT, ...FORM_LAYOUT];
    return FORM_LAYOUT;
  };
  @action init = (data?: any, isCommon?: boolean, cb?: Function) => {
    this.isCommon = !!isCommon;
    this.initForm(() => {
      cb && cb();
      if (!isEmpty(data)) {
        this.updatePropsData(data);
      }
    });
  };
  @action initForm = (cb?: Function) => {
    let json = {
      layout: this.getFormLayout(),
      items: this.getFormItem(),
      data: INIT_DATA(),
      groups: [],
    };
    this.formStore.initForm(json);
    this.updateLayout();
    cb && cb();
  };
  updateLayout = () => {
    const formData = this.formStore.getFormDatas();
    if (this.isCommon) {
      // 通用设置生效
      this.formStore.setLayoutProps('widthType', { hide: false });
      this.formStore.setLayoutProps('heightType', { hide: false });
    } else {
      // 子节点生效
      const isHide = formData.sizeType === 'common';
      this.formStore.setLayoutProps('widthType', { hide: isHide });
      this.formStore.setLayoutProps('heightType', { hide: isHide });
      if (isHide) return;
    }
    const ruleKeys = Object.keys(cascadeRules);
    ruleKeys.forEach((key: any) => {
      const value = formData[key];
      if (!value) return false;
      const ruleKey = Object.keys(cascadeRules[key])[0];
      const ruleValue: string[] = cascadeRules[key][ruleKey];
      if (value !== ruleKey) {
        ruleValue.forEach((j) => {
          // 需要隐藏
          this.formStore.setLayoutProps(key, { items: [key] });
        });
      } else {
        this.formStore.setLayoutProps(key, { items: [key, ...ruleValue] });
      }
    });
  };
  omFormChange = (value?: FormDatas) => {
    runInAction(() => {
      // 处理联动
      this.updateLayout();
    });
  };
  @action reset = () => {
    this.formStore = new FormStore();
    this.node = new FormStore();
    this.isCommon = false;
    this.loading = false;
  };
  @action updatePropsData = (data: any = {}) => {
    runInAction(() => {
      const { height, width, sizeType = 'common' } = data;
      const value = {
        widthType: width.type,
        heightType: height.type,
        widthSize: width.size,
        heightSize: height.size,
        sizeType,
      };
      this.formStore.updateDatas(value);
      setTimeout(() => {
        this.updateLayout();
      }, 0);
    });
  };
}

const instance = new NodeSizeStore();
export type NodeSizeStoreType = Pick<NodeSizeStore, keyof NodeSizeStore>;
export default instance;
