/**
 * <AUTHOR>
 * @createTime 2022-02-22
 */
import { getLabel } from '@weapp/utils';
import { toJS } from 'mobx';
import { observer } from 'mobx-react';
import React from 'react';
import { ebdMindMapComsClsPrefix } from '../../../../../constants';
import { DesignComProps } from '../../../../mindMap/types';
import DataSort from '../../../../common/plugins/data-sort';
import './index.less';

@observer
export default class Sort extends React.PureComponent<DesignComProps> {
  handleChange = (data: any[]) => {
    const { onChange } = this.props;
    onChange(toJS(data));
  };

  render() {
    const { value = [], config } = this.props;
    const {
      dataset = {
        id: '',
        text: '',
        groupId: '',
        type: '',
      },
    } = config;
    const hasValue = value.length > 0;
    const btnElm = !hasValue ? getLabel('105962', '新建排序') : getLabel('105963', '编辑排序');
    return (
      <span className={`${ebdMindMapComsClsPrefix}-config-sort`}>
        <DataSort weId={`${this.props.weId || ''}_18yvex`} value={value} dataset={dataset!} onChange={this.handleChange} buttonType='default' btnElm={btnElm} />
      </span>
    );
  }
}
