/**
 * 布局切换组件
 */
import { useState, useEffect, useMemo } from 'react';
import { Dialog, Button, Checkbox, Icon } from '@weapp/ui';
import { getLabel } from '@weapp/utils';
import { ebdfClsPrefix } from '../../../../../constants';
import { MIND_VIEW, MIND_LINE_STYLE } from '../../../../../constants/enum';
import { MindMapConfigData } from '../../../../mindMap/types';
import './index.less';

interface IProps {
  weId?: string;
  value: MIND_VIEW.LOGICAL_STRUCTURE;
  onChange?: (value: MIND_VIEW) => void;
  onConfigChange?: (configs: any) => void;
  config: MindMapConfigData;
}
const cls = `${ebdfClsPrefix}-layoutSet`;
const diffKey = 'LayoutSet';
const getLayoutItems = () => [
  {
    id: MIND_VIEW.LOGICAL_STRUCTURE,
    name: getLabel('288331', '逻辑结构图'),
  },
  {
    id: MIND_VIEW.ORGANIZATION_STRUCTURE,
    name: getLabel('288332', '组织结构图'),
  },
  {
    id: MIND_VIEW.CATALOG_ORGANIZATION,
    name: getLabel('288333', '目录组织图'),
  },
  {
    id: MIND_VIEW.FISHBONE,
    name: getLabel('299420', '鱼骨图'),
  },
  {
    id: MIND_VIEW.MIND_MAP,
    name: getLabel('299421', '双向布局'),
  },
];
const LayoutSet = (props: IProps) => {
  const [visible, setVisible] = useState(false);
  const [layout, setLayout] = useState(MIND_VIEW.LOGICAL_STRUCTURE);
  const getLineStyle = (layout: string) => {
    let lineStyle = MIND_LINE_STYLE.CURVE;
    switch (layout) {
      case MIND_VIEW.LOGICAL_STRUCTURE:
      case MIND_VIEW.MIND_MAP:
        lineStyle = MIND_LINE_STYLE.CURVE;
        break;
      case MIND_VIEW.ORGANIZATION_STRUCTURE:
      case MIND_VIEW.CATALOG_ORGANIZATION:
      case MIND_VIEW.FISHBONE:
        lineStyle = MIND_LINE_STYLE.STRAIGHT;
        break;
      default:
        break;
    }
    return lineStyle;
  };
  const onSure = () => {
    setVisible(false);
    // 还需要同步更新相匹配的连线样式
    props.onConfigChange?.({ mindLayout: layout, lineStyle: getLineStyle(layout) });
    props.onChange?.(layout);
  };
  const onCancel = () => {
    setVisible(false);
    const originValue = props.config?.mindLayout!;
    setLayout(originValue);
  };
  const showDlg = () => {
    setLayout(props.value);
    setVisible(true);
  };
  const renderContent = useMemo(() => {
    const renderLayoutItem = (item: any) => {
      const img = require(`../../../../../../public/images/layouts/${item.id}.png`).default || '';
      return (
        <div
          key={item.id}
          className={`${cls}-content-item`}
          onClick={() => {
            setLayout(item.id);
          }}
        >
          <div className={`${cls}-content-item-label`}>
            <div className={`${cls}-content-item-label-wrapper`}>
              <Checkbox weId={`${props.weId || ''}_9m3y5a`} value={item.id === layout} />
              {item.name}
            </div>
          </div>
          <div className={`${cls}-content-item-content`}>
            <img src={img} alt={''} className={`${cls}-content-item-img`} />
          </div>
        </div>
      );
    };
    return <>{getLayoutItems().map((i) => renderLayoutItem(i))}</>;
  }, [layout, props.weId]);
  const renderModal = useMemo(() => {
    const getFooter = () => [
      <Button weId={`${props.weId || ''}_c154yn@${diffKey}`} key='sure' type='primary' onClick={onSure}>
        {getLabel('40565', '确定')}
      </Button>,
      <Button weId={`${props.weId || ''}_wsv4mi@${diffKey}`} key='cancel' onClick={onCancel}>
        {getLabel('53937', '取消')}
      </Button>,
    ];
    const commonStyle = () => ({
      background: '#fff',
    });
    if (!visible) {
      return null;
    }
    return (
      <Dialog
        weId={`${props.weId || ''}_5ifhrl`}
        title={getLabel('288330', '布局设置')}
        closable
        mask
        width={760}
        onClose={() => setVisible(false)}
        destroyOnClose
        visible={visible}
        footer={getFooter()}
        footerStyle={{
          ...commonStyle(),
          borderTop: '1px solid #e5e5e5',
        }}
        bodyStyle={commonStyle()}
      >
        <div className={`${cls}-content`}>{renderContent}</div>
      </Dialog>
    );
  }, [visible, layout, props.weId]);
  return (
    <div className={cls}>
      <div className={`${cls}-btn`}>
        <Icon weId={`${props.weId || ''}_099mgb`} name='Icon-set-up-o' onClick={showDlg}></Icon>
      </div>
      {renderModal}
    </div>
  );
};
export default LayoutSet;
