@import (reference) '../../../../style/index.less';

.@{prefix}-mindmap-node-slider {
  .ui-dialog-body-container {
    background: var(--base-white);
  }
  .ui-title-inDialog {
    padding: 0;
  }
  .ui-dialog-content-right {
    box-shadow: none;
  }

  .ui-select {
    width: 100%;
    max-width: 100%;
  }
  &-node-size {
    &-formHide {
     .ui-form-row-first {
      border-bottom: none;
     }
    }
  }
  &-node-style, &-node-size {
    display: flex;
    justify-content: flex-end;
  }
  .ebcoms-collapse>.ui-collapse-panel--inactive{
    border-bottom: none;
  }
  // 兼容点击标题右侧区域icon不按折叠状态展示的问题
  .ui-collapse-panel--has-arrow.ui-collapse-panel--inactive .ui-icon{
    transform: rotate(0)
  }
  .ui-collapse-panel--has-arrow.ui-collapse-panel--active .ui-icon{
    transform: rotate(-90deg)
  }
  // 显示字段
  .ebcoms-field-view{
    .ebcoms-field-view-name>span{
      max-width: 140px
    }
  }
  &-rootIconEditor{
    .ebcoms-assets-icon-setting{
      display: none;
    }
  }
}

.@{prefix}-mindmap-node-slider-top {
  height: 100%;
  display: flex;
  align-items: center;
  padding-left: 16px;
  color: var(--regular-fc);
  font-size: var(--font-size-12);
  &-icon {
    margin-right: 5px;
    color: var(--secondary-fc);
    cursor: pointer;
  }
}
.@{prefix}-dataset-config.hide-perm {
  .ebcomponents-dataset-view{
    .ebcomponents-dataset-view-permissions{
      display: none;
    }
  }
}

.ebcoms-config-MindMap{
  .ui-collapse-panel .ui-collapse-panel__title .ebcoms-collapse{
    .ui-collapse-panel .ui-collapse-panel__title{
      max-width: 176px;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
    }
  }
}