import { CorsComponent } from '@weapp/ui';
import { isObject } from "@weapp/utils"
// import isObject from 'lodash-es/isObject';
import React from 'react';
import { ebdfClsPrefix } from '../../../../constants';
import { DesignProps } from '../../types';
import './index.less';

interface IProps {
  onChange: (val: any) => void;
  value: any;
  pageId: string;
}

export default class RootConfigIconEidtor extends React.PureComponent<DesignProps & IProps> {
  onChange = (val: any) => {
    const { value = {}, onChange } = this.props;
    const _value = isObject(value) ? value : {};
    onChange({ ..._value, ...val });
  };

  render() {
    const { config = {}, value, pageId } = this.props;
    return (
      <div className={`${ebdfClsPrefix}-mindmap-node-slider-rootIconEditor`}>
        <CorsComponent app='@weapp/ebdcoms' compName='IconEidtor' weId={`${this.props.weId || ''}_nfd0zv`} value={value} onChange={this.onChange} editConfig={{ isHideFontSize: true }} canEditTitle />
      </div>
    );
  }
}
