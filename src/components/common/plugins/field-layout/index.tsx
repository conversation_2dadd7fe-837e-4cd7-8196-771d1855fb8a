/**
 * <AUTHOR>
 * @createTime 2021-11-24
 * @desc  显示字段配置
 */
import { DataSet } from '@weapp/ebdcoms';
import { CorsComponent } from '@weapp/ui';
import { getLabel } from '@weapp/utils';
import { toJS } from 'mobx';
import { observer } from 'mobx-react';
import React, { PureComponent } from 'react';
import { AnyObj } from '../../../../types/common';
import { NotUseFields, ebdfClsPrefix } from "../../../../constants";
import './index.less'

type ConfigType = {
  dataset: DataSet;
};

export type FieldConfig = {
  cardLayout: any;
  field: any;
  tableField: any;
};

interface FieldLayoutProps extends React.Attributes {
  config: ConfigType;
  value: FieldConfig;
  onChange: (val: any) => void;
  onConfigChange?: (value?: FieldConfig) => void;
  externalElement?: (config: any, showDialog: any) => React.ReactNode;
  externalDftStyle?: AnyObj;
  dataset?: DataSet;
  needLink?: boolean; // 是否需要支持动作链接
  page?: any
}

export enum ListModeType {
  TABLE = '1', // 表格 默认
  LIST = '2', // 列表
  Excel = '3', // 列表
}

@observer
export default class FieldLayout extends PureComponent<FieldLayoutProps> {
  fieldChange = (fields: FieldConfig | any) => {
    const { value } = this.props;
    const _value = typeof value === 'string' ? JSON.parse(value) : value;
    this.props.onChange(toJS({
      field: _value?.field || [], // 由于直接点击外部的删除，这个属性会丢失，因此这里取一下之前的
      ...fields,
    }));
  };
  fieldFilter = (field:any)=>{
    //屏蔽不支持字段
    if (NotUseFields.includes(field?.id)) {
      return false
    }
    return true
  }

  render() {
    const {
      value = {},
      config,
      externalElement,
      externalDftStyle = {},
      dataset,
      needLink = true,
      page = {}
    } = this.props;
    const _value = typeof value === 'string' ? JSON.parse(value) : value;
    return (
      <CorsComponent
        weId={`${this.props.weId || ''}_x7skym`}
        // app="@weapp/ebdcoms"
        app="@weapp/ebdlist"
        compName="ListField"
        placeholder={getLabel('87540', '点击设置字段')}
        config={{
          ...config,
          mode: ListModeType.LIST,
          ..._value,
          cardLayout: toJS(_value?.cardLayout || {}),
          field: _value.field || [],
          dataset: config?.dataset || dataset,
        }}
        value={toJS(_value.cardLayout) || {}}
        onConfigChange={this.fieldChange}
        externalElement={externalElement}
        externalDftStyle={externalDftStyle}
        // onChange={onChange}
        // 添加链接动作需要加的参数
        // ebModuleType={needLink ? 'EBFORM' : ''}
        // pageScope={needLink ? 'EB_FORM_VIEW' : ''}
        appid={config?.dataset?.groupId}
        canSetEventAction
        fieldFilter={this.fieldFilter}
        page={page}
        // 屏蔽掉明细子表
        needDetail={false}
        className={`${ebdfClsPrefix}-layoutCard`}
      />
    );
  }
}
