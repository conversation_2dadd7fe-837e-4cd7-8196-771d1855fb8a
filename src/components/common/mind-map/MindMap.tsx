import { <PERSON><PERSON>, <PERSON><PERSON>uickMenu, <PERSON><PERSON>, Spin, AnyObj } from '@weapp/ui';
import { classnames as classNames, getLabel, middleware } from '@weapp/utils';
import React, { ReactNode, createRef } from 'react';
import Draggable from 'react-draggable';
import { Else, If, Then } from 'react-if';
import { appName, mindMapViewClsPrefix, MindDefaultPluginName, EbdMindMapEventKeys, ADVANCE_MIND_DEFAULT_CONFIG, A_MIND_ZOOM_MAX_COUNT } from '../../../constants';
import { MIND_VIEW, MIND_LINE_STYLE } from '../../../constants/enum';
import { invoke, isIE, hasRegisterFuncInPlugin, isAdvancedView } from '../../../utils';
import { toJS } from 'mobx';
import { MindMapProps } from './types';
import { handleBindEvent, loadMindMapDepend, formatDataToV2Mind, fullScreen, handleOffEvent, getChildrenLevel } from './utils';
import { COMP_DEFAULT_CONFIG } from '../constants';

import './style/index.less';

interface MindMapState {
  percent: number;
  fullScreen: boolean;
  horizontalScreen: boolean;
  showLegendPanel: boolean;
  transformValue: string;
  firstClickFlag: boolean;
  defaultZoomIn: number; // 最小所放比例
  defaultZoomOut: number; // 最大所放比例
  loading: boolean; // 导图渲染loading状态
  openPerformance: boolean; // 是否开启性能模式
}
@middleware(appName, 'MindMap')
class MindMap extends React.PureComponent<MindMapProps, MindMapState> {
  wrapperRef = createRef<HTMLDivElement>();

  mindRef = createRef<HTMLDivElement>();

  jm: any = null;
  timer: any = null;

  constructor(props: MindMapProps) {
    super(props);
    this.state = {
      percent: 100,
      fullScreen: false,
      horizontalScreen: false, // 横屏展示
      showLegendPanel: false, // 显示图例
      transformValue: 'matrix(1, 0, 0, 1, 0, 0)',
      firstClickFlag: true,
      defaultZoomIn: 50, // 默认最小缩放比例
      defaultZoomOut: 200, // 默认最大缩放比例
      loading: true,
      openPerformance: false,
    };
  }

  componentDidMount() {
    this.reset();
    this.renderMind();
    this.props.onRef?.(this.wrapperRef);
  }
  // 用完后销毁
  componentWillUnmount() {
    this.reset();
    this.mindRef = createRef<HTMLDivElement>();
    this.wrapperRef = createRef<HTMLDivElement>();
  }
  reset = () => {
    this.timer = null;
    this.unBindEvent();
    this.setState({ loading: true });
    clearInterval(this.timer);
    if (this.jm) {
      this.jm.destroy();
      this.jm = null;
    }
  };
  handleEvent = (cb?: Function) => {
    const { isFirstMount, parentProps } = this.props;
    const { pluginCenter, store, events, compId } = parentProps;
    if (isFirstMount) {
      invoke(pluginCenter, MindDefaultPluginName, EbdMindMapEventKeys.onDidMount, { args: [{ store, jmInstance: this.jm }] });
      events && events.emit && events.emit(`${EbdMindMapEventKeys.onDidMount}_${compId}`, compId, { store, jmInstance: this.jm });
    } else {
      // 更新
      events && events.emit && events.emit(`${EbdMindMapEventKeys.onPluDidUpdate}_${compId}`, { props: this.props });
    }
    handleBindEvent(this);
    cb && cb();
  };
  unBindEvent = () => {
    if (this.jm) {
      this.jm.off('scale', this.listenScale);
      this.jm.off('node_tree_render_end', this.listenRenderEnd);
    }
    handleOffEvent(this);
  };
  // 兼容刷新时容器节点还未渲染成功导致插件报错问题
  renderMind = () => {
    this.timer = setInterval(() => {
      const mindEle = this.mindRef.current as HTMLDivElement;
      if (mindEle) {
        const elRect = mindEle.getBoundingClientRect();
        if (elRect.width && elRect.height) {
          clearInterval(this.timer);
          this.initJSmind();
        }
      }
    }, 100);
  };
  listenScale = (scale: number) => {
    this.setState({ percent: +(scale * 100).toFixed(0) });
  };
  listenRenderEnd = () => {
    let { getInstance, defaultExpendDepth, parentProps } = this.props;
    let { pluginCenter } = parentProps;
    const cacheExpand = sessionStorage.getItem('cacheExpand');
    // 先绑定事件
    this.handleEvent(() => {
      getInstance && getInstance(this.jm);
      // !处理性能模式下展开收起 - 现阶段原插件存在此bug 会有游离节点没清除
      if (cacheExpand) {
        if (cacheExpand === 'UNEXPAND_ALL') {
          this.jm.execCommand('UNEXPAND_ALL', true);
        }
        if (cacheExpand === 'EXPAND_ALL') {
          this.jm.execCommand('EXPAND_ALL');
        }
        sessionStorage.removeItem('cacheExpand');
        setTimeout(() => {
          this.moveToCenter();
          this.setState({ loading: false }, () => {
            this.jm.updateConfig({ openPerformance: true });
          });
        }, 300);
        return;
      } else {
        if (defaultExpendDepth && !hasRegisterFuncInPlugin(pluginCenter, MindDefaultPluginName, EbdMindMapEventKeys.onSetExpandLevel)) {
          this.expand2level(defaultExpendDepth);
          setTimeout(() => {
            this.moveToCenter();
            this.setState({ loading: false });
          }, 300);
          return;
        }
        this.setState({ loading: false });
      }
    });
  };
  initJSmind = async () => {
    let { openPerformance } = this.state;
    let { nodeRenderCustom, draggable = true, datas, isHideRoot, funcSetting = COMP_DEFAULT_CONFIG().funcSetting, parentProps } = this.props;
    const { pluginCenter, config } = parentProps;
    const { lineStyle = MIND_LINE_STYLE.CURVE, mindLayout = MIND_VIEW.LOGICAL_STRUCTURE, commonNodeStyleSet, nodeTreeList } = config;
    const mindEle = this.mindRef.current as HTMLDivElement;
    let { isShowExpandNum } = funcSetting.base;
    const cacheExpand = sessionStorage.getItem('cacheExpand');
    // 节点数大于1000且为逻辑结构图曲线时，默认开启性能模式 3000-4000个节点会有明显卡顿 4000+不建议了
    // if (datas.data.length > 1000 && lineStyle === MIND_LINE_STYLE.CURVE && mindLayout === MIND_VIEW.LOGICAL_STRUCTURE) {
    if (datas.data.length > 1000) {
      openPerformance = true;
      this.setState({ openPerformance });
    }
    // 是双向布局或者是鱼骨图布局 隐藏根节点无效
    if (mindLayout === MIND_VIEW.FISHBONE || mindLayout === MIND_VIEW.MIND_MAP) {
      isHideRoot = false;
    }
    loadMindMapDepend(draggable, () => {
      const JsMind = window.simpleMindMap;
      // 格式化数据 处理自定义节点样式
      const newDatas: AnyObj = formatDataToV2Mind(datas.data as any, commonNodeStyleSet);
      // 最大层级数
      const maxFloor = getChildrenLevel(datas.data as any[]);
      const options = {
        el: mindEle,
        data: newDatas,
        hideRoot: isHideRoot,
        isShowExpandNum,
        customCreateNodeContent: nodeRenderCustom,
        openPerformance: cacheExpand ? false : openPerformance, // 兼容处理性能模式下展开收起异常
        performanceConfig: {
          time: 250,
          padding: 500,
          removeNodeWhenOutCanvas: true,
        },
        // 是否禁止双指移动画布
        isDisableTwoFingerMoveCanvas: !newDatas.children.length,
        maxFloor,
        // 有效：节点拖拽结束后
        // afterDraggedNode: (cancel: Function) => {
        //   // 取消拖拽
        //   cancel()
        // }
      };
      const themeProps = ADVANCE_MIND_DEFAULT_CONFIG({ key: mindLayout, options, totalLen: datas.data.length, lineStyle });
      // 插件包：拦截分组数据并返回新处理的数据
      const [_themeProps] = invoke(pluginCenter, MindDefaultPluginName, EbdMindMapEventKeys.getThemeConfig, {
        args: [themeProps],
      }) || [themeProps];
      this.jm = new JsMind(_themeProps);
      this.jm.on('scale', this.listenScale);
      // 导图渲染完毕
      this.jm.on('mind_map_position_adapt_end', this.listenRenderEnd);
    });
  };

  mindZoomOut = () => {
    const { defaultZoomIn, percent } = this.state;
    const currentPercent = percent - 10;
    if (currentPercent >= defaultZoomIn) {
      this.setState({ percent: currentPercent });
      this.jm.view.narrow();
      this.jm.getIsShowScrollBar();
    }
  };

  mindZoomIn = () => {
    const { defaultZoomOut, percent } = this.state;
    const currentPercent = percent + 10;
    if (currentPercent <= defaultZoomOut) {
      this.setState({ percent: currentPercent });
      this.jm.view.enlarge();
      this.jm.getIsShowScrollBar();
    }
  };

  mindFullScreen = () => {
    this.setState((pre: any) => ({ fullScreen: !pre.fullScreen }));
  };
  toFullscreenShow = () => {
    fullScreen(this.jm.el);
  };
  // 画布居中
  moveToCenter = () => {
    this.jm.setMindMapCenter({ dblclick: true });
  };
  handleMindRerender = () => {
    const { openPerformance } = this.state;
    this.setState({ loading: true });
    const makeCenter = () => {
      this.jm.getIsShowScrollBar();
      setTimeout(() => {
        this.moveToCenter();
        this.setState({ loading: false });
      }, 300);
    };
    // !性能模式下连线折叠展开有问题 需重新渲染一次
    if (openPerformance) {
      this.jm && this.jm.reRender(makeCenter);
      return;
    }
    makeCenter();
  };

  expandAll = () => {
    const { openPerformance } = this.state;
    const { refreshView } = this.props;
    if (openPerformance) {
      sessionStorage.setItem('cacheExpand', 'EXPAND_ALL');
      refreshView && refreshView();
      return;
    }
    this.jm && this.jm.execCommand('EXPAND_ALL');
    this.handleMindRerender();
  };

  collapseAll = () => {
    const { refreshView } = this.props;
    const { openPerformance } = this.state;
    if (!this.jm) return;
    if (openPerformance) {
      sessionStorage.setItem('cacheExpand', 'UNEXPAND_ALL');
      refreshView && refreshView();
      return;
    }
    // * false 表示画布不重新定位居中
    this.jm.execCommand('UNEXPAND_ALL', true);
    this.handleMindRerender();
  };

  expand2level(num: number) {
    switch (num) {
      case -1:
        this.collapseAll();
        break;
      case 0:
        this.expandAll();
        break;
      default:
        this.jm && this.jm.execCommand('UNEXPAND_TO_LEVEL', num);
        break;
    }
  }

  changeAppDirection = () => {
    const width = (this.mindRef.current as any).clientWidth;
    const height = (this.mindRef.current as any).clientHeight;
    const wrapper = document.documentElement;
    const fullHeight = wrapper ? wrapper.clientHeight : height;
    let style = '';
    if (this.state.horizontalScreen) {
      // 横屏状态
      if (width >= height) {
        // 转为竖屏
        style += 'width:100%;';
        style += 'height:100%;';
        style += '-webkit-transform: rotate(0); transform: rotate(0);';
        style += '-webkit-transform-origin: 0 0;';
        style += 'transform-origin: 0 0;';
      }
    } else if (width < height) {
      style += `width:${fullHeight}px;`; // 注意旋转后的宽高切换
      style += `height:${width}px;`;
      style += '-webkit-transform: rotate(90deg); transform: rotate(90deg);';
      // 注意旋转中点的处理
      style += `-webkit-transform-origin: ${width / 2}px ${width / 2}px;`;
      style += `transform-origin: ${width / 2}px ${width / 2}px;`;
    }
    (this.mindRef.current as any).style.cssText = style;
    this.setState((prevState: any) => ({
      fullScreen: !prevState.fullScreen,
      horizontalScreen: !prevState.horizontalScreen,
    }));
  };
  renderFooterMenus = () => {
    const { percent } = this.state;
    const { funcSetting } = this.props;
    let menu: ReactNode[] = [];
    let extraFuncEle: any = {
      fullScreen: (
        <Popover weId={`${this.props.weId || ''}_5gxg8k`} popup={getLabel('261408', '全屏查看')} placement='top' popoverType='tooltip'>
          <span onClick={this.toFullscreenShow}>
            <Icon weId={`${this.props.weId || ''}_pl19m8`} name='Icon-Full-screen' />
          </span>
        </Popover>
      ),
    };
    if (!isAdvancedView()) {
      extraFuncEle = {};
    }
    const elems: any = {
      goRoot: (
        <Popover weId={`${this.props.weId || ''}_t8jm1n`} popup={getLabel('261991', '回到根节点')} placement='top' popoverType='tooltip'>
          <span onClick={this.moveToCenter}>
            <Icon weId={`${this.props.weId || ''}_05ct1s`} name='Icon-reduction' />
          </span>
        </Popover>
      ),
      scale: (
        <>
          <span onClick={this.mindZoomOut}>
            <Icon weId={`${this.props.weId || ''}_l6kzoe`} name='Icon-reduce01' />
          </span>
          <span className='mind-zoom-percent'>{percent}%</span>
          <span onClick={this.mindZoomIn}>
            <Icon weId={`${this.props.weId || ''}_e99u7n`} name='Icon-add-to01' />
          </span>
          {/* <span onClick={this.mindFullScreen}>
          {fullScreen ? (
            <Icon weId={`${this.props.weId || ''}_m96ezn`} name="Icon-Global-zoom-in" />
          ) : (
            <Icon weId={`${this.props.weId || ''}_2jdz9y`} name="Icon-Global-reduction" />
          )}
        </span> */}
        </>
      ),
      ...extraFuncEle,
      expand_all: (
        <Popover weId={`${this.props.weId || ''}_twin8d`} popup={getLabel('55985', '全部展开')} placement='top' popoverType='tooltip'>
          <span onClick={this.expandAll}>
            <Icon weId={`${this.props.weId || ''}_ypg02e`} name='Icon-drop-down-menu' />
          </span>
        </Popover>
      ),
      collapse_all: (
        <Popover weId={`${this.props.weId || ''}_71vbwh`} popup={getLabel('247902', '全部折叠')} placement='top' popoverType='tooltip'>
          <span onClick={this.collapseAll}>
            <Icon weId={`${this.props.weId || ''}_e99u7n`} name='Icon-collapse-menu' />
          </span>
        </Popover>
      ),
    };
    funcSetting?.toolbar.forEach((item) => {
      const key = Object.keys(item)[0];
      if (key && item[key]) {
        // 非ie下支持全屏操作
        if (key === 'fullScreen' && !isIE()) {
          menu.push(elems[key]);
        } else {
          menu.push(elems[key]);
        }
      }
    });
    return (
      <>
        {menu.map((item, idx) => {
          return <div key={idx}>{item}</div>;
        })}
      </>
    );
  };
  renderLengend = () => {
    const { store } = this.props.parentProps;
    const { legendMap } = store!;
    if (legendMap.size === 0) return null;
    const legendDom: any = [];
    legendMap.forEach((value: any, key: number) => {
      let { id = '', name: _name = '', styleSetting = {}, time = '0' } = toJS(value) || {};
      const name = typeof _name === 'string' ? _name : _name?.nameAlias;
      const { color = '#666', textColor = '#fff', countColor = '#333' } = styleSetting;
      const itemNameStyle = name === '' ? {} : { backgroundColor: color, color: textColor };
      legendDom.push(
        <div className={`${mindMapViewClsPrefix}-lengend-item`} key={key + id}>
          <div className={`${mindMapViewClsPrefix}-lengend-item-name`} style={itemNameStyle} title={name}>
            {name}
          </div>
          <div className={`${mindMapViewClsPrefix}-lengend-item-time`} style={{ color: countColor }} title={time}>
            {time}
          </div>
        </div>
      );
    });
    return (
      <div className={`legend-container-content`}>
        <div className='folder-arrow' onClick={this.showLegendPanel}>
          <Icon weId={`${this.props.weId || ''}_q5bkmc`} name='Icon-up-arrow03' size='s' />
        </div>
        <div className='all-item'>{legendDom}</div>
      </div>
    );
  };

  //处理图例面板拖动与点击事件两者兼容存在的问题
  showLegendPanel = (event: any) => {
    event.stopPropagation();
    const { compId } = this.props.parentProps;
    const { transformValue } = this.state;
    let dom = document.querySelector(`#${compId} .${mindMapViewClsPrefix}-legend-box`);
    if ((dom && getComputedStyle(dom).transform === transformValue) || !dom) {
      this.setState((prevState: any) => {
        return { showLegendPanel: !prevState.showLegendPanel };
      });
    }
  };

  draggableMouseDown = (event: any) => {
    event.stopPropagation();
    const { compId } = this.props.parentProps;
    const { transformValue, firstClickFlag } = this.state;
    let dom = document.querySelector(`#${compId} .${mindMapViewClsPrefix}-legend-box`);
    if (firstClickFlag) {
      this.setState((prevState: any) => {
        return { showLegendPanel: !prevState.showLegendPanel, firstClickFlag: false };
      });
    } else if (dom) {
      let translateX = getComputedStyle(dom).transform;
      this.setState({
        transformValue: translateX,
      });
    }
  };

  getLegendContainerStyle = () => {
    const { compId } = this.props.parentProps;
    const { showLegendPanel } = this.state;
    const { legendMap } = this.props?.parentProps?.store!;
    let mindmapvContainerDom: any = document.querySelector(`#${compId} .weapp-ebdmindmap-mindmapv`);
    let isOK = false,
      isNotEmpty = legendMap.size !== 0;
    if (mindmapvContainerDom && isNotEmpty) {
      isOK = mindmapvContainerDom?.getBoundingClientRect()?.height < legendMap.size * 35 + 27 + 60 + 45; //item*35 + 27 箭头+ 60 缩放底部  +45 底部距离
    }
    let legendContainerStyle = {};
    if (isNotEmpty && isOK && showLegendPanel) {
      legendContainerStyle = {
        height: '100%',
      };
    }
    return legendContainerStyle;
  };
  render() {
    const { fullScreen, horizontalScreen, showLegendPanel, loading } = this.state;
    const { prefixCls, className, style, parentProps, isFirstMount } = this.props;
    const { store } = parentProps;
    const cls = classNames(prefixCls, className, {
      [`${prefixCls}-full-screen`]: fullScreen,
    });
    const { legendMap } = store!;
    const cacheExpand = sessionStorage.getItem('cacheExpand');
    const spinning = cacheExpand ? false : isFirstMount && loading;
    return (
      <div className={cls} style={{ ...style }} ref={this.wrapperRef}>
        <Spin spinning={spinning} weId={`${this.props.weId || ''}_xrmzqc`}>
          <div ref={this.mindRef} className={`${prefixCls}-container fade-container ${!loading ? 'fade-in ' : 'hide'}`} style={{ overflow: 'hidden' }} />
        </Spin>
        <If weId={`${this.props.weId || ''}_u3tywy`} condition={parentProps?.isMobile && isIE()}>
          <Then weId={`${this.props.weId || ''}_20r20y`}>
            <MQuickMenu
              weId={`${this.props.weId || ''}_mmq2ll`}
              className={`${mindMapViewClsPrefix}-quick-menu`}
              menuClassName={`${mindMapViewClsPrefix}-quick-menu-item`}
              onClick={this.changeAppDirection}
              quickIcon={<Icon weId={`${this.props.weId || ''}_a5f37x`} name={horizontalScreen ? 'Icon-narrow01' : 'Icon-enlarge01'} />}
            />
          </Then>
          <Else weId={`${this.props.weId || ''}_jhquzn`}>
            <div className={`${mindMapViewClsPrefix}-footer`}>{this.renderFooterMenus()}</div>
            {legendMap.size !== 0 && (
              <Draggable weId={`${this.props.weId || ''}_pduo2k`} key={'renderKey'} bounds={'parent'} onMouseDown={this.draggableMouseDown} defaultPosition={{ x: 0, y: 0 }} disabled={true}>
                <div className={`${mindMapViewClsPrefix}-${showLegendPanel ? 'legend-container' : 'legend-box'}`} style={this.getLegendContainerStyle()}>
                  {!showLegendPanel ? (
                    <span className='text' onClick={this.showLegendPanel} title={getLabel('84310', '图例')}>
                      {getLabel('84310', '图例')}
                    </span>
                  ) : (
                    this.renderLengend()
                  )}
                </div>
              </Draggable>
            )}
          </Else>
        </If>
      </div>
    );
  }
}

export default MindMap;
