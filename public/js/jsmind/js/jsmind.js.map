{"version": 3, "file": "jsmind.js", "sources": ["../src/jsmind.common.js", "../src/jsmind.dom.js", "../src/jsmind.util.js", "../src/jsmind.option.js", "../src/jsmind.node.js", "../src/jsmind.mind.js", "../src/jsmind.format.js", "../src/jsmind.data_provider.js", "../src/jsmind.layout_provider.js", "../src/jsmind.graph.js", "../src/jsmind.view_provider.js", "../src/jsmind.shortcut_provider.js", "../src/jsmind.plugin.js", "../src/jsmind.js"], "sourcesContent": ["export const __version__ = '0.8.3';\nexport const __author__ = '<EMAIL>';\n\nif (typeof String.prototype.startsWith != 'function') {\n    String.prototype.startsWith = function (p) {\n        return this.slice(0, p.length) === p;\n    };\n}\n\nexport const Direction = {\n    left: -1,\n    center: 0,\n    right: 1,\n    of: function (dir) {\n        if (!dir || dir === -1 || dir === 0 || dir === 1) {\n            return dir;\n        }\n        if (dir === '-1' || dir === '0' || dir === '1') {\n            return parseInt(dir);\n        }\n        if (dir.toLowerCase() === 'left') {\n            return this.left;\n        }\n        if (dir.toLowerCase() === 'right') {\n            return this.right;\n        }\n        if (dir.toLowerCase() === 'center') {\n            return this.center;\n        }\n    },\n};\nexport const EventType = { show: 1, resize: 2, edit: 3, select: 4 };\nexport const Key = { meta: 1 << 13, ctrl: 1 << 12, alt: 1 << 11, shift: 1 << 10 };\nexport const LogLevel = { debug: 1, info: 2, warn: 3, error: 4, disable: 9 };\n\n// an noop function define\nvar _noop = function () {};\nexport let logger =\n    typeof console === 'undefined'\n        ? {\n              level: _noop,\n              log: _noop,\n              debug: _noop,\n              info: _noop,\n              warn: _noop,\n              error: _noop,\n          }\n        : {\n              level: setup_logger_level,\n              log: console.log,\n              debug: console.debug,\n              info: console.info,\n              warn: console.warn,\n              error: console.error,\n          };\n\nfunction setup_logger_level(log_level) {\n    if (log_level > LogLevel.debug) {\n        logger.debug = _noop;\n    } else {\n        logger.debug = console.debug;\n    }\n    if (log_level > LogLevel.info) {\n        logger.info = _noop;\n    } else {\n        logger.info = console.info;\n    }\n    if (log_level > LogLevel.warn) {\n        logger.warn = _noop;\n    } else {\n        logger.warn = console.warn;\n    }\n    if (log_level > LogLevel.error) {\n        logger.error = _noop;\n    } else {\n        logger.error = console.error;\n    }\n}\n", "class Dom {\n    constructor(w) {\n        this.w = w;\n        this.d = w.document;\n        this.g = function (id) {\n            return this.d.getElementById(id);\n        };\n        this.c = function (tag) {\n            return this.d.createElement(tag);\n        };\n        this.t = function (n, t) {\n            if (n.hasChildNodes()) {\n                n.firstChild.nodeValue = t;\n            } else {\n                n.appendChild(this.d.createTextNode(t));\n            }\n        };\n\n        this.h = function (n, t) {\n            if (t instanceof HTMLElement) {\n                n.innerHTML = '';\n                n.appendChild(t);\n            } else {\n                n.innerHTML = t;\n            }\n        };\n        // detect isElement\n        this.i = function (el) {\n            return (\n                !!el &&\n                typeof el === 'object' &&\n                el.nodeType === 1 &&\n                typeof el.style === 'object' &&\n                typeof el.ownerDocument === 'object'\n            );\n        };\n\n        //target,eventType,handler\n        this.on = function (t, e, h) {\n            if (!!t.addEventListener) {\n                t.addEventListener(e, h, false);\n            } else {\n                t.attachEvent('on' + e, h);\n            }\n        };\n    }\n}\n\nexport const $ = new Dom(window);\n", "import { $ } from './jsmind.dom.js';\n\nexport const util = {\n    file: {\n        read: function (file_data, fn_callback) {\n            var reader = new FileReader();\n            reader.onload = function () {\n                if (typeof fn_callback === 'function') {\n                    fn_callback(this.result, file_data.name);\n                }\n            };\n            reader.readAsText(file_data);\n        },\n\n        save: function (file_data, type, name) {\n            var blob;\n            if (typeof $.w.Blob === 'function') {\n                blob = new Blob([file_data], { type: type });\n            } else {\n                var BlobBuilder =\n                    $.w.BlobBuilder ||\n                    $.w.MozBlobBuilder ||\n                    $.w.WebKitBlobBuilder ||\n                    $.w.MSBlobBuilder;\n                var bb = new BlobBuilder();\n                bb.append(file_data);\n                blob = bb.getBlob(type);\n            }\n            if (navigator.msSaveBlob) {\n                navigator.msSaveBlob(blob, name);\n            } else {\n                var URL = $.w.URL || $.w.webkitURL;\n                var blob_url = URL.createObjectURL(blob);\n                var anchor = $.c('a');\n                if ('download' in anchor) {\n                    anchor.style.visibility = 'hidden';\n                    anchor.href = blob_url;\n                    anchor.download = name;\n                    $.d.body.appendChild(anchor);\n                    var evt = $.d.createEvent('MouseEvents');\n                    evt.initEvent('click', true, true);\n                    anchor.dispatchEvent(evt);\n                    $.d.body.removeChild(anchor);\n                } else {\n                    location.href = blob_url;\n                }\n            }\n        },\n    },\n\n    json: {\n        json2string: function (json) {\n            return JSON.stringify(json);\n        },\n        string2json: function (json_str) {\n            return JSON.parse(json_str);\n        },\n        merge: function (b, a) {\n            for (var o in a) {\n                if (o in b) {\n                    if (\n                        typeof b[o] === 'object' &&\n                        Object.prototype.toString.call(b[o]).toLowerCase() == '[object object]' &&\n                        !b[o].length\n                    ) {\n                        util.json.merge(b[o], a[o]);\n                    } else {\n                        b[o] = a[o];\n                    }\n                } else {\n                    b[o] = a[o];\n                }\n            }\n            return b;\n        },\n    },\n\n    uuid: {\n        newid: function () {\n            return (\n                new Date().getTime().toString(16) + Math.random().toString(16).substring(2)\n            ).substring(2, 18);\n        },\n    },\n\n    text: {\n        is_empty: function (s) {\n            if (!s) {\n                return true;\n            }\n            return s.replace(/\\s*/, '').length == 0;\n        },\n    },\n};\n", "import { util } from './jsmind.util.js';\n\nconst default_options = {\n    container: '', // id of the container\n    editable: false, // you can change it in your options\n    theme: null,\n    mode: 'full', // full or side\n    support_html: true,\n    log_level: 'info',\n\n    view: {\n        engine: 'canvas',\n        hmargin: 100,\n        vmargin: 50,\n        line_width: 2,\n        line_color: '#555',\n        line_style: 'curved', // [straight | curved]\n        draggable: false, // drag the mind map with your mouse, when it's larger that the container\n        hide_scrollbars_when_draggable: false, // hide container scrollbars, when mind map is larger than container and draggable option is true.\n        node_overflow: 'hidden', // [hidden | wrap]\n        zoom: {\n            min: 0.5,\n            max: 2.1,\n            step: 0.1,\n        },\n        custom_node_render: null,\n        expander_style: 'char', // [char | number]\n    },\n    layout: {\n        hspace: 30,\n        vspace: 20,\n        pspace: 13,\n        cousin_space: 0,\n    },\n    default_event_handle: {\n        enable_mousedown_handle: true,\n        enable_click_handle: true,\n        enable_dblclick_handle: true,\n        enable_mousewheel_handle: true,\n    },\n    shortcut: {\n        enable: true,\n        handles: {},\n        mapping: {\n            addchild: [45, 4096 + 13], // Insert, Ctrl+Enter\n            addbrother: 13, // Enter\n            editnode: 113, // F2\n            delnode: 46, // Delete\n            toggle: 32, // Space\n            left: 37, // Left\n            up: 38, // Up\n            right: 39, // Right\n            down: 40, // Down\n        },\n    },\n    plugin: {},\n};\n\nexport function merge_option(options) {\n    var opts = {};\n    util.json.merge(opts, default_options);\n    util.json.merge(opts, options);\n\n    if (!opts.container) {\n        throw new Error('the options.container should not be null or empty.');\n    }\n    return opts;\n}\n", "import { logger } from './jsmind.common.js';\nexport class Node {\n    constructor(sId, iIndex, sTopic, oData, bIsRoot, oParent, eDirection, bExpanded) {\n        if (!sId) {\n            logger.error('invalid node id');\n            return;\n        }\n        if (typeof iIndex != 'number') {\n            logger.error('invalid node index');\n            return;\n        }\n        if (typeof bExpanded === 'undefined') {\n            bExpanded = true;\n        }\n        this.id = sId;\n        this.index = iIndex;\n        this.topic = sTopic;\n        this.data = oData || {};\n        this.isroot = bIsRoot;\n        this.parent = oParent;\n        this.direction = eDirection;\n        this.expanded = !!bExpanded;\n        this.children = [];\n        this._data = {};\n    }\n\n    get_location() {\n        var vd = this._data.view;\n        return {\n            x: vd.abs_x,\n            y: vd.abs_y,\n        };\n    }\n    get_size() {\n        var vd = this._data.view;\n        return {\n            w: vd.width,\n            h: vd.height,\n        };\n    }\n\n    static compare(node1, node2) {\n        // '-1' is always the latest\n        var r = 0;\n        var i1 = node1.index;\n        var i2 = node2.index;\n        if (i1 >= 0 && i2 >= 0) {\n            r = i1 - i2;\n        } else if (i1 == -1 && i2 == -1) {\n            r = 0;\n        } else if (i1 == -1) {\n            r = 1;\n        } else if (i2 == -1) {\n            r = -1;\n        } else {\n            r = 0;\n        }\n        return r;\n    }\n    static inherited(parent_node, node) {\n        if (!!parent_node && !!node) {\n            if (parent_node.id === node.id) {\n                return true;\n            }\n            if (parent_node.isroot) {\n                return true;\n            }\n            var pid = parent_node.id;\n            var p = node;\n            while (!p.isroot) {\n                p = p.parent;\n                if (p.id === pid) {\n                    return true;\n                }\n            }\n        }\n        return false;\n    }\n    static is_node(n) {\n        return !!n && n instanceof Node;\n    }\n}\n", "/**\n * @license BSD\n * @copyright 2014-2023 <EMAIL>\n *\n * Project Home:\n *   https://github.com/hizzgdev/jsmind/\n */\n\nimport { Node } from './jsmind.node.js';\nimport { logger, Direction } from './jsmind.common.js';\n\nexport class Mind {\n    constructor() {\n        this.name = null;\n        this.author = null;\n        this.version = null;\n        this.root = null;\n        this.selected = null;\n        this.nodes = {};\n    }\n    get_node(node_id) {\n        if (node_id in this.nodes) {\n            return this.nodes[node_id];\n        } else {\n            logger.warn('the node[id=' + node_id + '] can not be found');\n            return null;\n        }\n    }\n    set_root(node_id, topic, data) {\n        if (this.root == null) {\n            this.root = new Node(node_id, 0, topic, data, true);\n            this._put_node(this.root);\n            return this.root;\n        } else {\n            logger.error('root node is already exist');\n            return null;\n        }\n    }\n    add_node(parent_node, node_id, topic, data, direction, expanded, idx) {\n        if (!Node.is_node(parent_node)) {\n            logger.error('the parent_node ' + parent_node + ' is not a node.');\n            return null;\n        }\n        var node_index = idx || -1;\n        var node = new Node(\n            node_id,\n            node_index,\n            topic,\n            data,\n            false,\n            parent_node,\n            parent_node.direction,\n            expanded\n        );\n        if (parent_node.isroot) {\n            node.direction = direction || Direction.right;\n        }\n        if (this._put_node(node)) {\n            parent_node.children.push(node);\n            this._update_index(parent_node);\n        } else {\n            logger.error(\"fail, the node id '\" + node.id + \"' has been already exist.\");\n            node = null;\n        }\n        return node;\n    }\n    insert_node_before(node_before, node_id, topic, data, direction) {\n        if (!Node.is_node(node_before)) {\n            logger.error('the node_before ' + node_before + ' is not a node.');\n            return null;\n        }\n        var node_index = node_before.index - 0.5;\n        return this.add_node(node_before.parent, node_id, topic, data, direction, true, node_index);\n    }\n    get_node_before(node) {\n        if (!Node.is_node(node)) {\n            var the_node = this.get_node(node);\n            if (!the_node) {\n                logger.error('the node[id=' + node + '] can not be found.');\n                return null;\n            } else {\n                return this.get_node_before(the_node);\n            }\n        }\n        if (node.isroot) {\n            return null;\n        }\n        var idx = node.index - 2;\n        if (idx >= 0) {\n            return node.parent.children[idx];\n        } else {\n            return null;\n        }\n    }\n    insert_node_after(node_after, node_id, topic, data, direction) {\n        if (!Node.is_node(node_after)) {\n            logger.error('the node_after ' + node_after + ' is not a node.');\n            return null;\n        }\n        var node_index = node_after.index + 0.5;\n        return this.add_node(node_after.parent, node_id, topic, data, direction, true, node_index);\n    }\n    get_node_after(node) {\n        if (!Node.is_node(node)) {\n            var the_node = this.get_node(node);\n            if (!the_node) {\n                logger.error('the node[id=' + node + '] can not be found.');\n                return null;\n            } else {\n                return this.get_node_after(the_node);\n            }\n        }\n        if (node.isroot) {\n            return null;\n        }\n        var idx = node.index;\n        var brothers = node.parent.children;\n        if (brothers.length > idx) {\n            return node.parent.children[idx];\n        } else {\n            return null;\n        }\n    }\n    move_node(node, before_id, parent_id, direction) {\n        if (!Node.is_node(node)) {\n            logger.error('the parameter node ' + node + ' is not a node.');\n            return null;\n        }\n        if (!parent_id) {\n            parent_id = node.parent.id;\n        }\n        return this._move_node(node, before_id, parent_id, direction);\n    }\n    _flow_node_direction(node, direction) {\n        if (typeof direction === 'undefined') {\n            direction = node.direction;\n        } else {\n            node.direction = direction;\n        }\n        var len = node.children.length;\n        while (len--) {\n            this._flow_node_direction(node.children[len], direction);\n        }\n    }\n    _move_node_internal(node, before_id) {\n        if (!!node && !!before_id) {\n            if (before_id == '_last_') {\n                node.index = -1;\n                this._update_index(node.parent);\n            } else if (before_id == '_first_') {\n                node.index = 0;\n                this._update_index(node.parent);\n            } else {\n                var node_before = !!before_id ? this.get_node(before_id) : null;\n                if (\n                    node_before != null &&\n                    node_before.parent != null &&\n                    node_before.parent.id == node.parent.id\n                ) {\n                    node.index = node_before.index - 0.5;\n                    this._update_index(node.parent);\n                }\n            }\n        }\n        return node;\n    }\n    _move_node(node, before_id, parent_id, direction) {\n        if (!!node && !!parent_id) {\n            var parent_node = this.get_node(parent_id);\n            if (Node.inherited(node, parent_node)) {\n                logger.error('can not move a node to its children');\n                return null;\n            }\n            if (node.parent.id != parent_id) {\n                // remove from parent's children\n                var sibling = node.parent.children;\n                var si = sibling.length;\n                while (si--) {\n                    if (sibling[si].id == node.id) {\n                        sibling.splice(si, 1);\n                        break;\n                    }\n                }\n                let origin_parent = node.parent;\n                node.parent = parent_node;\n                parent_node.children.push(node);\n                this._update_index(origin_parent);\n            }\n\n            if (node.parent.isroot) {\n                if (direction == Direction.left) {\n                    node.direction = direction;\n                } else {\n                    node.direction = Direction.right;\n                }\n            } else {\n                node.direction = node.parent.direction;\n            }\n            this._move_node_internal(node, before_id);\n            this._flow_node_direction(node);\n        }\n        return node;\n    }\n    remove_node(node) {\n        if (!Node.is_node(node)) {\n            logger.error('the parameter node ' + node + ' is not a node.');\n            return false;\n        }\n        if (node.isroot) {\n            logger.error('fail, can not remove root node');\n            return false;\n        }\n        if (this.selected != null && this.selected.id == node.id) {\n            this.selected = null;\n        }\n        // clean all subordinate nodes\n        var children = node.children;\n        var ci = children.length;\n        while (ci--) {\n            this.remove_node(children[ci]);\n        }\n        // clean all children\n        children.length = 0;\n        var node_parent = node.parent;\n        // remove from parent's children\n        var sibling = node_parent.children;\n        var si = sibling.length;\n        while (si--) {\n            if (sibling[si].id == node.id) {\n                sibling.splice(si, 1);\n                break;\n            }\n        }\n        // remove from global nodes\n        delete this.nodes[node.id];\n        // clean all properties\n        for (var k in node) {\n            delete node[k];\n        }\n        // remove it's self\n        node = null;\n        this._update_index(node_parent);\n        return true;\n    }\n    _put_node(node) {\n        if (node.id in this.nodes) {\n            logger.warn(\"the node_id '\" + node.id + \"' has been already exist.\");\n            return false;\n        } else {\n            this.nodes[node.id] = node;\n            return true;\n        }\n    }\n    _update_index(node) {\n        if (node instanceof Node) {\n            node.children.sort(Node.compare);\n            for (var i = 0; i < node.children.length; i++) {\n                node.children[i].index = i + 1;\n            }\n        }\n    }\n}\n", "import { __author__, __version__, logger, Direction } from './jsmind.common.js';\nimport { Mind } from './jsmind.mind.js';\nimport { Node } from './jsmind.node.js';\nimport { util } from './jsmind.util.js';\n\nconst DEFAULT_META = { name: 'jsMind', author: __author__, version: __version__ };\n\nexport const format = {\n    node_tree: {\n        example: {\n            meta: DEFAULT_META,\n            format: 'node_tree',\n            data: { id: 'root', topic: 'jsMind node_tree example' },\n        },\n        get_mind: function (source) {\n            var df = format.node_tree;\n            var mind = new Mind();\n            mind.name = source.meta.name;\n            mind.author = source.meta.author;\n            mind.version = source.meta.version;\n            df._parse(mind, source.data);\n            return mind;\n        },\n        get_data: function (mind) {\n            var df = format.node_tree;\n            var json = {};\n            json.meta = {\n                name: mind.name,\n                author: mind.author,\n                version: mind.version,\n            };\n            json.format = 'node_tree';\n            json.data = df._build_node(mind.root);\n            return json;\n        },\n\n        _parse: function (mind, node_root) {\n            var df = format.node_tree;\n            var data = df._extract_data(node_root);\n            mind.set_root(node_root.id, node_root.topic, data);\n            if ('children' in node_root) {\n                var children = node_root.children;\n                for (var i = 0; i < children.length; i++) {\n                    df._extract_subnode(mind, mind.root, children[i]);\n                }\n            }\n        },\n\n        _extract_data: function (node_json) {\n            var data = {};\n            for (var k in node_json) {\n                if (\n                    k == 'id' ||\n                    k == 'topic' ||\n                    k == 'children' ||\n                    k == 'direction' ||\n                    k == 'expanded'\n                ) {\n                    continue;\n                }\n                data[k] = node_json[k];\n            }\n            return data;\n        },\n\n        _extract_subnode: function (mind, node_parent, node_json) {\n            var df = format.node_tree;\n            var data = df._extract_data(node_json);\n            var d = null;\n            if (node_parent.isroot) {\n                d = node_json.direction == 'left' ? Direction.left : Direction.right;\n            }\n            var node = mind.add_node(\n                node_parent,\n                node_json.id,\n                node_json.topic,\n                data,\n                d,\n                node_json.expanded\n            );\n            if (!!node_json['children']) {\n                var children = node_json.children;\n                for (var i = 0; i < children.length; i++) {\n                    df._extract_subnode(mind, node, children[i]);\n                }\n            }\n        },\n\n        _build_node: function (node) {\n            var df = format.node_tree;\n            if (!(node instanceof Node)) {\n                return;\n            }\n            var o = {\n                id: node.id,\n                topic: node.topic,\n                expanded: node.expanded,\n            };\n            if (!!node.parent && node.parent.isroot) {\n                o.direction = node.direction == Direction.left ? 'left' : 'right';\n            }\n            if (node.data != null) {\n                var node_data = node.data;\n                for (var k in node_data) {\n                    o[k] = node_data[k];\n                }\n            }\n            var children = node.children;\n            if (children.length > 0) {\n                o.children = [];\n                for (var i = 0; i < children.length; i++) {\n                    o.children.push(df._build_node(children[i]));\n                }\n            }\n            return o;\n        },\n    },\n\n    node_array: {\n        example: {\n            meta: DEFAULT_META,\n            format: 'node_array',\n            data: [{ id: 'root', topic: 'jsMind node_array example', isroot: true }],\n        },\n\n        get_mind: function (source) {\n            var df = format.node_array;\n            var mind = new Mind();\n            mind.name = source.meta.name;\n            mind.author = source.meta.author;\n            mind.version = source.meta.version;\n            df._parse(mind, source.data);\n            return mind;\n        },\n\n        get_data: function (mind) {\n            var df = format.node_array;\n            var json = {};\n            json.meta = {\n                name: mind.name,\n                author: mind.author,\n                version: mind.version,\n            };\n            json.format = 'node_array';\n            json.data = [];\n            df._array(mind, json.data);\n            return json;\n        },\n\n        _parse: function (mind, node_array) {\n            var df = format.node_array;\n            var nodes = node_array.slice(0);\n            // reverse array for improving looping performance\n            nodes.reverse();\n            var root_node = df._extract_root(mind, nodes);\n            if (!!root_node) {\n                df._extract_subnode(mind, root_node, nodes);\n            } else {\n                logger.error('root node can not be found');\n            }\n        },\n\n        _extract_root: function (mind, node_array) {\n            var df = format.node_array;\n            var i = node_array.length;\n            while (i--) {\n                if ('isroot' in node_array[i] && node_array[i].isroot) {\n                    var root_json = node_array[i];\n                    var data = df._extract_data(root_json);\n                    var node = mind.set_root(root_json.id, root_json.topic, data);\n                    node_array.splice(i, 1);\n                    return node;\n                }\n            }\n            return null;\n        },\n\n        _extract_subnode: function (mind, parent_node, node_array) {\n            var df = format.node_array;\n            var i = node_array.length;\n            var node_json = null;\n            var data = null;\n            var extract_count = 0;\n            while (i--) {\n                node_json = node_array[i];\n                if (node_json.parentid == parent_node.id) {\n                    data = df._extract_data(node_json);\n                    var d = null;\n                    var node_direction = node_json.direction;\n                    if (!!node_direction) {\n                        d = node_direction == 'left' ? Direction.left : Direction.right;\n                    }\n                    var node = mind.add_node(\n                        parent_node,\n                        node_json.id,\n                        node_json.topic,\n                        data,\n                        d,\n                        node_json.expanded\n                    );\n                    node_array.splice(i, 1);\n                    extract_count++;\n                    var sub_extract_count = df._extract_subnode(mind, node, node_array);\n                    if (sub_extract_count > 0) {\n                        // reset loop index after extract subordinate node\n                        i = node_array.length;\n                        extract_count += sub_extract_count;\n                    }\n                }\n            }\n            return extract_count;\n        },\n\n        _extract_data: function (node_json) {\n            var data = {};\n            for (var k in node_json) {\n                if (\n                    k == 'id' ||\n                    k == 'topic' ||\n                    k == 'parentid' ||\n                    k == 'isroot' ||\n                    k == 'direction' ||\n                    k == 'expanded'\n                ) {\n                    continue;\n                }\n                data[k] = node_json[k];\n            }\n            return data;\n        },\n\n        _array: function (mind, node_array) {\n            var df = format.node_array;\n            df._array_node(mind.root, node_array);\n        },\n\n        _array_node: function (node, node_array) {\n            var df = format.node_array;\n            if (!(node instanceof Node)) {\n                return;\n            }\n            var o = {\n                id: node.id,\n                topic: node.topic,\n                expanded: node.expanded,\n            };\n            if (!!node.parent) {\n                o.parentid = node.parent.id;\n            }\n            if (node.isroot) {\n                o.isroot = true;\n            }\n            if (!!node.parent && node.parent.isroot) {\n                o.direction = node.direction == Direction.left ? 'left' : 'right';\n            }\n            if (node.data != null) {\n                var node_data = node.data;\n                for (var k in node_data) {\n                    o[k] = node_data[k];\n                }\n            }\n            node_array.push(o);\n            var ci = node.children.length;\n            for (var i = 0; i < ci; i++) {\n                df._array_node(node.children[i], node_array);\n            }\n        },\n    },\n\n    freemind: {\n        example: {\n            meta: DEFAULT_META,\n            format: 'freemind',\n            data: '<map version=\"1.0.1\"><node ID=\"root\" TEXT=\"jsMind freemind example\"/></map>',\n        },\n        get_mind: function (source) {\n            var df = format.freemind;\n            var mind = new Mind();\n            mind.name = source.meta.name;\n            mind.author = source.meta.author;\n            mind.version = source.meta.version;\n            var xml = source.data;\n            var xml_doc = df._parse_xml(xml);\n            var xml_root = df._find_root(xml_doc);\n            df._load_node(mind, null, xml_root);\n            return mind;\n        },\n\n        get_data: function (mind) {\n            var df = format.freemind;\n            var json = {};\n            json.meta = {\n                name: mind.name,\n                author: mind.author,\n                version: mind.version,\n            };\n            json.format = 'freemind';\n            var xml_lines = [];\n            xml_lines.push('<map version=\"1.0.1\">');\n            df._build_map(mind.root, xml_lines);\n            xml_lines.push('</map>');\n            json.data = xml_lines.join('');\n            return json;\n        },\n\n        _parse_xml: function (xml) {\n            var xml_doc = null;\n            if (window.DOMParser) {\n                var parser = new DOMParser();\n                xml_doc = parser.parseFromString(xml, 'text/xml');\n            } else {\n                // Internet Explorer\n                xml_doc = new ActiveXObject('Microsoft.XMLDOM');\n                xml_doc.async = false;\n                xml_doc.loadXML(xml);\n            }\n            return xml_doc;\n        },\n\n        _find_root: function (xml_doc) {\n            var nodes = xml_doc.childNodes;\n            var node = null;\n            var root = null;\n            var n = null;\n            for (var i = 0; i < nodes.length; i++) {\n                n = nodes[i];\n                if (n.nodeType == 1 && n.tagName == 'map') {\n                    node = n;\n                    break;\n                }\n            }\n            if (!!node) {\n                var ns = node.childNodes;\n                node = null;\n                for (var i = 0; i < ns.length; i++) {\n                    n = ns[i];\n                    if (n.nodeType == 1 && n.tagName == 'node') {\n                        node = n;\n                        break;\n                    }\n                }\n            }\n            return node;\n        },\n\n        _load_node: function (mind, parent_node, xml_node) {\n            var df = format.freemind;\n            var node_id = xml_node.getAttribute('ID');\n            var node_topic = xml_node.getAttribute('TEXT');\n            var node_folded = xml_node.getAttribute('FOLDED');\n            // look for richcontent\n            if (node_topic == null) {\n                var topic_children = xml_node.childNodes;\n                var topic_child = null;\n                for (var i = 0; i < topic_children.length; i++) {\n                    topic_child = topic_children[i];\n                    if (topic_child.nodeType == 1 && topic_child.tagName === 'richcontent') {\n                        node_topic = topic_child.textContent;\n                        break;\n                    }\n                }\n            }\n            var node_data = df._load_attributes(xml_node);\n            var node_expanded =\n                'expanded' in node_data ? node_data.expanded == 'true' : node_folded != 'true';\n            delete node_data.expanded;\n\n            var node_position = xml_node.getAttribute('POSITION');\n            var node_direction = null;\n            if (!!node_position) {\n                node_direction = node_position == 'left' ? Direction.left : Direction.right;\n            }\n            var node = null;\n            if (!!parent_node) {\n                node = mind.add_node(\n                    parent_node,\n                    node_id,\n                    node_topic,\n                    node_data,\n                    node_direction,\n                    node_expanded\n                );\n            } else {\n                node = mind.set_root(node_id, node_topic, node_data);\n            }\n            var children = xml_node.childNodes;\n            var child = null;\n            for (var i = 0; i < children.length; i++) {\n                child = children[i];\n                if (child.nodeType == 1 && child.tagName == 'node') {\n                    df._load_node(mind, node, child);\n                }\n            }\n        },\n\n        _load_attributes: function (xml_node) {\n            var children = xml_node.childNodes;\n            var attr = null;\n            var attr_data = {};\n            for (var i = 0; i < children.length; i++) {\n                attr = children[i];\n                if (attr.nodeType == 1 && attr.tagName === 'attribute') {\n                    attr_data[attr.getAttribute('NAME')] = attr.getAttribute('VALUE');\n                }\n            }\n            return attr_data;\n        },\n\n        _build_map: function (node, xml_lines) {\n            var df = format.freemind;\n            var pos = null;\n            if (!!node.parent && node.parent.isroot) {\n                pos = node.direction === Direction.left ? 'left' : 'right';\n            }\n            xml_lines.push('<node');\n            xml_lines.push(' ID=\"' + node.id + '\"');\n            if (!!pos) {\n                xml_lines.push(' POSITION=\"' + pos + '\"');\n            }\n            if (!node.expanded) {\n                xml_lines.push(' FOLDED=\"true\"');\n            }\n            xml_lines.push(' TEXT=\"' + df._escape(node.topic) + '\">');\n\n            // for attributes\n            var node_data = node.data;\n            if (node_data != null) {\n                for (var k in node_data) {\n                    xml_lines.push('<attribute NAME=\"' + k + '\" VALUE=\"' + node_data[k] + '\"/>');\n                }\n            }\n\n            // for children\n            var children = node.children;\n            for (var i = 0; i < children.length; i++) {\n                df._build_map(children[i], xml_lines);\n            }\n\n            xml_lines.push('</node>');\n        },\n\n        _escape: function (text) {\n            return text\n                .replace(/&/g, '&amp;')\n                .replace(/</g, '&lt;')\n                .replace(/>/g, '&gt;')\n                .replace(/'/g, '&apos;')\n                .replace(/\"/g, '&quot;');\n        },\n    },\n    text: {\n        example: {\n            meta: DEFAULT_META,\n            format: 'text',\n            data: 'jsMind text example\\n node1\\n  node1-sub\\n  node1-sub\\n node2',\n        },\n        _line_regex: /\\s*/,\n        get_mind: function (source) {\n            var df = format.text;\n            var mind = new Mind();\n            mind.name = source.meta.name;\n            mind.author = source.meta.author;\n            mind.version = source.meta.version;\n            var lines = source.data.split(/\\n|\\r/);\n            df._fill_nodes(mind, lines, 0, 0);\n            return mind;\n        },\n\n        _fill_nodes: function (mind, lines) {\n            let node_path = [];\n            let i = 0;\n            while (i < lines.length) {\n                let line = lines[i];\n                let level = line.match(/\\s*/)[0].length;\n                let topic = line.substr(level);\n\n                if (level == 0 && node_path.length > 0) {\n                    log.error('more than 1 root node was found: ' + topic);\n                    return;\n                }\n                if (level > node_path.length) {\n                    log.error('a suspended node was found: ' + topic);\n                    return;\n                }\n                let diff = node_path.length - level;\n                while (diff--) {\n                    node_path.pop();\n                }\n\n                if (level == 0 && node_path.length == 0) {\n                    let node = mind.set_root(util.uuid.newid(), topic);\n                    node_path.push(node);\n                } else {\n                    let node = mind.add_node(\n                        node_path[level - 1],\n                        util.uuid.newid(),\n                        topic,\n                        {},\n                        null\n                    );\n                    node_path.push(node);\n                }\n                i++;\n            }\n            node_path.length = 0;\n        },\n\n        get_data: function (mind) {\n            var df = format.text;\n            var json = {};\n            json.meta = {\n                name: mind.name,\n                author: mind.author,\n                version: mind.version,\n            };\n            json.format = 'text';\n            let lines = [];\n            df._build_lines(lines, [mind.root], 0);\n            json.data = lines.join('\\n');\n            return json;\n        },\n\n        _build_lines: function (lines, nodes, level) {\n            let prefix = new Array(level + 1).join(' ');\n            for (let node of nodes) {\n                lines.push(prefix + node.topic);\n                if (!!node.children) {\n                    format.text._build_lines(lines, node.children, level + 1);\n                }\n            }\n        },\n    },\n};\n", "import { logger } from './jsmind.common.js';\nimport { format } from './jsmind.format.js';\n\nexport class DataProvider {\n    constructor(jm) {\n        this.jm = jm;\n    }\n\n    init() {\n        logger.debug('data.init');\n    }\n    reset() {\n        logger.debug('data.reset');\n    }\n    load(mind_data) {\n        var df = null;\n        var mind = null;\n        if (typeof mind_data === 'object') {\n            if (!!mind_data.format) {\n                df = mind_data.format;\n            } else {\n                df = 'node_tree';\n            }\n        } else {\n            df = 'freemind';\n        }\n\n        if (df == 'node_array') {\n            mind = format.node_array.get_mind(mind_data);\n        } else if (df == 'node_tree') {\n            mind = format.node_tree.get_mind(mind_data);\n        } else if (df == 'freemind') {\n            mind = format.freemind.get_mind(mind_data);\n        } else if (df == 'text') {\n            mind = format.text.get_mind(mind_data);\n        } else {\n            logger.warn('unsupported format');\n        }\n        return mind;\n    }\n    get_data(data_format) {\n        var data = null;\n        if (data_format == 'node_array') {\n            data = format.node_array.get_data(this.jm.mind);\n        } else if (data_format == 'node_tree') {\n            data = format.node_tree.get_data(this.jm.mind);\n        } else if (data_format == 'freemind') {\n            data = format.freemind.get_data(this.jm.mind);\n        } else if (data_format == 'text') {\n            data = format.text.get_data(this.jm.mind);\n        } else {\n            logger.error('unsupported ' + data_format + ' format');\n        }\n        return data;\n    }\n}\n", "/**\n * @license BSD\n * @copyright 2014-2023 <EMAIL>\n *\n * Project Home:\n *   https://github.com/hizzgdev/jsmind/\n */\nimport { logger, Direction, EventType } from './jsmind.common.js';\n\nexport class LayoutProvider {\n    constructor(jm, options) {\n        this.opts = options;\n        this.jm = jm;\n        this.isside = this.opts.mode == 'side';\n        this.bounds = null;\n\n        this.cache_valid = false;\n    }\n    init() {\n        logger.debug('layout.init');\n    }\n    reset() {\n        logger.debug('layout.reset');\n        this.bounds = { n: 0, s: 0, w: 0, e: 0 };\n    }\n    calculate_next_child_direction(node) {\n        if (this.isside) {\n            return Direction.right;\n        }\n        var children = node.children || [];\n        var children_len = children.length;\n        var r = 0;\n        for (var i = 0; i < children_len; i++) {\n            if (children[i].direction === Direction.left) {\n                r--;\n            } else {\n                r++;\n            }\n        }\n        return children_len > 1 && r > 0 ? Direction.left : Direction.right;\n    }\n    layout() {\n        logger.debug('layout.layout');\n        this.layout_direction();\n        this.layout_offset();\n    }\n    layout_direction() {\n        this._layout_direction_root();\n    }\n    _layout_direction_root() {\n        var node = this.jm.mind.root;\n        var layout_data = null;\n        if ('layout' in node._data) {\n            layout_data = node._data.layout;\n        } else {\n            layout_data = {};\n            node._data.layout = layout_data;\n        }\n        var children = node.children;\n        var children_count = children.length;\n        layout_data.direction = Direction.center;\n        layout_data.side_index = 0;\n        if (this.isside) {\n            var i = children_count;\n            while (i--) {\n                this._layout_direction_side(children[i], Direction.right, i);\n            }\n        } else {\n            var i = children_count;\n            var subnode = null;\n            while (i--) {\n                subnode = children[i];\n                if (subnode.direction == Direction.left) {\n                    this._layout_direction_side(subnode, Direction.left, i);\n                } else {\n                    this._layout_direction_side(subnode, Direction.right, i);\n                }\n            }\n        }\n    }\n    _layout_direction_side(node, direction, side_index) {\n        var layout_data = null;\n        if ('layout' in node._data) {\n            layout_data = node._data.layout;\n        } else {\n            layout_data = {};\n            node._data.layout = layout_data;\n        }\n        var children = node.children;\n        var children_count = children.length;\n\n        layout_data.direction = direction;\n        layout_data.side_index = side_index;\n        var i = children_count;\n        while (i--) {\n            this._layout_direction_side(children[i], direction, i);\n        }\n    }\n    layout_offset() {\n        var node = this.jm.mind.root;\n        var layout_data = node._data.layout;\n        layout_data.offset_x = 0;\n        layout_data.offset_y = 0;\n        layout_data.outer_height = 0;\n        var children = node.children;\n        var i = children.length;\n        var left_nodes = [];\n        var right_nodes = [];\n        var subnode = null;\n        while (i--) {\n            subnode = children[i];\n            if (subnode._data.layout.direction == Direction.right) {\n                right_nodes.unshift(subnode);\n            } else {\n                left_nodes.unshift(subnode);\n            }\n        }\n        layout_data.left_nodes = left_nodes;\n        layout_data.right_nodes = right_nodes;\n        layout_data.outer_height_left = this._layout_offset_subnodes(left_nodes);\n        layout_data.outer_height_right = this._layout_offset_subnodes(right_nodes);\n        this.bounds.e = node._data.view.width / 2;\n        this.bounds.w = 0 - this.bounds.e;\n        this.bounds.n = 0;\n        this.bounds.s = Math.max(layout_data.outer_height_left, layout_data.outer_height_right);\n    }\n    // layout both the x and y axis\n    _layout_offset_subnodes(nodes) {\n        var total_height = 0;\n        var nodes_count = nodes.length;\n        var i = nodes_count;\n        var node = null;\n        var node_outer_height = 0;\n        var layout_data = null;\n        var base_y = 0;\n        var pd = null; // parent._data\n        while (i--) {\n            node = nodes[i];\n            layout_data = node._data.layout;\n            if (pd == null) {\n                pd = node.parent._data;\n            }\n\n            node_outer_height = this._layout_offset_subnodes(node.children);\n            if (!node.expanded) {\n                node_outer_height = 0;\n                this.set_visible(node.children, false);\n            }\n            node_outer_height = Math.max(node._data.view.height, node_outer_height);\n            if (node.children.length > 1) {\n                node_outer_height += this.opts.cousin_space;\n            }\n\n            layout_data.outer_height = node_outer_height;\n            layout_data.offset_y = base_y - node_outer_height / 2;\n            layout_data.offset_x =\n                this.opts.hspace * layout_data.direction +\n                (pd.view.width * (pd.layout.direction + layout_data.direction)) / 2;\n            if (!node.parent.isroot) {\n                layout_data.offset_x += this.opts.pspace * layout_data.direction;\n            }\n\n            base_y = base_y - node_outer_height - this.opts.vspace;\n            total_height += node_outer_height;\n        }\n        if (nodes_count > 1) {\n            total_height += this.opts.vspace * (nodes_count - 1);\n        }\n        i = nodes_count;\n        var middle_height = total_height / 2;\n        while (i--) {\n            node = nodes[i];\n            node._data.layout.offset_y += middle_height;\n        }\n        return total_height;\n    }\n    // layout the y axis only, for collapse/expand a node\n    _layout_offset_subnodes_height(nodes) {\n        var total_height = 0;\n        var nodes_count = nodes.length;\n        var i = nodes_count;\n        var node = null;\n        var node_outer_height = 0;\n        var layout_data = null;\n        var base_y = 0;\n        var pd = null; // parent._data\n        while (i--) {\n            node = nodes[i];\n            layout_data = node._data.layout;\n            if (pd == null) {\n                pd = node.parent._data;\n            }\n\n            node_outer_height = this._layout_offset_subnodes_height(node.children);\n            if (!node.expanded) {\n                node_outer_height = 0;\n            }\n            node_outer_height = Math.max(node._data.view.height, node_outer_height);\n            if (node.children.length > 1) {\n                node_outer_height += this.opts.cousin_space;\n            }\n\n            layout_data.outer_height = node_outer_height;\n            layout_data.offset_y = base_y - node_outer_height / 2;\n            base_y = base_y - node_outer_height - this.opts.vspace;\n            total_height += node_outer_height;\n        }\n        if (nodes_count > 1) {\n            total_height += this.opts.vspace * (nodes_count - 1);\n        }\n        i = nodes_count;\n        var middle_height = total_height / 2;\n        while (i--) {\n            node = nodes[i];\n            node._data.layout.offset_y += middle_height;\n        }\n        return total_height;\n    }\n    get_node_offset(node) {\n        var layout_data = node._data.layout;\n        var offset_cache = null;\n        if ('_offset_' in layout_data && this.cache_valid) {\n            offset_cache = layout_data._offset_;\n        } else {\n            offset_cache = { x: -1, y: -1 };\n            layout_data._offset_ = offset_cache;\n        }\n        if (offset_cache.x == -1 || offset_cache.y == -1) {\n            var x = layout_data.offset_x;\n            var y = layout_data.offset_y;\n            if (!node.isroot) {\n                var offset_p = this.get_node_offset(node.parent);\n                x += offset_p.x;\n                y += offset_p.y;\n            }\n            offset_cache.x = x;\n            offset_cache.y = y;\n        }\n        return offset_cache;\n    }\n    get_node_point(node) {\n        var view_data = node._data.view;\n        var offset_p = this.get_node_offset(node);\n        var p = {};\n        p.x = offset_p.x + (view_data.width * (node._data.layout.direction - 1)) / 2;\n        p.y = offset_p.y - view_data.height / 2;\n        return p;\n    }\n    get_node_point_in(node) {\n        var p = this.get_node_offset(node);\n        return p;\n    }\n    get_node_point_out(node) {\n        var layout_data = node._data.layout;\n        var pout_cache = null;\n        if ('_pout_' in layout_data && this.cache_valid) {\n            pout_cache = layout_data._pout_;\n        } else {\n            pout_cache = { x: -1, y: -1 };\n            layout_data._pout_ = pout_cache;\n        }\n        if (pout_cache.x == -1 || pout_cache.y == -1) {\n            if (node.isroot) {\n                pout_cache.x = 0;\n                pout_cache.y = 0;\n            } else {\n                var view_data = node._data.view;\n                var offset_p = this.get_node_offset(node);\n                pout_cache.x =\n                    offset_p.x + (view_data.width + this.opts.pspace) * node._data.layout.direction;\n                pout_cache.y = offset_p.y;\n            }\n        }\n        return pout_cache;\n    }\n    get_expander_point(node) {\n        var p = this.get_node_point_out(node);\n        var ex_p = {};\n        if (node._data.layout.direction == Direction.right) {\n            ex_p.x = p.x - this.opts.pspace;\n        } else {\n            ex_p.x = p.x;\n        }\n        ex_p.y = p.y - Math.ceil(this.opts.pspace / 2);\n        return ex_p;\n    }\n    get_min_size() {\n        var nodes = this.jm.mind.nodes;\n        var node = null;\n        var pout = null;\n        for (var node_id in nodes) {\n            node = nodes[node_id];\n            pout = this.get_node_point_out(node);\n            if (pout.x > this.bounds.e) {\n                this.bounds.e = pout.x;\n            }\n            if (pout.x < this.bounds.w) {\n                this.bounds.w = pout.x;\n            }\n        }\n        return {\n            w: this.bounds.e - this.bounds.w,\n            h: this.bounds.s - this.bounds.n,\n        };\n    }\n    toggle_node(node) {\n        if (node.isroot) {\n            return;\n        }\n        if (node.expanded) {\n            this.collapse_node(node);\n        } else {\n            this.expand_node(node);\n        }\n    }\n    expand_node(node) {\n        node.expanded = true;\n        this.part_layout(node);\n        this.set_visible(node.children, true);\n        this.jm.invoke_event_handle(EventType.show, {\n            evt: 'expand_node',\n            data: [],\n            node: node.id,\n        });\n    }\n    collapse_node(node) {\n        node.expanded = false;\n        this.part_layout(node);\n        this.set_visible(node.children, false);\n        this.jm.invoke_event_handle(EventType.show, {\n            evt: 'collapse_node',\n            data: [],\n            node: node.id,\n        });\n    }\n    expand_all() {\n        var nodes = this.jm.mind.nodes;\n        var c = 0;\n        var node;\n        for (var node_id in nodes) {\n            node = nodes[node_id];\n            if (!node.expanded) {\n                node.expanded = true;\n                c++;\n            }\n        }\n        if (c > 0) {\n            var root = this.jm.mind.root;\n            this.part_layout(root);\n            this.set_visible(root.children, true);\n        }\n    }\n    collapse_all() {\n        var nodes = this.jm.mind.nodes;\n        var c = 0;\n        var node;\n        for (var node_id in nodes) {\n            node = nodes[node_id];\n            if (node.expanded && !node.isroot) {\n                node.expanded = false;\n                c++;\n            }\n        }\n        if (c > 0) {\n            var root = this.jm.mind.root;\n            this.part_layout(root);\n            this.set_visible(root.children, true);\n        }\n    }\n    expand_to_depth(target_depth, curr_nodes, curr_depth) {\n        if (target_depth < 1) {\n            return;\n        }\n        var nodes = curr_nodes || this.jm.mind.root.children;\n        var depth = curr_depth || 1;\n        var i = nodes.length;\n        var node = null;\n        while (i--) {\n            node = nodes[i];\n            if (depth < target_depth) {\n                if (!node.expanded) {\n                    this.expand_node(node);\n                }\n                this.expand_to_depth(target_depth, node.children, depth + 1);\n            }\n            if (depth == target_depth) {\n                if (node.expanded) {\n                    this.collapse_node(node);\n                }\n            }\n        }\n    }\n    part_layout(node) {\n        var root = this.jm.mind.root;\n        if (!!root) {\n            var root_layout_data = root._data.layout;\n            if (node.isroot) {\n                root_layout_data.outer_height_right = this._layout_offset_subnodes_height(\n                    root_layout_data.right_nodes\n                );\n                root_layout_data.outer_height_left = this._layout_offset_subnodes_height(\n                    root_layout_data.left_nodes\n                );\n            } else {\n                if (node._data.layout.direction == Direction.right) {\n                    root_layout_data.outer_height_right = this._layout_offset_subnodes_height(\n                        root_layout_data.right_nodes\n                    );\n                } else {\n                    root_layout_data.outer_height_left = this._layout_offset_subnodes_height(\n                        root_layout_data.left_nodes\n                    );\n                }\n            }\n            this.bounds.s = Math.max(\n                root_layout_data.outer_height_left,\n                root_layout_data.outer_height_right\n            );\n            this.cache_valid = false;\n        } else {\n            logger.warn('can not found root node');\n        }\n    }\n    set_visible(nodes, visible) {\n        var i = nodes.length;\n        var node = null;\n        var layout_data = null;\n        while (i--) {\n            node = nodes[i];\n            layout_data = node._data.layout;\n            if (node.expanded) {\n                this.set_visible(node.children, visible);\n            } else {\n                this.set_visible(node.children, false);\n            }\n            if (!node.isroot) {\n                node._data.layout.visible = visible;\n            }\n        }\n    }\n    is_expand(node) {\n        return node.expanded;\n    }\n    is_visible(node) {\n        var layout_data = node._data.layout;\n        if ('visible' in layout_data && !layout_data.visible) {\n            return false;\n        } else {\n            return true;\n        }\n    }\n}\n", "import { $ } from './jsmind.dom.js';\n\nclass SvgGraph {\n    constructor(view) {\n        this.view = view;\n        this.opts = view.opts;\n        this.e_svg = SvgGraph.c('svg');\n        this.e_svg.setAttribute('class', 'jsmind');\n        this.size = { w: 0, h: 0 };\n        this.lines = [];\n        this.line_drawing = {\n            straight: this._line_to,\n            curved: this._bezier_to,\n        };\n        this.drawing = this.line_drawing[this.opts.line_style] || this.line_drawing.curved;\n    }\n    static c(tag) {\n        return $.d.createElementNS('http://www.w3.org/2000/svg', tag);\n    }\n    element() {\n        return this.e_svg;\n    }\n    set_size(w, h) {\n        this.size.w = w;\n        this.size.h = h;\n        this.e_svg.setAttribute('width', w);\n        this.e_svg.setAttribute('height', h);\n    }\n    clear() {\n        var len = this.lines.length;\n        while (len--) {\n            this.e_svg.removeChild(this.lines[len]);\n        }\n        this.lines.length = 0;\n    }\n    draw_line(pout, pin, offset, color) {\n        var line = SvgGraph.c('path');\n        line.setAttribute('stroke', color || this.opts.line_color);\n        line.setAttribute('stroke-width', this.opts.line_width);\n        line.setAttribute('fill', 'transparent');\n        this.lines.push(line);\n        this.e_svg.appendChild(line);\n        this.drawing(\n            line,\n            pin.x + offset.x,\n            pin.y + offset.y,\n            pout.x + offset.x,\n            pout.y + offset.y\n        );\n    }\n\n    copy_to(dest_canvas_ctx, callback) {\n        var img = new Image();\n        img.onload = function () {\n            dest_canvas_ctx.drawImage(img, 0, 0);\n            !!callback && callback();\n        };\n        img.src =\n            'data:image/svg+xml;base64,' + btoa(new XMLSerializer().serializeToString(this.e_svg));\n    }\n    _bezier_to(path, x1, y1, x2, y2) {\n        path.setAttribute(\n            'd',\n            'M ' +\n                x1 +\n                ' ' +\n                y1 +\n                ' C ' +\n                (x1 + ((x2 - x1) * 2) / 3) +\n                ' ' +\n                y1 +\n                ', ' +\n                x1 +\n                ' ' +\n                y2 +\n                ', ' +\n                x2 +\n                ' ' +\n                y2\n        );\n    }\n    _line_to(path, x1, y1, x2, y2) {\n        path.setAttribute('d', 'M ' + x1 + ' ' + y1 + ' L ' + x2 + ' ' + y2);\n    }\n}\n\nclass CanvasGraph {\n    constructor(view) {\n        this.opts = view.opts;\n        this.e_canvas = $.c('canvas');\n        this.e_canvas.className = 'jsmind';\n        this.canvas_ctx = this.e_canvas.getContext('2d');\n        this.size = { w: 0, h: 0 };\n        this.line_drawing = {\n            straight: this._line_to,\n            curved: this._bezier_to,\n        };\n        this.drawing = this.line_drawing[this.opts.line_style] || this.line_drawing.curved;\n    }\n    element() {\n        return this.e_canvas;\n    }\n    set_size(w, h) {\n        this.size.w = w;\n        this.size.h = h;\n        this.e_canvas.width = w;\n        this.e_canvas.height = h;\n    }\n    clear() {\n        this.canvas_ctx.clearRect(0, 0, this.size.w, this.size.h);\n    }\n    draw_line(pout, pin, offset, color) {\n        var ctx = this.canvas_ctx;\n        ctx.strokeStyle = color || this.opts.line_color;\n        ctx.lineWidth = this.opts.line_width;\n        ctx.lineCap = 'round';\n        this.drawing(ctx, pin.x + offset.x, pin.y + offset.y, pout.x + offset.x, pout.y + offset.y);\n    }\n    copy_to(dest_canvas_ctx, callback) {\n        dest_canvas_ctx.drawImage(this.e_canvas, 0, 0);\n        !!callback && callback();\n    }\n    _bezier_to(ctx, x1, y1, x2, y2) {\n        ctx.beginPath();\n        ctx.moveTo(x1, y1);\n        ctx.bezierCurveTo(x1 + ((x2 - x1) * 2) / 3, y1, x1, y2, x2, y2);\n        ctx.stroke();\n    }\n    _line_to(ctx, x1, y1, x2, y2) {\n        ctx.beginPath();\n        ctx.moveTo(x1, y1);\n        ctx.lineTo(x2, y2);\n        ctx.stroke();\n    }\n}\n\nexport function init_graph(view, engine) {\n    return engine.toLowerCase() === 'svg' ? new SvgGraph(view) : new CanvasGraph(view);\n}\n", "import { logger, EventType } from './jsmind.common.js';\nimport { $ } from './jsmind.dom.js';\nimport { init_graph } from './jsmind.graph.js';\nimport { util } from './jsmind.util.js';\n\nexport class ViewProvider {\n    constructor(jm, options) {\n        this.opts = options;\n        this.jm = jm;\n        this.layout = jm.layout;\n\n        this.container = null;\n        this.e_panel = null;\n        this.e_nodes = null;\n\n        this.size = { w: 0, h: 0 };\n\n        this.selected_node = null;\n        this.editing_node = null;\n\n        this.graph = null;\n        this.render_node = !!options.custom_node_render\n            ? this._custom_node_render\n            : this._default_node_render;\n        this._initialized = false;\n    }\n    init() {\n        logger.debug(this.opts);\n        logger.debug('view.init');\n\n        this.container = $.i(this.opts.container) ? this.opts.container : $.g(this.opts.container);\n        if (!this.container) {\n            logger.error('the options.view.container was not be found in dom');\n            return;\n        }\n        this.graph = init_graph(this, this.opts.engine);\n\n        this.e_panel = $.c('div');\n        this.e_nodes = $.c('jmnodes');\n        this.e_editor = $.c('input');\n        this.e_panel.className = 'jsmind-inner jmnode-overflow-' + this.opts.node_overflow;\n        this.e_panel.tabIndex = 1;\n        this.e_panel.appendChild(this.graph.element());\n        this.e_panel.appendChild(this.e_nodes);\n\n        this.e_editor.className = 'jsmind-editor';\n        this.e_editor.type = 'text';\n\n        this.zoom_current = 1;\n\n        var v = this;\n        $.on(this.e_editor, 'keydown', function (e) {\n            var evt = e || event;\n            if (evt.keyCode == 13) {\n                v.edit_node_end();\n                evt.stopPropagation();\n            }\n        });\n        $.on(this.e_editor, 'blur', function (e) {\n            v.edit_node_end();\n        });\n\n        this.container.appendChild(this.e_panel);\n\n        if (!this.container.offsetParent) {\n            new IntersectionObserver((entities, observer) => {\n                if (entities[0].isIntersecting) {\n                    observer.unobserve(this.e_panel);\n                    this.resize();\n                }\n            }).observe(this.e_panel);\n        }\n    }\n\n    add_event(obj, event_name, event_handle, capture_by_panel) {\n        let target = !!capture_by_panel ? this.e_panel : this.e_nodes;\n        $.on(target, event_name, function (e) {\n            var evt = e || event;\n            event_handle.call(obj, evt);\n        });\n    }\n    get_binded_nodeid(element) {\n        if (element == null) {\n            return null;\n        }\n        var tagName = element.tagName.toLowerCase();\n        if (tagName == 'jmnode' || tagName == 'jmexpander') {\n            return element.getAttribute('nodeid');\n        } else if (tagName == 'jmnodes' || tagName == 'body' || tagName == 'html') {\n            return null;\n        } else {\n            return this.get_binded_nodeid(element.parentElement);\n        }\n    }\n    is_node(element) {\n        if (element == null) {\n            return false;\n        }\n        var tagName = element.tagName.toLowerCase();\n        if (tagName == 'jmnode') {\n            return true;\n        } else if (tagName == 'jmnodes' || tagName == 'body' || tagName == 'html') {\n            return false;\n        } else {\n            return this.is_node(element.parentElement);\n        }\n    }\n    is_expander(element) {\n        return element.tagName.toLowerCase() == 'jmexpander';\n    }\n    reset() {\n        logger.debug('view.reset');\n        this.selected_node = null;\n        this.clear_lines();\n        this.clear_nodes();\n        this.reset_theme();\n    }\n    reset_theme() {\n        var theme_name = this.jm.options.theme;\n        if (!!theme_name) {\n            this.e_nodes.className = 'theme-' + theme_name;\n        } else {\n            this.e_nodes.className = '';\n        }\n    }\n    reset_custom_style() {\n        var nodes = this.jm.mind.nodes;\n        for (var nodeid in nodes) {\n            this.reset_node_custom_style(nodes[nodeid]);\n        }\n    }\n    load() {\n        logger.debug('view.load');\n        this.setup_canvas_draggable(this.opts.draggable);\n        this.init_nodes();\n        this._initialized = true;\n    }\n    expand_size() {\n        var min_size = this.layout.get_min_size();\n        var min_width = min_size.w + this.opts.hmargin * 2;\n        var min_height = min_size.h + this.opts.vmargin * 2;\n        var client_w = this.e_panel.clientWidth;\n        var client_h = this.e_panel.clientHeight;\n        if (client_w < min_width) {\n            client_w = min_width;\n        }\n        if (client_h < min_height) {\n            client_h = min_height;\n        }\n        this.size.w = client_w;\n        this.size.h = client_h;\n    }\n    init_nodes_size(node) {\n        var view_data = node._data.view;\n        view_data.width = view_data.element.clientWidth;\n        view_data.height = view_data.element.clientHeight;\n    }\n    init_nodes() {\n        var nodes = this.jm.mind.nodes;\n        var doc_frag = $.d.createDocumentFragment();\n        for (var nodeid in nodes) {\n            this.create_node_element(nodes[nodeid], doc_frag);\n        }\n        this.e_nodes.appendChild(doc_frag);\n\n        this.run_in_c11y_mode_if_needed(() => {\n            for (var nodeid in nodes) {\n                this.init_nodes_size(nodes[nodeid]);\n            }\n        });\n    }\n    add_node(node) {\n        this.create_node_element(node, this.e_nodes);\n        this.run_in_c11y_mode_if_needed(() => {\n            this.init_nodes_size(node);\n        });\n    }\n    run_in_c11y_mode_if_needed(func) {\n        if (!!this.container.offsetParent) {\n            func();\n            return;\n        }\n        logger.warn(\n            'init nodes in compatibility mode. because the container or its parent has style {display:none}. '\n        );\n        this.e_panel.style.position = 'absolute';\n        this.e_panel.style.top = '-100000';\n        $.d.body.appendChild(this.e_panel);\n        func();\n        this.container.appendChild(this.e_panel);\n        this.e_panel.style.position = null;\n        this.e_panel.style.top = null;\n    }\n    create_node_element(node, parent_node) {\n        var view_data = null;\n        if ('view' in node._data) {\n            view_data = node._data.view;\n        } else {\n            view_data = {};\n            node._data.view = view_data;\n        }\n\n        var d = $.c('jmnode');\n        if (node.isroot) {\n            d.className = 'root';\n        } else {\n            var d_e = $.c('jmexpander');\n            $.t(d_e, '-');\n            d_e.setAttribute('nodeid', node.id);\n            d_e.style.visibility = 'hidden';\n            parent_node.appendChild(d_e);\n            view_data.expander = d_e;\n        }\n        if (!!node.topic) {\n            this.render_node(d, node);\n        }\n        d.setAttribute('nodeid', node.id);\n        d.style.visibility = 'hidden';\n        this._reset_node_custom_style(d, node.data);\n\n        parent_node.appendChild(d);\n        view_data.element = d;\n    }\n    remove_node(node) {\n        if (this.selected_node != null && this.selected_node.id == node.id) {\n            this.selected_node = null;\n        }\n        if (this.editing_node != null && this.editing_node.id == node.id) {\n            node._data.view.element.removeChild(this.e_editor);\n            this.editing_node = null;\n        }\n        var children = node.children;\n        var i = children.length;\n        while (i--) {\n            this.remove_node(children[i]);\n        }\n        if (node._data.view) {\n            var element = node._data.view.element;\n            var expander = node._data.view.expander;\n            this.e_nodes.removeChild(element);\n            this.e_nodes.removeChild(expander);\n            node._data.view.element = null;\n            node._data.view.expander = null;\n        }\n    }\n    update_node(node) {\n        var view_data = node._data.view;\n        var element = view_data.element;\n        if (!!node.topic) {\n            this.render_node(element, node);\n        }\n        if (this.layout.is_visible(node)) {\n            view_data.width = element.clientWidth;\n            view_data.height = element.clientHeight;\n        } else {\n            let origin_style = element.getAttribute('style');\n            element.style = 'visibility: visible; left:0; top:0;';\n            view_data.width = element.clientWidth;\n            view_data.height = element.clientHeight;\n            element.style = origin_style;\n        }\n    }\n    select_node(node) {\n        if (!!this.selected_node) {\n            var element = this.selected_node._data.view.element;\n            element.className = element.className.replace(/\\s*selected\\b/i, '');\n            this.restore_selected_node_custom_style(this.selected_node);\n        }\n        if (!!node) {\n            this.selected_node = node;\n            node._data.view.element.className += ' selected';\n            this.clear_selected_node_custom_style(node);\n        }\n    }\n    select_clear() {\n        this.select_node(null);\n    }\n    get_editing_node() {\n        return this.editing_node;\n    }\n    is_editing() {\n        return !!this.editing_node;\n    }\n    edit_node_begin(node) {\n        if (!node.topic) {\n            logger.warn(\"don't edit image nodes\");\n            return;\n        }\n        if (this.editing_node != null) {\n            this.edit_node_end();\n        }\n        this.editing_node = node;\n        var view_data = node._data.view;\n        var element = view_data.element;\n        var topic = node.topic;\n        var ncs = getComputedStyle(element);\n        this.e_editor.value = topic;\n        this.e_editor.style.width =\n            element.clientWidth -\n            parseInt(ncs.getPropertyValue('padding-left')) -\n            parseInt(ncs.getPropertyValue('padding-right')) +\n            'px';\n        element.innerHTML = '';\n        element.appendChild(this.e_editor);\n        element.style.zIndex = 5;\n        this.e_editor.focus();\n        this.e_editor.select();\n    }\n    edit_node_end() {\n        if (this.editing_node != null) {\n            var node = this.editing_node;\n            this.editing_node = null;\n            var view_data = node._data.view;\n            var element = view_data.element;\n            var topic = this.e_editor.value;\n            element.style.zIndex = 'auto';\n            element.removeChild(this.e_editor);\n            if (util.text.is_empty(topic) || node.topic === topic) {\n                this.render_node(element, node);\n            } else {\n                this.jm.update_node(node.id, topic);\n            }\n        }\n        this.e_panel.focus();\n    }\n    get_view_offset() {\n        var bounds = this.layout.bounds;\n        var rootNode = this.jm.get_root();\n        // 判断根节点是否隐藏\n        var isHide = rootNode._data.view.hideNode;\n        var _x = (this.size.w - bounds.e - bounds.w) / 2;\n        var _y = this.size.h / 2;\n        return { x: isHide ? _x - rootNode._data.view.width : _x, y: _y };\n    }\n    resize() {\n        this.graph.set_size(1, 1);\n        this.e_nodes.style.width = '1px';\n        this.e_nodes.style.height = '1px';\n\n        this.expand_size();\n        this._show();\n    }\n    hideRootNodeVisible(isHide) {\n        var rootNode = this.jm.get_root();\n        // 隐藏本身\n        rootNode._data.view.hideNode = isHide;\n        // 子节点左侧连线去掉\n        var childList = rootNode.children;\n        for (var i = 0; i < childList.length; i++) {\n            childList[i]._data.layout.hideLine = isHide;\n        }\n        // 重新渲染\n        this.show_nodes();\n        this.show_lines();\n    }\n    _show() {\n        this.graph.set_size(this.size.w, this.size.h);\n        this.e_nodes.style.width = this.size.w + 'px';\n        this.e_nodes.style.height = this.size.h + 'px';\n        this.show_nodes();\n        this.show_lines();\n        //this.layout.cache_valid = true;\n        this.jm.invoke_event_handle(EventType.resize, { data: [] });\n    }\n    zoom_in(e) {\n        return this.set_zoom(this.zoom_current + this.opts.zoom.step, e);\n    }\n    zoom_out(e) {\n        return this.set_zoom(this.zoom_current - this.opts.zoom.step, e);\n    }\n    set_zoom(zoom, e) {\n        if (zoom < this.opts.zoom.min || zoom > this.opts.zoom.max) {\n            return false;\n        }\n        let e_panel_rect = this.e_panel.getBoundingClientRect();\n        if (\n            zoom < 1 &&\n            zoom < this.zoom_current &&\n            this.size.w * zoom < e_panel_rect.width &&\n            this.size.h * zoom < e_panel_rect.height\n        ) {\n            return false;\n        }\n        let zoom_center = !!e\n            ? { x: e.x - e_panel_rect.x, y: e.y - e_panel_rect.y }\n            : { x: e_panel_rect.width / 2, y: e_panel_rect.height / 2 };\n        let panel_scroll_x =\n            ((this.e_panel.scrollLeft + zoom_center.x) * zoom) / this.zoom_current - zoom_center.x;\n        let panel_scroll_y =\n            ((this.e_panel.scrollTop + zoom_center.y) * zoom) / this.zoom_current - zoom_center.y;\n\n        this.zoom_current = zoom;\n        for (var i = 0; i < this.e_panel.children.length; i++) {\n            this.e_panel.children[i].style.zoom = zoom;\n        }\n        this._show();\n        this.e_panel.scrollLeft = panel_scroll_x;\n        this.e_panel.scrollTop = panel_scroll_y;\n        return true;\n    }\n    show(keep_center) {\n        logger.debug('view.show');\n        this.expand_size();\n        this._show();\n        if (!!keep_center) {\n            this.center_node(this.jm.mind.root);\n        }\n    }\n    relayout() {\n        this.expand_size();\n        this._show();\n    }\n    save_location(node) {\n        var vd = node._data.view;\n        vd._saved_location = {\n            x: parseInt(vd.element.style.left) - this.e_panel.scrollLeft,\n            y: parseInt(vd.element.style.top) - this.e_panel.scrollTop,\n        };\n    }\n    restore_location(node) {\n        var vd = node._data.view;\n        this.e_panel.scrollLeft = parseInt(vd.element.style.left) - vd._saved_location.x;\n        this.e_panel.scrollTop = parseInt(vd.element.style.top) - vd._saved_location.y;\n    }\n    clear_nodes() {\n        var mind = this.jm.mind;\n        if (mind == null) {\n            return;\n        }\n        var nodes = mind.nodes;\n        var node = null;\n        for (var nodeid in nodes) {\n            node = nodes[nodeid];\n            node._data.view.element = null;\n            node._data.view.expander = null;\n        }\n        this.e_nodes.innerHTML = '';\n    }\n    show_nodes() {\n        var nodes = this.jm.mind.nodes;\n        var node = null;\n        var node_element = null;\n        var p = null;\n        var view_data = null;\n        var view_offset = this.get_view_offset();\n        for (var nodeid in nodes) {\n            node = nodes[nodeid];\n            view_data = node._data.view;\n            node_element = view_data.element;\n            if (!this.layout.is_visible(node) || view_data.hideNode) {\n                node_element.style.display = 'none';\n                if (view_data.expander && view_data.expander.style) {\n                    view_data.expander.style.display = 'none';\n                }\n                continue;\n            }\n            this.reset_node_custom_style(node);\n            p = this.layout.get_node_point(node);\n            view_data.abs_x = view_offset.x + p.x;\n            view_data.abs_y = view_offset.y + p.y;\n            node_element.style.left = view_offset.x + p.x + 'px';\n            node_element.style.top = view_offset.y + p.y + 'px';\n            node_element.style.display = '';\n            node_element.style.visibility = 'visible';\n            this._show_expander(node, view_offset);\n        }\n    }\n    _show_expander(node, view_offset) {\n        if (node.isroot) {\n            return;\n        }\n\n        var expander = node._data.view.expander;\n        if (node.children.length == 0) {\n            expander.style.display = 'none';\n            expander.style.visibility = 'hidden';\n            return;\n        }\n\n        let expander_text = this._get_expander_text(node);\n        $.t(expander, expander_text);\n\n        let p_expander = this.layout.get_expander_point(node);\n        expander.style.left = view_offset.x + p_expander.x + 3 + 'px';\n        expander.style.top = view_offset.y + p_expander.y + 3 + 'px';\n        expander.style.display = '';\n        expander.style.visibility = 'visible';\n        expander.style.backgroundColor = node.data.expandColor;\n    }\n\n    _get_expander_text(node) {\n        let style = !!this.opts.expander_style ? this.opts.expander_style.toLowerCase() : 'char';\n        if (style === 'number') {\n            return node.children.length > 99 ? '...' : node.children.length;\n        }\n        if (style === 'char') {\n            return node.expanded ? '-' : '+';\n        }\n    }\n\n    _default_node_render(ele, node) {\n        if (this.opts.support_html) {\n            $.h(ele, node.topic);\n        } else {\n            $.t(ele, node.topic);\n        }\n    }\n    _custom_node_render(ele, node) {\n        let rendered = this.opts.custom_node_render(this.jm, ele, node);\n        if (!rendered) {\n            this._default_node_render(ele, node);\n        }\n    }\n    reset_node_custom_style(node) {\n        this._reset_node_custom_style(node._data.view.element, node.data);\n    }\n    _reset_node_custom_style(node_element, node_data) {\n        if ('background-color' in node_data) {\n            node_element.style.backgroundColor = node_data['background-color'];\n        }\n        if ('foreground-color' in node_data) {\n            node_element.style.color = node_data['foreground-color'];\n        }\n        if ('width' in node_data) {\n            node_element.style.width = node_data['width'] + 'px';\n        }\n        if ('height' in node_data) {\n            node_element.style.height = node_data['height'] + 'px';\n        }\n        if ('font-size' in node_data) {\n            node_element.style.fontSize = node_data['font-size'] + 'px';\n        }\n        if ('font-weight' in node_data) {\n            node_element.style.fontWeight = node_data['font-weight'];\n        }\n        if ('font-style' in node_data) {\n            node_element.style.fontStyle = node_data['font-style'];\n        }\n        if ('background-image' in node_data) {\n            var backgroundImage = node_data['background-image'];\n            if (backgroundImage.startsWith('data') && node_data['width'] && node_data['height']) {\n                var img = new Image();\n\n                img.onload = function () {\n                    var c = $.c('canvas');\n                    c.width = node_element.clientWidth;\n                    c.height = node_element.clientHeight;\n                    var img = this;\n                    if (c.getContext) {\n                        var ctx = c.getContext('2d');\n                        ctx.drawImage(\n                            img,\n                            2,\n                            2,\n                            node_element.clientWidth,\n                            node_element.clientHeight\n                        );\n                        var scaledImageData = c.toDataURL();\n                        node_element.style.backgroundImage = 'url(' + scaledImageData + ')';\n                    }\n                };\n                img.src = backgroundImage;\n            } else {\n                node_element.style.backgroundImage = 'url(' + backgroundImage + ')';\n            }\n            node_element.style.backgroundSize = '99%';\n\n            if ('background-rotation' in node_data) {\n                node_element.style.transform =\n                    'rotate(' + node_data['background-rotation'] + 'deg)';\n            }\n        }\n    }\n    restore_selected_node_custom_style(node) {\n        var node_element = node._data.view.element;\n        var node_data = node.data;\n        if ('background-color' in node_data) {\n            node_element.style.backgroundColor = node_data['background-color'];\n        }\n        if ('foreground-color' in node_data) {\n            node_element.style.color = node_data['foreground-color'];\n        }\n    }\n    clear_selected_node_custom_style(node) {\n        var node_element = node._data.view.element;\n        node_element.style.backgroundColor = '';\n        node_element.style.color = '';\n    }\n    clear_lines() {\n        this.graph.clear();\n    }\n    show_lines() {\n        this.clear_lines();\n        var nodes = this.jm.mind.nodes;\n        var node = null;\n        var pin = null;\n        var pout = null;\n        var color = null;\n        var _offset = this.get_view_offset();\n        for (var nodeid in nodes) {\n            node = nodes[nodeid];\n            if (!!node.isroot) {\n                continue;\n            }\n            if (!this.layout.is_visible(node)) {\n                continue;\n            }\n            if (node._data.layout.hideLine) {\n                continue;\n            }\n            pin = this.layout.get_node_point_in(node);\n            pout = this.layout.get_node_point_out(node.parent);\n            color = node.data['leading-line-color'];\n            this.graph.draw_line(pout, pin, _offset, color);\n        }\n    }\n    // Drag the whole mind map with your mouse, when it's larger that the container\n    setup_canvas_draggable(enabled) {\n        this.opts.draggable = enabled;\n        if (!this._initialized) {\n            let dragging = false;\n            let x, y;\n            if (this.opts.hide_scrollbars_when_draggable) {\n                // Avoid scrollbars when mind map is larger than the container (e_panel = id jsmind-inner)\n                this.e_panel.style = 'overflow: hidden';\n            }\n            // Move the whole mind map with mouse moves, while button is down.\n            $.on(this.container, 'mousedown', eventDown => {\n                if (this.opts.draggable) {\n                    dragging = true;\n                    // Record current mouse position.\n                    x = eventDown.clientX;\n                    y = eventDown.clientY;\n                }\n            });\n            // Stop moving mind map once mouse button is released.\n            $.on(this.container, 'mouseup', () => {\n                dragging = false;\n            });\n            // Follow current mouse position and move mind map accordingly.\n            $.on(this.container, 'mousemove', eventMove => {\n                if (this.opts.draggable) {\n                    if (dragging) {\n                        this.e_panel.scrollBy(x - eventMove.clientX, y - eventMove.clientY);\n                        // Record new current position.\n                        x = eventMove.clientX;\n                        y = eventMove.clientY;\n                    }\n                }\n            });\n        }\n    }\n    center_node(node) {\n        if (!this.layout.is_visible(node)) {\n            logger.warn('can not scroll to the node, because it is invisible');\n            return false;\n        }\n        let view_data = node._data.view;\n        let e_panel_rect = this.e_panel.getBoundingClientRect();\n        let node_center_point = {\n            x: view_data.abs_x + view_data.width / 2,\n            y: view_data.abs_y + view_data.height / 2,\n        };\n        this.e_panel.scrollTo(\n            node_center_point.x * this.zoom_current - e_panel_rect.width / 2,\n            node_center_point.y * this.zoom_current - e_panel_rect.height / 2\n        );\n        return true;\n    }\n\n    zoomIn(e) {\n        logger.warn('please use zoom_in instead');\n        return this.zoom_in(e);\n    }\n    zoomOut(e) {\n        logger.warn('please use zoom_out instead');\n        return this.zoom_out(e);\n    }\n    setZoom(zoom, e) {\n        logger.warn('please use set_zoom instead');\n        return this.set_zoom(zoom, e);\n    }\n}\n", "import { $ } from './jsmind.dom.js';\nimport { util } from './jsmind.util.js';\nimport { Direction } from './jsmind.common.js';\n\nexport class ShortcutProvider {\n    constructor(jm, options) {\n        this.jm = jm;\n        this.opts = options;\n        this.mapping = options.mapping;\n        this.handles = options.handles;\n        this._newid = null;\n        this._mapping = {};\n    }\n    init() {\n        $.on(this.jm.view.e_panel, 'keydown', this.handler.bind(this));\n\n        this.handles['addchild'] = this.handle_addchild;\n        this.handles['addbrother'] = this.handle_addbrother;\n        this.handles['editnode'] = this.handle_editnode;\n        this.handles['delnode'] = this.handle_delnode;\n        this.handles['toggle'] = this.handle_toggle;\n        this.handles['up'] = this.handle_up;\n        this.handles['down'] = this.handle_down;\n        this.handles['left'] = this.handle_left;\n        this.handles['right'] = this.handle_right;\n\n        for (var handle in this.mapping) {\n            if (!!this.mapping[handle] && handle in this.handles) {\n                let keys = this.mapping[handle];\n                if (!Array.isArray(keys)) {\n                    keys = [keys];\n                }\n                for (let key of keys) {\n                    this._mapping[key] = this.handles[handle];\n                }\n            }\n        }\n\n        if (typeof this.opts.id_generator === 'function') {\n            this._newid = this.opts.id_generator;\n        } else {\n            this._newid = util.uuid.newid;\n        }\n    }\n    enable_shortcut() {\n        this.opts.enable = true;\n    }\n    disable_shortcut() {\n        this.opts.enable = false;\n    }\n    handler(e) {\n        if (e.which == 9) {\n            e.preventDefault();\n        } //prevent tab to change focus in browser\n        if (this.jm.view.is_editing()) {\n            return;\n        }\n        var evt = e || event;\n        if (!this.opts.enable) {\n            return true;\n        }\n        var kc =\n            evt.keyCode +\n            (evt.metaKey << 13) +\n            (evt.ctrlKey << 12) +\n            (evt.altKey << 11) +\n            (evt.shiftKey << 10);\n        if (kc in this._mapping) {\n            this._mapping[kc].call(this, this.jm, e);\n        }\n    }\n    handle_addchild(_jm, e) {\n        var selected_node = _jm.get_selected_node();\n        if (!!selected_node) {\n            var node_id = this._newid();\n            var node = _jm.add_node(selected_node, node_id, 'New Node');\n            if (!!node) {\n                _jm.select_node(node_id);\n                _jm.begin_edit(node_id);\n            }\n        }\n    }\n    handle_addbrother(_jm, e) {\n        var selected_node = _jm.get_selected_node();\n        if (!!selected_node && !selected_node.isroot) {\n            var node_id = this._newid();\n            var node = _jm.insert_node_after(selected_node, node_id, 'New Node');\n            if (!!node) {\n                _jm.select_node(node_id);\n                _jm.begin_edit(node_id);\n            }\n        }\n    }\n    handle_editnode(_jm, e) {\n        var selected_node = _jm.get_selected_node();\n        if (!!selected_node) {\n            _jm.begin_edit(selected_node);\n        }\n    }\n    handle_delnode(_jm, e) {\n        var selected_node = _jm.get_selected_node();\n        if (!!selected_node && !selected_node.isroot) {\n            _jm.select_node(selected_node.parent);\n            _jm.remove_node(selected_node);\n        }\n    }\n    handle_toggle(_jm, e) {\n        var evt = e || event;\n        var selected_node = _jm.get_selected_node();\n        if (!!selected_node) {\n            _jm.toggle_node(selected_node.id);\n            evt.stopPropagation();\n            evt.preventDefault();\n        }\n    }\n    handle_up(_jm, e) {\n        var evt = e || event;\n        var selected_node = _jm.get_selected_node();\n        if (!!selected_node) {\n            var up_node = _jm.find_node_before(selected_node);\n            if (!up_node) {\n                var np = _jm.find_node_before(selected_node.parent);\n                if (!!np && np.children.length > 0) {\n                    up_node = np.children[np.children.length - 1];\n                }\n            }\n            if (!!up_node) {\n                _jm.select_node(up_node);\n            }\n            evt.stopPropagation();\n            evt.preventDefault();\n        }\n    }\n    handle_down(_jm, e) {\n        var evt = e || event;\n        var selected_node = _jm.get_selected_node();\n        if (!!selected_node) {\n            var down_node = _jm.find_node_after(selected_node);\n            if (!down_node) {\n                var np = _jm.find_node_after(selected_node.parent);\n                if (!!np && np.children.length > 0) {\n                    down_node = np.children[0];\n                }\n            }\n            if (!!down_node) {\n                _jm.select_node(down_node);\n            }\n            evt.stopPropagation();\n            evt.preventDefault();\n        }\n    }\n    handle_left(_jm, e) {\n        this._handle_direction(_jm, e, Direction.left);\n    }\n    handle_right(_jm, e) {\n        this._handle_direction(_jm, e, Direction.right);\n    }\n    _handle_direction(_jm, e, d) {\n        var evt = e || event;\n        var selected_node = _jm.get_selected_node();\n        var node = null;\n        if (!!selected_node) {\n            if (selected_node.isroot) {\n                var c = selected_node.children;\n                var children = [];\n                for (var i = 0; i < c.length; i++) {\n                    if (c[i].direction === d) {\n                        children.push(i);\n                    }\n                }\n                node = c[children[Math.floor((children.length - 1) / 2)]];\n            } else if (selected_node.direction === d) {\n                var children = selected_node.children;\n                var children_count = children.length;\n                if (children_count > 0) {\n                    node = children[Math.floor((children_count - 1) / 2)];\n                }\n            } else {\n                node = selected_node.parent;\n            }\n            if (!!node) {\n                _jm.select_node(node);\n            }\n            evt.stopPropagation();\n            evt.preventDefault();\n        }\n    }\n}\n", "import { $ } from './jsmind.dom.js';\n\nconst plugin_data = {\n    plugins: [],\n};\n\nexport function register(plugin) {\n    if (!(plugin instanceof Plugin)) {\n        throw new Error('can not register plugin, it is not an instance of Plugin');\n    }\n    // if (plugin_data.plugins.map(p => p.name).includes(plugin.name)) {\n    //     throw new Error('can not register plugin ' + plugin.name + ': plugin name already exist');\n    // }\n    plugin_data.plugins.push(plugin);\n}\n\nexport function apply(jm, options) {\n    $.w.setTimeout(function () {\n        _apply(jm, options);\n    }, 0);\n}\n\nfunction _apply(jm, options) {\n    plugin_data.plugins.forEach(p => p.fn_init(jm, options[p.name]));\n}\n\nexport class Plugin {\n    // function fn_init(jm, options){ }\n    constructor(name, fn_init) {\n        if (!name) {\n            throw new Error('plugin must has a name');\n        }\n        if (!fn_init || typeof fn_init !== 'function') {\n            throw new Error('plugin must has an init function');\n        }\n        this.name = name;\n        this.fn_init = fn_init;\n    }\n}\n", "import { __version__, logger, EventType, Direction, LogLevel } from './jsmind.common.js';\nimport { merge_option } from './jsmind.option.js';\nimport { Mind } from './jsmind.mind.js';\nimport { Node } from './jsmind.node.js';\nimport { DataProvider } from './jsmind.data_provider.js';\nimport { LayoutProvider } from './jsmind.layout_provider.js';\nimport { ViewProvider } from './jsmind.view_provider.js';\nimport { ShortcutProvider } from './jsmind.shortcut_provider.js';\nimport { Plugin, register as _register_plugin, apply as apply_plugins } from './jsmind.plugin.js';\nimport { format } from './jsmind.format.js';\nimport { $ } from './jsmind.dom.js';\nimport { util as _util } from './jsmind.util.js';\n\nif (!window.scrollTo) {\n    window.scrollTo = function (option) {\n        window.scrollLeft = option.left;\n        window.scrollTop = option.top;\n    };\n}\n\nif (!document.body.scrollTo) {\n    Element.prototype.scrollTo = function (option) {\n        this.scrollLeft = option.left;\n        this.scrollTop = option.top;\n    };\n}\n\nexport default class jsMind {\n    static mind = Mind;\n    static node = Node;\n    static direction = Direction;\n    static event_type = EventType;\n    static $ = $;\n    static plugin = Plugin;\n    static register_plugin = _register_plugin;\n    static util = _util;\n\n    constructor(options) {\n        jsMind.current = this;\n        this.options = merge_option(options);\n        logger.level(LogLevel[this.options.log_level]);\n        this.version = __version__;\n        this.initialized = false;\n        this.mind = null;\n        this.event_handles = [];\n        this.init();\n    }\n\n    init() {\n        if (!!this.initialized) {\n            return;\n        }\n        this.initialized = true;\n        var opts_layout = {\n            mode: this.options.mode,\n            hspace: this.options.layout.hspace,\n            vspace: this.options.layout.vspace,\n            pspace: this.options.layout.pspace,\n            cousin_space: this.options.layout.cousin_space,\n        };\n        var opts_view = {\n            container: this.options.container,\n            support_html: this.options.support_html,\n            engine: this.options.view.engine,\n            hmargin: this.options.view.hmargin,\n            vmargin: this.options.view.vmargin,\n            line_width: this.options.view.line_width,\n            line_color: this.options.view.line_color,\n            line_style: this.options.view.line_style,\n            draggable: this.options.view.draggable,\n            hide_scrollbars_when_draggable: this.options.view.hide_scrollbars_when_draggable,\n            node_overflow: this.options.view.node_overflow,\n            zoom: this.options.view.zoom,\n            custom_node_render: this.options.view.custom_node_render,\n            expander_style: this.options.view.expander_style,\n        };\n        // create instance of function provider\n        this.data = new DataProvider(this);\n        this.layout = new LayoutProvider(this, opts_layout);\n        this.view = new ViewProvider(this, opts_view);\n        this.shortcut = new ShortcutProvider(this, this.options.shortcut);\n\n        this.data.init();\n        this.layout.init();\n        this.view.init();\n        this.shortcut.init();\n\n        this._event_bind();\n\n        apply_plugins(this, this.options.plugin);\n    }\n    get_editable() {\n        return this.options.editable;\n    }\n    enable_edit() {\n        this.options.editable = true;\n    }\n    disable_edit() {\n        this.options.editable = false;\n    }\n    get_view_draggable() {\n        return this.options.view.draggable;\n    }\n    enable_view_draggable() {\n        this.options.view.draggable = true;\n        this.view.setup_canvas_draggable(true);\n    }\n    disable_view_draggable() {\n        this.options.view.draggable = false;\n        this.view.setup_canvas_draggable(false);\n    }\n    // options are 'mousedown', 'click', 'dblclick', 'mousewheel'\n    enable_event_handle(event_handle) {\n        this.options.default_event_handle['enable_' + event_handle + '_handle'] = true;\n    }\n    // options are 'mousedown', 'click', 'dblclick', 'mousewheel'\n    disable_event_handle(event_handle) {\n        this.options.default_event_handle['enable_' + event_handle + '_handle'] = false;\n    }\n    set_theme(theme) {\n        var theme_old = this.options.theme;\n        this.options.theme = !!theme ? theme : null;\n        if (theme_old != this.options.theme) {\n            this.view.reset_theme();\n            this.view.reset_custom_style();\n        }\n    }\n    _event_bind() {\n        this.view.add_event(this, 'mousedown', this.mousedown_handle);\n        this.view.add_event(this, 'click', this.click_handle);\n        this.view.add_event(this, 'dblclick', this.dblclick_handle);\n        this.view.add_event(this, 'mousewheel', this.mousewheel_handle, true);\n    }\n    mousedown_handle(e) {\n        if (!this.options.default_event_handle['enable_mousedown_handle']) {\n            return;\n        }\n        var element = e.target || event.srcElement;\n        var node_id = this.view.get_binded_nodeid(element);\n        if (!!node_id) {\n            if (this.view.is_node(element)) {\n                this.select_node(node_id);\n            }\n        } else {\n            this.select_clear();\n        }\n    }\n    click_handle(e) {\n        if (!this.options.default_event_handle['enable_click_handle']) {\n            return;\n        }\n        var element = e.target || event.srcElement;\n        var is_expander = this.view.is_expander(element);\n        if (is_expander) {\n            var node_id = this.view.get_binded_nodeid(element);\n            if (!!node_id) {\n                this.toggle_node(node_id);\n            }\n        }\n    }\n    dblclick_handle(e) {\n        if (!this.options.default_event_handle['enable_dblclick_handle']) {\n            return;\n        }\n        if (this.get_editable()) {\n            var element = e.target || event.srcElement;\n            var is_node = this.view.is_node(element);\n            if (is_node) {\n                var node_id = this.view.get_binded_nodeid(element);\n                if (!!node_id) {\n                    this.begin_edit(node_id);\n                }\n            }\n        }\n    }\n    // Use [Ctrl] + Mousewheel, to zoom in/out.\n    mousewheel_handle(e) {\n        // Test if mousewheel option is enabled and Ctrl key is pressed.\n        if (!this.options.default_event_handle['enable_mousewheel_handle'] || !e.ctrlKey) {\n            return;\n        }\n        var evt = e || event;\n        // Avoid default page scrolling behavior.\n        evt.preventDefault();\n\n        if (evt.deltaY < 0) {\n            this.view.zoom_in(evt); // wheel down\n        } else {\n            this.view.zoom_out(evt);\n        }\n    }\n    begin_edit(node) {\n        if (!Node.is_node(node)) {\n            var the_node = this.get_node(node);\n            if (!the_node) {\n                logger.error('the node[id=' + node + '] can not be found.');\n                return false;\n            } else {\n                return this.begin_edit(the_node);\n            }\n        }\n        if (this.get_editable()) {\n            this.view.edit_node_begin(node);\n        } else {\n            logger.error('fail, this mind map is not editable.');\n            return;\n        }\n    }\n    end_edit() {\n        this.view.edit_node_end();\n    }\n    toggle_node(node) {\n        if (!Node.is_node(node)) {\n            var the_node = this.get_node(node);\n            if (!the_node) {\n                logger.error('the node[id=' + node + '] can not be found.');\n                return;\n            } else {\n                return this.toggle_node(the_node);\n            }\n        }\n        if (node.isroot) {\n            return;\n        }\n        this.view.save_location(node);\n        this.layout.toggle_node(node);\n        this.view.relayout();\n        this.view.restore_location(node);\n    }\n    expand_node(node) {\n        if (!Node.is_node(node)) {\n            var the_node = this.get_node(node);\n            if (!the_node) {\n                logger.error('the node[id=' + node + '] can not be found.');\n                return;\n            } else {\n                return this.expand_node(the_node);\n            }\n        }\n        if (node.isroot) {\n            return;\n        }\n        this.view.save_location(node);\n        this.layout.expand_node(node);\n        this.view.relayout();\n        this.view.restore_location(node);\n    }\n    collapse_node(node) {\n        if (!Node.is_node(node)) {\n            var the_node = this.get_node(node);\n            if (!the_node) {\n                logger.error('the node[id=' + node + '] can not be found.');\n                return;\n            } else {\n                return this.collapse_node(the_node);\n            }\n        }\n        if (node.isroot) {\n            return;\n        }\n        this.view.save_location(node);\n        this.layout.collapse_node(node);\n        this.view.relayout();\n        this.view.restore_location(node);\n    }\n    expand_all() {\n        this.layout.expand_all();\n        this.view.relayout();\n    }\n    collapse_all() {\n        this.layout.collapse_all();\n        this.view.relayout();\n    }\n    expand_to_depth(depth) {\n        this.layout.expand_to_depth(depth);\n        this.view.relayout();\n    }\n    _reset() {\n        this.view.reset();\n        this.layout.reset();\n        this.data.reset();\n    }\n    _show(mind) {\n        var m = mind || format.node_array.example;\n\n        this.mind = this.data.load(m);\n        if (!this.mind) {\n            logger.error('data.load error');\n            return;\n        } else {\n            logger.debug('data.load ok');\n        }\n\n        this.view.load();\n        logger.debug('view.load ok');\n\n        this.layout.layout();\n        logger.debug('layout.layout ok');\n\n        this.view.show(true);\n        logger.debug('view.show ok');\n\n        this.invoke_event_handle(EventType.show, { data: [mind] });\n    }\n    show(mind) {\n        this._reset();\n        this._show(mind);\n    }\n    get_meta() {\n        return {\n            name: this.mind.name,\n            author: this.mind.author,\n            version: this.mind.version,\n        };\n    }\n    get_data(data_format) {\n        var df = data_format || 'node_tree';\n        return this.data.get_data(df);\n    }\n    get_root() {\n        return this.mind.root;\n    }\n    get_node(node) {\n        if (Node.is_node(node)) {\n            return node;\n        }\n        return this.mind.get_node(node);\n    }\n    add_node(parent_node, node_id, topic, data, direction) {\n        if (this.get_editable()) {\n            var the_parent_node = this.get_node(parent_node);\n            var dir = Direction.of(direction);\n            if (dir === undefined) {\n                dir = this.layout.calculate_next_child_direction(the_parent_node);\n            }\n            var node = this.mind.add_node(the_parent_node, node_id, topic, data, dir);\n            if (!!node) {\n                this.view.add_node(node);\n                this.layout.layout();\n                this.view.show(false);\n                this.view.reset_node_custom_style(node);\n                this.expand_node(the_parent_node);\n                this.invoke_event_handle(EventType.edit, {\n                    evt: 'add_node',\n                    data: [the_parent_node.id, node_id, topic, data, dir],\n                    node: node_id,\n                });\n            }\n            return node;\n        } else {\n            logger.error('fail, this mind map is not editable');\n            return null;\n        }\n    }\n    insert_node_before(node_before, node_id, topic, data, direction) {\n        if (this.get_editable()) {\n            var the_node_before = this.get_node(node_before);\n            var dir = Direction.of(direction);\n            if (dir === undefined) {\n                dir = this.layout.calculate_next_child_direction(the_node_before.parent);\n            }\n            var node = this.mind.insert_node_before(the_node_before, node_id, topic, data, dir);\n            if (!!node) {\n                this.view.add_node(node);\n                this.layout.layout();\n                this.view.show(false);\n                this.invoke_event_handle(EventType.edit, {\n                    evt: 'insert_node_before',\n                    data: [the_node_before.id, node_id, topic, data, dir],\n                    node: node_id,\n                });\n            }\n            return node;\n        } else {\n            logger.error('fail, this mind map is not editable');\n            return null;\n        }\n    }\n    insert_node_after(node_after, node_id, topic, data, direction) {\n        if (this.get_editable()) {\n            var the_node_after = this.get_node(node_after);\n            var dir = Direction.of(direction);\n            if (dir === undefined) {\n                dir = this.layout.calculate_next_child_direction(the_node_after.parent);\n            }\n            var node = this.mind.insert_node_after(the_node_after, node_id, topic, data, dir);\n            if (!!node) {\n                this.view.add_node(node);\n                this.layout.layout();\n                this.view.show(false);\n                this.invoke_event_handle(EventType.edit, {\n                    evt: 'insert_node_after',\n                    data: [the_node_after.id, node_id, topic, data, dir],\n                    node: node_id,\n                });\n            }\n            return node;\n        } else {\n            logger.error('fail, this mind map is not editable');\n            return null;\n        }\n    }\n    remove_node(node) {\n        if (!Node.is_node(node)) {\n            var the_node = this.get_node(node);\n            if (!the_node) {\n                logger.error('the node[id=' + node + '] can not be found.');\n                return false;\n            } else {\n                return this.remove_node(the_node);\n            }\n        }\n        if (this.get_editable()) {\n            if (node.isroot) {\n                logger.error('fail, can not remove root node');\n                return false;\n            }\n            var node_id = node.id;\n            var parent_id = node.parent.id;\n            var parent_node = this.get_node(parent_id);\n            this.view.save_location(parent_node);\n            this.view.remove_node(node);\n            this.mind.remove_node(node);\n            this.layout.layout();\n            this.view.show(false);\n            this.view.restore_location(parent_node);\n            this.invoke_event_handle(EventType.edit, {\n                evt: 'remove_node',\n                data: [node_id],\n                node: parent_id,\n            });\n            return true;\n        } else {\n            logger.error('fail, this mind map is not editable');\n            return false;\n        }\n    }\n    update_node(node_id, topic) {\n        if (this.get_editable()) {\n            if (_util.text.is_empty(topic)) {\n                logger.warn('fail, topic can not be empty');\n                return;\n            }\n            var node = this.get_node(node_id);\n            if (!!node) {\n                if (node.topic === topic) {\n                    logger.info('nothing changed');\n                    this.view.update_node(node);\n                    return;\n                }\n                node.topic = topic;\n                this.view.update_node(node);\n                this.layout.layout();\n                this.view.show(false);\n                this.invoke_event_handle(EventType.edit, {\n                    evt: 'update_node',\n                    data: [node_id, topic],\n                    node: node_id,\n                });\n            }\n        } else {\n            logger.error('fail, this mind map is not editable');\n            return;\n        }\n    }\n    move_node(node_id, before_id, parent_id, direction) {\n        if (this.get_editable()) {\n            var node = this.get_node(node_id);\n            var updated_node = this.mind.move_node(node, before_id, parent_id, direction);\n            if (!!updated_node) {\n                this.view.update_node(updated_node);\n                this.layout.layout();\n                this.view.show(false);\n                this.invoke_event_handle(EventType.edit, {\n                    evt: 'move_node',\n                    data: [node_id, before_id, parent_id, direction],\n                    node: node_id,\n                });\n            }\n        } else {\n            logger.error('fail, this mind map is not editable');\n            return;\n        }\n    }\n    select_node(node) {\n        if (!Node.is_node(node)) {\n            var the_node = this.get_node(node);\n            if (!the_node) {\n                logger.error('the node[id=' + node + '] can not be found.');\n                return;\n            } else {\n                return this.select_node(the_node);\n            }\n        }\n        if (!this.layout.is_visible(node)) {\n            return;\n        }\n        this.mind.selected = node;\n        this.view.select_node(node);\n        this.invoke_event_handle(EventType.select, { evt: 'select_node', data: [], node: node.id });\n    }\n    get_selected_node() {\n        if (!!this.mind) {\n            return this.mind.selected;\n        } else {\n            return null;\n        }\n    }\n    select_clear() {\n        if (!!this.mind) {\n            this.mind.selected = null;\n            this.view.select_clear();\n        }\n    }\n    is_node_visible(node) {\n        return this.layout.is_visible(node);\n    }\n    scroll_node_to_center(node) {\n        if (!Node.is_node(node)) {\n            var the_node = this.get_node(node);\n            if (!the_node) {\n                logger.error('the node[id=' + node + '] can not be found.');\n            } else {\n                this.scroll_node_to_center(the_node);\n            }\n            return;\n        }\n        this.view.center_node(node);\n    }\n    find_node_before(node) {\n        if (!Node.is_node(node)) {\n            var the_node = this.get_node(node);\n            if (!the_node) {\n                logger.error('the node[id=' + node + '] can not be found.');\n                return;\n            } else {\n                return this.find_node_before(the_node);\n            }\n        }\n        if (node.isroot) {\n            return null;\n        }\n        var n = null;\n        if (node.parent.isroot) {\n            var c = node.parent.children;\n            var prev = null;\n            var ni = null;\n            for (var i = 0; i < c.length; i++) {\n                ni = c[i];\n                if (node.direction === ni.direction) {\n                    if (node.id === ni.id) {\n                        n = prev;\n                    }\n                    prev = ni;\n                }\n            }\n        } else {\n            n = this.mind.get_node_before(node);\n        }\n        return n;\n    }\n    find_node_after(node) {\n        if (!Node.is_node(node)) {\n            var the_node = this.get_node(node);\n            if (!the_node) {\n                logger.error('the node[id=' + node + '] can not be found.');\n                return;\n            } else {\n                return this.find_node_after(the_node);\n            }\n        }\n        if (node.isroot) {\n            return null;\n        }\n        var n = null;\n        if (node.parent.isroot) {\n            var c = node.parent.children;\n            var found = false;\n            var ni = null;\n            for (var i = 0; i < c.length; i++) {\n                ni = c[i];\n                if (node.direction === ni.direction) {\n                    if (found) {\n                        n = ni;\n                        break;\n                    }\n                    if (node.id === ni.id) {\n                        found = true;\n                    }\n                }\n            }\n        } else {\n            n = this.mind.get_node_after(node);\n        }\n        return n;\n    }\n    set_node_color(node_id, bg_color, fg_color) {\n        if (this.get_editable()) {\n            var node = this.mind.get_node(node_id);\n            if (!!node) {\n                if (!!bg_color) {\n                    node.data['background-color'] = bg_color;\n                }\n                if (!!fg_color) {\n                    node.data['foreground-color'] = fg_color;\n                }\n                this.view.reset_node_custom_style(node);\n            }\n        } else {\n            logger.error('fail, this mind map is not editable');\n            return null;\n        }\n    }\n    set_node_font_style(node_id, size, weight, style) {\n        if (this.get_editable()) {\n            var node = this.mind.get_node(node_id);\n            if (!!node) {\n                if (!!size) {\n                    node.data['font-size'] = size;\n                }\n                if (!!weight) {\n                    node.data['font-weight'] = weight;\n                }\n                if (!!style) {\n                    node.data['font-style'] = style;\n                }\n                this.view.reset_node_custom_style(node);\n                this.view.update_node(node);\n                this.layout.layout();\n                this.view.show(false);\n            }\n        } else {\n            logger.error('fail, this mind map is not editable');\n            return null;\n        }\n    }\n    set_node_background_image(node_id, image, width, height, rotation) {\n        if (this.get_editable()) {\n            var node = this.mind.get_node(node_id);\n            if (!!node) {\n                if (!!image) {\n                    node.data['background-image'] = image;\n                }\n                if (!!width) {\n                    node.data['width'] = width;\n                }\n                if (!!height) {\n                    node.data['height'] = height;\n                }\n                if (!!rotation) {\n                    node.data['background-rotation'] = rotation;\n                }\n                this.view.reset_node_custom_style(node);\n                this.view.update_node(node);\n                this.layout.layout();\n                this.view.show(false);\n            }\n        } else {\n            logger.error('fail, this mind map is not editable');\n            return null;\n        }\n    }\n    set_node_background_rotation(node_id, rotation) {\n        if (this.get_editable()) {\n            var node = this.mind.get_node(node_id);\n            if (!!node) {\n                if (!node.data['background-image']) {\n                    logger.error(\n                        'fail, only can change rotation angle of node with background image'\n                    );\n                    return null;\n                }\n                node.data['background-rotation'] = rotation;\n                this.view.reset_node_custom_style(node);\n                this.view.update_node(node);\n                this.layout.layout();\n                this.view.show(false);\n            }\n        } else {\n            logger.error('fail, this mind map is not editable');\n            return null;\n        }\n    }\n    resize() {\n        this.view.resize();\n    }\n    // callback(type ,data)\n    add_event_listener(callback) {\n        if (typeof callback === 'function') {\n            this.event_handles.push(callback);\n        }\n    }\n    clear_event_listener() {\n        this.event_handles = [];\n    }\n    invoke_event_handle(type, data) {\n        var j = this;\n        $.w.setTimeout(function () {\n            j._invoke_event_handle(type, data);\n        }, 0);\n    }\n    _invoke_event_handle(type, data) {\n        var l = this.event_handles.length;\n        for (var i = 0; i < l; i++) {\n            this.event_handles[i](type, data);\n        }\n    }\n\n    static show(options, mind) {\n        logger.warn(\n            '`jsMind.show(options, mind)` is deprecated, please use `jm = new jsMind(options); jm.show(mind);` instead'\n        );\n        var _jm = new jsMind(options);\n        _jm.show(mind);\n        return _jm;\n    }\n}\n"], "names": ["__version__", "String", "prototype", "startsWith", "p", "this", "slice", "length", "Direction", "left", "center", "right", "of", "dir", "parseInt", "toLowerCase", "EventType", "show", "resize", "edit", "select", "LogLevel", "debug", "info", "warn", "error", "disable", "_noop", "logger", "console", "level", "log", "log_level", "Dom", "$", "_createClass", "w", "_classCallCheck", "d", "document", "g", "id", "getElementById", "c", "tag", "createElement", "t", "n", "hasChildNodes", "<PERSON><PERSON><PERSON><PERSON>", "nodeValue", "append<PERSON><PERSON><PERSON>", "createTextNode", "h", "HTMLElement", "innerHTML", "i", "el", "_typeof", "nodeType", "style", "ownerDocument", "on", "e", "addEventListener", "attachEvent", "window", "util", "file", "read", "file_data", "fn_callback", "reader", "FileReader", "onload", "result", "name", "readAsText", "save", "type", "blob", "Blob", "bb", "BlobBuilder", "MozBlobBuilder", "WebKitBlobBuilder", "MSBlobBuilder", "append", "getBlob", "navigator", "msSaveBlob", "blob_url", "URL", "webkitURL", "createObjectURL", "anchor", "visibility", "href", "download", "body", "evt", "createEvent", "initEvent", "dispatchEvent", "<PERSON><PERSON><PERSON><PERSON>", "location", "json", "json2string", "JSON", "stringify", "string2json", "json_str", "parse", "merge", "b", "a", "o", "Object", "toString", "call", "uuid", "newid", "Date", "getTime", "Math", "random", "substring", "text", "is_empty", "s", "replace", "default_options", "container", "editable", "theme", "mode", "support_html", "view", "engine", "h<PERSON>gin", "vmargin", "line_width", "line_color", "line_style", "draggable", "hide_scrollbars_when_draggable", "node_overflow", "zoom", "min", "max", "step", "custom_node_render", "expander_style", "layout", "hspace", "vspace", "pspace", "cousin_space", "default_event_handle", "enable_mousedown_handle", "enable_click_handle", "enable_dblclick_handle", "enable_mousewheel_handle", "shortcut", "enable", "handles", "mapping", "addchild", "addbrother", "editnode", "delnode", "toggle", "up", "down", "plugin", "Node", "sId", "iIndex", "sTopic", "oData", "bIsRoot", "oParent", "eDirection", "bExpanded", "index", "topic", "data", "isroot", "parent", "direction", "expanded", "children", "_data", "key", "value", "vd", "x", "abs_x", "y", "abs_y", "width", "height", "node1", "node2", "i1", "i2", "parent_node", "node", "pid", "Mind", "author", "version", "root", "selected", "nodes", "node_id", "_put_node", "idx", "is_node", "push", "_update_index", "node_before", "node_index", "add_node", "the_node", "get_node", "get_node_before", "node_after", "get_node_after", "before_id", "parent_id", "_move_node", "len", "_flow_node_direction", "inherited", "sibling", "si", "splice", "origin_parent", "_move_node_internal", "ci", "remove_node", "node_parent", "k", "sort", "compare", "DEFAULT_META", "format", "node_tree", "example", "meta", "get_mind", "source", "df", "mind", "_parse", "get_data", "_build_node", "node_root", "_extract_data", "set_root", "_extract_subnode", "node_json", "node_data", "node_array", "_array", "reverse", "root_node", "_extract_root", "root_json", "extract_count", "parentid", "node_direction", "sub_extract_count", "_array_node", "freemind", "xml", "xml_doc", "_parse_xml", "xml_root", "_find_root", "_load_node", "xml_lines", "_build_map", "join", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "parseFromString", "ActiveXObject", "async", "loadXML", "childNodes", "tagName", "ns", "xml_node", "getAttribute", "node_topic", "node_folded", "topic_children", "topic_child", "textContent", "_load_attributes", "node_expanded", "node_position", "child", "attr", "attr_data", "pos", "_escape", "_line_regex", "lines", "split", "_fill_nodes", "node_path", "line", "match", "substr", "diff", "pop", "_build_lines", "_step", "prefix", "Array", "_iterator", "_createForOfIteratorHelper", "done", "err", "f", "DataProvider", "jm", "mind_data", "data_format", "LayoutProvider", "options", "opts", "isside", "bounds", "cache_valid", "children_len", "r", "layout_direction", "layout_offset", "_layout_direction_root", "layout_data", "children_count", "side_index", "_layout_direction_side", "subnode", "offset_x", "offset_y", "outer_height", "left_nodes", "right_nodes", "unshift", "outer_height_left", "_layout_offset_subnodes", "outer_height_right", "total_height", "nodes_count", "node_outer_height", "base_y", "pd", "set_visible", "middle_height", "_layout_offset_subnodes_height", "offset_cache", "_offset_", "offset_p", "get_node_offset", "view_data", "pout_cache", "_pout_", "get_node_point_out", "ex_p", "ceil", "pout", "collapse_node", "expand_node", "part_layout", "invoke_event_handle", "target_depth", "curr_nodes", "curr_depth", "depth", "expand_to_depth", "root_layout_data", "visible", "SvgGraph", "e_svg", "setAttribute", "size", "line_drawing", "straight", "_line_to", "curved", "_bezier_to", "drawing", "pin", "offset", "color", "dest_canvas_ctx", "callback", "img", "Image", "drawImage", "src", "btoa", "XMLSerializer", "serializeToString", "path", "x1", "y1", "x2", "y2", "createElementNS", "CanvasGraph", "e_canvas", "className", "canvas_ctx", "getContext", "clearRect", "ctx", "strokeStyle", "lineWidth", "lineCap", "beginPath", "moveTo", "bezierCurveTo", "stroke", "lineTo", "ViewProvider", "e_panel", "e_nodes", "selected_node", "editing_node", "graph", "render_node", "_custom_node_render", "_default_node_render", "_initialized", "_this", "e_editor", "tabIndex", "element", "zoom_current", "v", "event", "keyCode", "edit_node_end", "stopPropagation", "offsetParent", "IntersectionObserver", "entities", "observer", "_newArrowCheck", "isIntersecting", "unobserve", "bind", "observe", "obj", "event_name", "event_handle", "capture_by_panel", "target", "get_binded_nodeid", "parentElement", "clear_lines", "clear_nodes", "reset_theme", "theme_name", "nodeid", "reset_node_custom_style", "setup_canvas_draggable", "init_nodes", "min_size", "get_min_size", "min_width", "min_height", "client_w", "clientWidth", "client_h", "clientHeight", "_this2", "doc_frag", "createDocumentFragment", "create_node_element", "run_in_c11y_mode_if_needed", "init_nodes_size", "_this3", "func", "position", "top", "d_e", "expander", "_reset_node_custom_style", "is_visible", "origin_style", "restore_selected_node_custom_style", "clear_selected_node_custom_style", "select_node", "ncs", "getComputedStyle", "getPropertyValue", "zIndex", "focus", "update_node", "rootNode", "get_root", "isHide", "hideNode", "_x", "_y", "set_size", "expand_size", "_show", "childList", "hideLine", "show_nodes", "show_lines", "set_zoom", "e_panel_rect", "getBoundingClientRect", "zoom_center", "panel_scroll_x", "scrollLeft", "panel_scroll_y", "scrollTop", "keep_center", "center_node", "_saved_location", "node_element", "view_offset", "get_view_offset", "get_node_point", "display", "_show_expander", "expander_text", "_get_expander_text", "p_expander", "get_expander_point", "backgroundColor", "expandColor", "ele", "fontSize", "fontWeight", "fontStyle", "backgroundImage", "scaledImageData", "toDataURL", "backgroundSize", "transform", "clear", "_offset", "get_node_point_in", "draw_line", "enabled", "_this4", "dragging", "eventDown", "clientX", "clientY", "eventMove", "scrollBy", "node_center_point", "scrollTo", "zoom_in", "zoom_out", "ShortcutProvider", "_newid", "_mapping", "handle", "handler", "handle_addchild", "handle_addbrother", "handle_editnode", "handle_delnode", "handle_toggle", "handle_up", "handle_down", "handle_left", "handle_right", "keys", "isArray", "id_generator", "which", "preventDefault", "is_editing", "kc", "metaKey", "ctrl<PERSON>ey", "altKey", "shift<PERSON>ey", "_jm", "get_selected_node", "begin_edit", "insert_node_after", "toggle_node", "up_node", "find_node_before", "np", "down_node", "find_node_after", "_handle_direction", "floor", "plugin_data", "plugins", "apply", "setTimeout", "for<PERSON>ach", "fn_init", "_apply", "Plugin", "Error", "option", "Element", "jsMind", "current", "merge_option", "initialized", "event_handles", "init", "opts_layout", "opts_view", "_event_bind", "apply_plugins", "theme_old", "reset_custom_style", "add_event", "mousedown_handle", "click_handle", "dblclick_handle", "mousewheel_handle", "srcElement", "select_clear", "is_expander", "get_editable", "deltaY", "edit_node_begin", "save_location", "relayout", "restore_location", "expand_all", "collapse_all", "reset", "m", "load", "_reset", "the_parent_node", "undefined", "calculate_next_child_direction", "the_node_before", "insert_node_before", "the_node_after", "_util", "updated_node", "move_node", "scroll_node_to_center", "prev", "ni", "found", "bg_color", "fg_color", "weight", "image", "rotation", "j", "_invoke_event_handle", "l", "_defineProperty"], "mappings": "qhHAAO,IAAMA,EAAc,QAGe,mBAA/BC,OAAOC,UAAUC,aACxBF,OAAOC,UAAUC,WAAa,SAAUC,GACpC,OAAOC,KAAKC,MAAM,EAAGF,EAAEG,UAAYH,IAIpC,IAAMI,EAAY,CACrBC,MAAO,EACPC,OAAQ,EACRC,MAAO,EACPC,GAAI,SAAUC,GACV,OAAKA,IAAgB,IAATA,GAAsB,IAARA,GAAqB,IAARA,EAG3B,OAARA,GAAwB,MAARA,GAAuB,MAARA,EACxBC,SAASD,GAEM,SAAtBA,EAAIE,cACGV,KAAKI,KAEU,UAAtBI,EAAIE,cACGV,KAAKM,MAEU,WAAtBE,EAAIE,cACGV,KAAKK,YADhB,EAXWG,CAcf,GAESG,EAAY,CAAEC,KAAM,EAAGC,OAAQ,EAAGC,KAAM,EAAGC,OAAQ,GAEnDC,EAAW,CAAEC,MAAO,EAAGC,KAAM,EAAGC,KAAM,EAAGC,MAAO,EAAGC,QAAS,GAGrEC,EAAQ,aACDC,EACY,oBAAZC,QACD,CACIC,MAAOH,EACPI,IAAKJ,EACLL,MAAOK,EACPJ,KAAMI,EACNH,KAAMG,EACNF,MAAOE,GAEX,CACIG,MAQd,SAA4BE,GAEpBJ,EAAON,MADPU,EAAYX,EAASC,MACNK,EAEAE,QAAQP,MAGvBM,EAAOL,KADPS,EAAYX,EAASE,KACPI,EAEAE,QAAQN,KAGtBK,EAAOJ,KADPQ,EAAYX,EAASG,KACPG,EAEAE,QAAQL,KAGtBI,EAAOH,MADPO,EAAYX,EAASI,MACNE,EAEAE,QAAQJ,KAE/B,EA5BcM,IAAKF,QAAQE,IACbT,MAAOO,QAAQP,MACfC,KAAMM,QAAQN,KACdC,KAAMK,QAAQL,KACdC,MAAOI,QAAQJ,OCrDvBQ,IAgDOC,EAAI,IAhDRC,GACL,SAAAF,EAAYG,GAAGC,OAAAJ,GACX5B,KAAK+B,EAAIA,EACT/B,KAAKiC,EAAIF,EAAEG,SACXlC,KAAKmC,EAAI,SAAUC,GACf,OAAOpC,KAAKiC,EAAEI,eAAeD,IAEjCpC,KAAKsC,EAAI,SAAUC,GACf,OAAOvC,KAAKiC,EAAEO,cAAcD,IAEhCvC,KAAKyC,EAAI,SAAUC,EAAGD,GACdC,EAAEC,gBACFD,EAAEE,WAAWC,UAAYJ,EAEzBC,EAAEI,YAAY9C,KAAKiC,EAAEc,eAAeN,KAI5CzC,KAAKgD,EAAI,SAAUN,EAAGD,GACdA,aAAaQ,aACbP,EAAEQ,UAAY,GACdR,EAAEI,YAAYL,IAEdC,EAAEQ,UAAYT,GAItBzC,KAAKmD,EAAI,SAAUC,GACf,QACMA,GACY,WAAdC,EAAOD,IACS,IAAhBA,EAAGE,UACiB,WAApBD,EAAOD,EAAGG,QACkB,WAA5BF,EAAOD,EAAGI,gBAKlBxD,KAAKyD,GAAK,SAAUhB,EAAGiB,EAAGV,GAChBP,EAAEkB,iBACJlB,EAAEkB,iBAAiBD,EAAGV,GAAG,GAEzBP,EAAEmB,YAAY,KAAOF,EAAGV,GAGpC,IAGa,CAAQa,QC9CZC,EAAO,CAChBC,KAAM,CACFC,KAAM,SAAUC,EAAWC,GACvB,IAAIC,EAAS,IAAIC,WACjBD,EAAOE,OAAS,WACe,mBAAhBH,GACPA,EAAYlE,KAAKsE,OAAQL,EAAUM,OAG3CJ,EAAOK,WAAWP,EACrB,EAEDQ,KAAM,SAAUR,EAAWS,EAAMH,GAC7B,IAAII,EACJ,GAAwB,mBAAb9C,EAAEE,EAAE6C,KACXD,EAAO,IAAIC,KAAK,CAACX,GAAY,CAAES,KAAMA,QAClC,CACH,IAKIG,EAAK,IAJLhD,EAAEE,EAAE+C,aACJjD,EAAEE,EAAEgD,gBACJlD,EAAEE,EAAEiD,mBACJnD,EAAEE,EAAEkD,eAERJ,EAAGK,OAAOjB,GACVU,EAAOE,EAAGM,QAAQT,EACtB,CACA,GAAIU,UAAUC,WACVD,UAAUC,WAAWV,EAAMJ,OACxB,CACH,IACIe,GADMzD,EAAEE,EAAEwD,KAAO1D,EAAEE,EAAEyD,WACNC,gBAAgBd,GAC/Be,EAAS7D,EAAES,EAAE,KACjB,GAAI,aAAcoD,EAAQ,CACtBA,EAAOnC,MAAMoC,WAAa,SAC1BD,EAAOE,KAAON,EACdI,EAAOG,SAAWtB,EAClB1C,EAAEI,EAAE6D,KAAKhD,YAAY4C,GACrB,IAAIK,EAAMlE,EAAEI,EAAE+D,YAAY,eAC1BD,EAAIE,UAAU,SAAS,GAAM,GAC7BP,EAAOQ,cAAcH,GACrBlE,EAAEI,EAAE6D,KAAKK,YAAYT,EACzB,MACIU,SAASR,KAAON,CAExB,CACJ,GAGJe,KAAM,CACFC,YAAa,SAAUD,GACnB,OAAOE,KAAKC,UAAUH,EACzB,EACDI,YAAa,SAAUC,GACnB,OAAOH,KAAKI,MAAMD,EACrB,EACDE,MAAO,SAAUC,EAAGC,GAChB,IAAK,IAAIC,KAAKD,EACNC,KAAKF,EAEe,WAAhBxD,EAAOwD,EAAEE,KAC6C,mBAAtDC,OAAOnH,UAAUoH,SAASC,KAAKL,EAAEE,IAAIrG,eACpCmG,EAAEE,GAAG7G,OAIN2G,EAAEE,GAAKD,EAAEC,GAFTjD,EAAKuC,KAAKO,MAAMC,EAAEE,GAAID,EAAEC,IAK5BF,EAAEE,GAAKD,EAAEC,GAGjB,OAAOF,CACX,GAGJM,KAAM,CACFC,MAAO,WACH,QACI,IAAIC,MAAOC,UAAUL,SAAS,IAAMM,KAAKC,SAASP,SAAS,IAAIQ,UAAU,IAC3EA,UAAU,EAAG,GACnB,GAGJC,KAAM,CACFC,SAAU,SAAUC,GAChB,OAAKA,GAGiC,GAA/BA,EAAEC,QAAQ,MAAO,IAAI3H,MAChC,ICzFF4H,EAAkB,CACpBC,UAAW,GACXC,UAAU,EACVC,MAAO,KACPC,KAAM,OACNC,cAAc,EACdxG,UAAW,OAEXyG,KAAM,CACFC,OAAQ,SACRC,QAAS,IACTC,QAAS,GACTC,WAAY,EACZC,WAAY,OACZC,WAAY,SACZC,WAAW,EACXC,gCAAgC,EAChCC,cAAe,SACfC,KAAM,CACFC,IAAK,GACLC,IAAK,IACLC,KAAM,IAEVC,mBAAoB,KACpBC,eAAgB,QAEpBC,OAAQ,CACJC,OAAQ,GACRC,OAAQ,GACRC,OAAQ,GACRC,aAAc,GAElBC,qBAAsB,CAClBC,yBAAyB,EACzBC,qBAAqB,EACrBC,wBAAwB,EACxBC,0BAA0B,GAE9BC,SAAU,CACNC,QAAQ,EACRC,QAAS,CAAE,EACXC,QAAS,CACLC,SAAU,CAAC,GAAI,MACfC,WAAY,GACZC,SAAU,IACVC,QAAS,GACTC,OAAQ,GACRlK,KAAM,GACNmK,GAAI,GACJjK,MAAO,GACPkK,KAAM,KAGdC,OAAQ,CAAC,GCtDb,IAAaC,EAAI,WACb,SAAAA,EAAYC,EAAKC,EAAQC,EAAQC,EAAOC,EAASC,EAASC,EAAYC,GAAWlJ,OAAA0I,GACxEC,EAIgB,iBAAVC,QAIc,IAAdM,IACPA,GAAY,GAEhBlL,KAAKoC,GAAKuI,EACV3K,KAAKmL,MAAQP,EACb5K,KAAKoL,MAAQP,EACb7K,KAAKqL,KAAOP,GAAS,GACrB9K,KAAKsL,OAASP,EACd/K,KAAKuL,OAASP,EACdhL,KAAKwL,UAAYP,EACjBjL,KAAKyL,WAAaP,EAClBlL,KAAK0L,SAAW,GAChB1L,KAAK2L,MAAQ,IAfTpK,EAAOH,MAAM,sBAJbG,EAAOH,MAAM,kBAoBrB,CAAC,OAAAU,EAAA4I,EAAA,CAAA,CAAAkB,IAAA,eAAAC,MAED,WACI,IAAIC,EAAK9L,KAAK2L,MAAMvD,KACpB,MAAO,CACH2D,EAAGD,EAAGE,MACNC,EAAGH,EAAGI,MAEd,GAAC,CAAAN,IAAA,WAAAC,MACD,WACI,IAAIC,EAAK9L,KAAK2L,MAAMvD,KACpB,MAAO,CACHrG,EAAG+J,EAAGK,MACNnJ,EAAG8I,EAAGM,OAEd,IAAC,CAAA,CAAAR,IAAA,UAAAC,MAED,SAAeQ,EAAOC,GAElB,IACIC,EAAKF,EAAMlB,MACXqB,EAAKF,EAAMnB,MAYf,OAXIoB,GAAM,GAAKC,GAAM,EACbD,EAAKC,GACK,GAAPD,IAAmB,GAAPC,EACf,GACU,GAAPD,EACH,GACU,GAAPC,GACF,EAED,CAGZ,GAAC,CAAAZ,IAAA,YAAAC,MACD,SAAiBY,EAAaC,GAC1B,GAAMD,GAAiBC,EAAM,CACzB,GAAID,EAAYrK,KAAOsK,EAAKtK,GACxB,OAAO,EAEX,GAAIqK,EAAYnB,OACZ,OAAO,EAIX,IAFA,IAAIqB,EAAMF,EAAYrK,GAClBrC,EAAI2M,GACA3M,EAAEuL,QAEN,IADAvL,EAAIA,EAAEwL,QACAnJ,KAAOuK,EACT,OAAO,CAGnB,CACA,OAAO,CACX,GAAC,CAAAf,IAAA,UAAAC,MACD,SAAenJ,GACX,QAASA,GAAKA,aAAagI,CAC/B,IAAC,CA/EY,GCUJkC,EAAI,WAQZ,OAAA9K,GAPD,SAAA8K,IAAc5K,OAAA4K,GACV5M,KAAKuE,KAAO,KACZvE,KAAK6M,OAAS,KACd7M,KAAK8M,QAAU,KACf9M,KAAK+M,KAAO,KACZ/M,KAAKgN,SAAW,KAChBhN,KAAKiN,MAAQ,EACjB,GAAC,CAAA,CAAArB,IAAA,WAAAC,MACD,SAASqB,GACL,OAAIA,KAAWlN,KAAKiN,MACTjN,KAAKiN,MAAMC,IAElB3L,EAAOJ,KAAK,eAAiB+L,EAAU,sBAChC,KAEf,GAAC,CAAAtB,IAAA,WAAAC,MACD,SAASqB,EAAS9B,EAAOC,GACrB,OAAiB,MAAbrL,KAAK+M,MACL/M,KAAK+M,KAAO,IAAIrC,EAAKwC,EAAS,EAAG9B,EAAOC,GAAM,GAC9CrL,KAAKmN,UAAUnN,KAAK+M,MACb/M,KAAK+M,OAEZxL,EAAOH,MAAM,8BACN,KAEf,GAAC,CAAAwK,IAAA,WAAAC,MACD,SAASY,EAAaS,EAAS9B,EAAOC,EAAMG,EAAWC,EAAU2B,GAC7D,IAAK1C,EAAK2C,QAAQZ,GAEd,OADAlL,EAAOH,MAAM,mBAAqBqL,EAAc,mBACzC,KAEX,IACIC,EAAO,IAAIhC,EACXwC,EAFaE,IAAQ,EAIrBhC,EACAC,GACA,EACAoB,EACAA,EAAYjB,UACZC,GAYJ,OAVIgB,EAAYnB,SACZoB,EAAKlB,UAAYA,GAAarL,EAAUG,OAExCN,KAAKmN,UAAUT,IACfD,EAAYf,SAAS4B,KAAKZ,GAC1B1M,KAAKuN,cAAcd,KAEnBlL,EAAOH,MAAM,sBAAwBsL,EAAKtK,GAAK,6BAC/CsK,EAAO,MAEJA,CACX,GAAC,CAAAd,IAAA,qBAAAC,MACD,SAAmB2B,EAAaN,EAAS9B,EAAOC,EAAMG,GAClD,IAAKd,EAAK2C,QAAQG,GAEd,OADAjM,EAAOH,MAAM,mBAAqBoM,EAAc,mBACzC,KAEX,IAAIC,EAAaD,EAAYrC,MAAQ,GACrC,OAAOnL,KAAK0N,SAASF,EAAYjC,OAAQ2B,EAAS9B,EAAOC,EAAMG,GAAW,EAAMiC,EACpF,GAAC,CAAA7B,IAAA,kBAAAC,MACD,SAAgBa,GACZ,IAAKhC,EAAK2C,QAAQX,GAAO,CACrB,IAAIiB,EAAW3N,KAAK4N,SAASlB,GAC7B,OAAKiB,EAIM3N,KAAK6N,gBAAgBF,IAH5BpM,EAAOH,MAAM,eAAiBsL,EAAO,uBAC9B,KAIf,CACA,GAAIA,EAAKpB,OACL,OAAO,KAEX,IAAI8B,EAAMV,EAAKvB,MAAQ,EACvB,OAAIiC,GAAO,EACAV,EAAKnB,OAAOG,SAAS0B,GAErB,IAEf,GAAC,CAAAxB,IAAA,oBAAAC,MACD,SAAkBiC,EAAYZ,EAAS9B,EAAOC,EAAMG,GAChD,IAAKd,EAAK2C,QAAQS,GAEd,OADAvM,EAAOH,MAAM,kBAAoB0M,EAAa,mBACvC,KAEX,IAAIL,EAAaK,EAAW3C,MAAQ,GACpC,OAAOnL,KAAK0N,SAASI,EAAWvC,OAAQ2B,EAAS9B,EAAOC,EAAMG,GAAW,EAAMiC,EACnF,GAAC,CAAA7B,IAAA,iBAAAC,MACD,SAAea,GACX,IAAKhC,EAAK2C,QAAQX,GAAO,CACrB,IAAIiB,EAAW3N,KAAK4N,SAASlB,GAC7B,OAAKiB,EAIM3N,KAAK+N,eAAeJ,IAH3BpM,EAAOH,MAAM,eAAiBsL,EAAO,uBAC9B,KAIf,CACA,GAAIA,EAAKpB,OACL,OAAO,KAEX,IAAI8B,EAAMV,EAAKvB,MAEf,OADeuB,EAAKnB,OAAOG,SACdxL,OAASkN,EACXV,EAAKnB,OAAOG,SAAS0B,GAErB,IAEf,GAAC,CAAAxB,IAAA,YAAAC,MACD,SAAUa,EAAMsB,EAAWC,EAAWzC,GAClC,OAAKd,EAAK2C,QAAQX,IAIbuB,IACDA,EAAYvB,EAAKnB,OAAOnJ,IAErBpC,KAAKkO,WAAWxB,EAAMsB,EAAWC,EAAWzC,KAN/CjK,EAAOH,MAAM,sBAAwBsL,EAAO,mBACrC,KAMf,GAAC,CAAAd,IAAA,uBAAAC,MACD,SAAqBa,EAAMlB,QACE,IAAdA,EACPA,EAAYkB,EAAKlB,UAEjBkB,EAAKlB,UAAYA,EAGrB,IADA,IAAI2C,EAAMzB,EAAKhB,SAASxL,OACjBiO,KACHnO,KAAKoO,qBAAqB1B,EAAKhB,SAASyC,GAAM3C,EAEtD,GAAC,CAAAI,IAAA,sBAAAC,MACD,SAAoBa,EAAMsB,GACtB,GAAMtB,GAAUsB,EACZ,GAAiB,UAAbA,EACAtB,EAAKvB,OAAS,EACdnL,KAAKuN,cAAcb,EAAKnB,aACrB,GAAiB,WAAbyC,EACPtB,EAAKvB,MAAQ,EACbnL,KAAKuN,cAAcb,EAAKnB,YACrB,CACH,IAAIiC,EAAgBQ,EAAYhO,KAAK4N,SAASI,GAAa,KAExC,MAAfR,GACsB,MAAtBA,EAAYjC,QACZiC,EAAYjC,OAAOnJ,IAAMsK,EAAKnB,OAAOnJ,KAErCsK,EAAKvB,MAAQqC,EAAYrC,MAAQ,GACjCnL,KAAKuN,cAAcb,EAAKnB,QAEhC,CAEJ,OAAOmB,CACX,GAAC,CAAAd,IAAA,aAAAC,MACD,SAAWa,EAAMsB,EAAWC,EAAWzC,GACnC,GAAMkB,GAAUuB,EAAW,CACvB,IAAIxB,EAAczM,KAAK4N,SAASK,GAChC,GAAIvD,EAAK2D,UAAU3B,EAAMD,GAErB,OADAlL,EAAOH,MAAM,uCACN,KAEX,GAAIsL,EAAKnB,OAAOnJ,IAAM6L,EAAW,CAI7B,IAFA,IAAIK,EAAU5B,EAAKnB,OAAOG,SACtB6C,EAAKD,EAAQpO,OACVqO,KACH,GAAID,EAAQC,GAAInM,IAAMsK,EAAKtK,GAAI,CAC3BkM,EAAQE,OAAOD,EAAI,GACnB,KACJ,CAEJ,IAAIE,EAAgB/B,EAAKnB,OACzBmB,EAAKnB,OAASkB,EACdA,EAAYf,SAAS4B,KAAKZ,GAC1B1M,KAAKuN,cAAckB,EACvB,CAEI/B,EAAKnB,OAAOD,OACRE,GAAarL,EAAUC,KACvBsM,EAAKlB,UAAYA,EAEjBkB,EAAKlB,UAAYrL,EAAUG,MAG/BoM,EAAKlB,UAAYkB,EAAKnB,OAAOC,UAEjCxL,KAAK0O,oBAAoBhC,EAAMsB,GAC/BhO,KAAKoO,qBAAqB1B,EAC9B,CACA,OAAOA,CACX,GAAC,CAAAd,IAAA,cAAAC,MACD,SAAYa,GACR,IAAKhC,EAAK2C,QAAQX,GAEd,OADAnL,EAAOH,MAAM,sBAAwBsL,EAAO,oBACrC,EAEX,GAAIA,EAAKpB,OAEL,OADA/J,EAAOH,MAAM,mCACN,EAEU,MAAjBpB,KAAKgN,UAAoBhN,KAAKgN,SAAS5K,IAAMsK,EAAKtK,KAClDpC,KAAKgN,SAAW,MAKpB,IAFA,IAAItB,EAAWgB,EAAKhB,SAChBiD,EAAKjD,EAASxL,OACXyO,KACH3O,KAAK4O,YAAYlD,EAASiD,IAG9BjD,EAASxL,OAAS,EAKlB,IAJA,IAAI2O,EAAcnC,EAAKnB,OAEnB+C,EAAUO,EAAYnD,SACtB6C,EAAKD,EAAQpO,OACVqO,KACH,GAAID,EAAQC,GAAInM,IAAMsK,EAAKtK,GAAI,CAC3BkM,EAAQE,OAAOD,EAAI,GACnB,KACJ,CAKJ,IAAK,IAAIO,YAFF9O,KAAKiN,MAAMP,EAAKtK,IAETsK,SACHA,EAAKoC,GAKhB,OAFApC,EAAO,KACP1M,KAAKuN,cAAcsB,IACZ,CACX,GAAC,CAAAjD,IAAA,YAAAC,MACD,SAAUa,GACN,OAAIA,EAAKtK,MAAMpC,KAAKiN,OAChB1L,EAAOJ,KAAK,gBAAkBuL,EAAKtK,GAAK,8BACjC,IAEPpC,KAAKiN,MAAMP,EAAKtK,IAAMsK,GACf,EAEf,GAAC,CAAAd,IAAA,gBAAAC,MACD,SAAca,GACV,GAAIA,aAAgBhC,EAAM,CACtBgC,EAAKhB,SAASqD,KAAKrE,EAAKsE,SACxB,IAAK,IAAI7L,EAAI,EAAGA,EAAIuJ,EAAKhB,SAASxL,OAAQiD,IACtCuJ,EAAKhB,SAASvI,GAAGgI,MAAQhI,EAAI,CAErC,CACJ,IAAC,CAzPY,GCNX8L,EAAe,CAAE1K,KAAM,SAAUsI,ONJb,mBMIiCC,QAASnN,GAEvDuP,EAAS,CAClBC,UAAW,CACPC,QAAS,CACLC,KAAMJ,EACNC,OAAQ,YACR7D,KAAM,CAAEjJ,GAAI,OAAQgJ,MAAO,6BAE/BkE,SAAU,SAAUC,GAChB,IAAIC,EAAKN,EAAOC,UACZM,EAAO,IAAI7C,EAKf,OAJA6C,EAAKlL,KAAOgL,EAAOF,KAAK9K,KACxBkL,EAAK5C,OAAS0C,EAAOF,KAAKxC,OAC1B4C,EAAK3C,QAAUyC,EAAOF,KAAKvC,QAC3B0C,EAAGE,OAAOD,EAAMF,EAAOlE,MAChBoE,CACV,EACDE,SAAU,SAAUF,GAChB,IAAID,EAAKN,EAAOC,UACZ9I,EAAO,CAAA,EAQX,OAPAA,EAAKgJ,KAAO,CACR9K,KAAMkL,EAAKlL,KACXsI,OAAQ4C,EAAK5C,OACbC,QAAS2C,EAAK3C,SAElBzG,EAAK6I,OAAS,YACd7I,EAAKgF,KAAOmE,EAAGI,YAAYH,EAAK1C,MACzB1G,CACV,EAEDqJ,OAAQ,SAAUD,EAAMI,GACpB,IAAIL,EAAKN,EAAOC,UACZ9D,EAAOmE,EAAGM,cAAcD,GAE5B,GADAJ,EAAKM,SAASF,EAAUzN,GAAIyN,EAAUzE,MAAOC,GACzC,aAAcwE,EAEd,IADA,IAAInE,EAAWmE,EAAUnE,SAChBvI,EAAI,EAAGA,EAAIuI,EAASxL,OAAQiD,IACjCqM,EAAGQ,iBAAiBP,EAAMA,EAAK1C,KAAMrB,EAASvI,GAGzD,EAED2M,cAAe,SAAUG,GACrB,IAAI5E,EAAO,CAAA,EACX,IAAK,IAAIyD,KAAKmB,EAED,MAALnB,GACK,SAALA,GACK,YAALA,GACK,aAALA,GACK,YAALA,IAIJzD,EAAKyD,GAAKmB,EAAUnB,IAExB,OAAOzD,CACV,EAED2E,iBAAkB,SAAUP,EAAMZ,EAAaoB,GAC3C,IAAIT,EAAKN,EAAOC,UACZ9D,EAAOmE,EAAGM,cAAcG,GACxBhO,EAAI,KACJ4M,EAAYvD,SACZrJ,EAA2B,QAAvBgO,EAAUzE,UAAsBrL,EAAUC,KAAOD,EAAUG,OAEnE,IAAIoM,EAAO+C,EAAK/B,SACZmB,EACAoB,EAAU7N,GACV6N,EAAU7E,MACVC,EACApJ,EACAgO,EAAUxE,UAEd,GAAMwE,EAAoB,SAEtB,IADA,IAAIvE,EAAWuE,EAAUvE,SAChBvI,EAAI,EAAGA,EAAIuI,EAASxL,OAAQiD,IACjCqM,EAAGQ,iBAAiBP,EAAM/C,EAAMhB,EAASvI,GAGpD,EAEDyM,YAAa,SAAUlD,GACnB,IAAI8C,EAAKN,EAAOC,UAChB,GAAMzC,aAAgBhC,EAAtB,CAGA,IAAI3D,EAAI,CACJ3E,GAAIsK,EAAKtK,GACTgJ,MAAOsB,EAAKtB,MACZK,SAAUiB,EAAKjB,UAKnB,GAHMiB,EAAKnB,QAAUmB,EAAKnB,OAAOD,SAC7BvE,EAAEyE,UAAYkB,EAAKlB,WAAarL,EAAUC,KAAO,OAAS,SAE7C,MAAbsM,EAAKrB,KAAc,CACnB,IAAI6E,EAAYxD,EAAKrB,KACrB,IAAK,IAAIyD,KAAKoB,EACVnJ,EAAE+H,GAAKoB,EAAUpB,EAEzB,CACA,IAAIpD,EAAWgB,EAAKhB,SACpB,GAAIA,EAASxL,OAAS,EAAG,CACrB6G,EAAE2E,SAAW,GACb,IAAK,IAAIvI,EAAI,EAAGA,EAAIuI,EAASxL,OAAQiD,IACjC4D,EAAE2E,SAAS4B,KAAKkC,EAAGI,YAAYlE,EAASvI,IAEhD,CACA,OAAO4D,CAtBP,CAuBJ,GAGJoJ,WAAY,CACRf,QAAS,CACLC,KAAMJ,EACNC,OAAQ,aACR7D,KAAM,CAAC,CAAEjJ,GAAI,OAAQgJ,MAAO,4BAA6BE,QAAQ,KAGrEgE,SAAU,SAAUC,GAChB,IAAIC,EAAKN,EAAOiB,WACZV,EAAO,IAAI7C,EAKf,OAJA6C,EAAKlL,KAAOgL,EAAOF,KAAK9K,KACxBkL,EAAK5C,OAAS0C,EAAOF,KAAKxC,OAC1B4C,EAAK3C,QAAUyC,EAAOF,KAAKvC,QAC3B0C,EAAGE,OAAOD,EAAMF,EAAOlE,MAChBoE,CACV,EAEDE,SAAU,SAAUF,GAChB,IAAID,EAAKN,EAAOiB,WACZ9J,EAAO,CAAA,EASX,OARAA,EAAKgJ,KAAO,CACR9K,KAAMkL,EAAKlL,KACXsI,OAAQ4C,EAAK5C,OACbC,QAAS2C,EAAK3C,SAElBzG,EAAK6I,OAAS,aACd7I,EAAKgF,KAAO,GACZmE,EAAGY,OAAOX,EAAMpJ,EAAKgF,MACdhF,CACV,EAEDqJ,OAAQ,SAAUD,EAAMU,GACpB,IAAIX,EAAKN,EAAOiB,WACZlD,EAAQkD,EAAWlQ,MAAM,GAE7BgN,EAAMoD,UACN,IAAIC,EAAYd,EAAGe,cAAcd,EAAMxC,GACjCqD,EACFd,EAAGQ,iBAAiBP,EAAMa,EAAWrD,GAErC1L,EAAOH,MAAM,6BAEpB,EAEDmP,cAAe,SAAUd,EAAMU,GAG3B,IAFA,IAAIX,EAAKN,EAAOiB,WACZhN,EAAIgN,EAAWjQ,OACZiD,KACH,GAAI,WAAYgN,EAAWhN,IAAMgN,EAAWhN,GAAGmI,OAAQ,CACnD,IAAIkF,EAAYL,EAAWhN,GACvBkI,EAAOmE,EAAGM,cAAcU,GACxB9D,EAAO+C,EAAKM,SAASS,EAAUpO,GAAIoO,EAAUpF,MAAOC,GAExD,OADA8E,EAAW3B,OAAOrL,EAAG,GACduJ,CACX,CAEJ,OAAO,IACV,EAEDsD,iBAAkB,SAAUP,EAAMhD,EAAa0D,GAM3C,IALA,IAAIX,EAAKN,EAAOiB,WACZhN,EAAIgN,EAAWjQ,OACf+P,EAAY,KACZ5E,EAAO,KACPoF,EAAgB,EACbtN,KAEH,IADA8M,EAAYE,EAAWhN,IACTuN,UAAYjE,EAAYrK,GAAI,CACtCiJ,EAAOmE,EAAGM,cAAcG,GACxB,IAAIhO,EAAI,KACJ0O,EAAiBV,EAAUzE,UACzBmF,IACF1O,EAAsB,QAAlB0O,EAA2BxQ,EAAUC,KAAOD,EAAUG,OAE9D,IAAIoM,EAAO+C,EAAK/B,SACZjB,EACAwD,EAAU7N,GACV6N,EAAU7E,MACVC,EACApJ,EACAgO,EAAUxE,UAEd0E,EAAW3B,OAAOrL,EAAG,GACrBsN,IACA,IAAIG,EAAoBpB,EAAGQ,iBAAiBP,EAAM/C,EAAMyD,GACpDS,EAAoB,IAEpBzN,EAAIgN,EAAWjQ,OACfuQ,GAAiBG,EAEzB,CAEJ,OAAOH,CACV,EAEDX,cAAe,SAAUG,GACrB,IAAI5E,EAAO,CAAA,EACX,IAAK,IAAIyD,KAAKmB,EAED,MAALnB,GACK,SAALA,GACK,YAALA,GACK,UAALA,GACK,aAALA,GACK,YAALA,IAIJzD,EAAKyD,GAAKmB,EAAUnB,IAExB,OAAOzD,CACV,EAED+E,OAAQ,SAAUX,EAAMU,GACXjB,EAAOiB,WACbU,YAAYpB,EAAK1C,KAAMoD,EAC7B,EAEDU,YAAa,SAAUnE,EAAMyD,GACzB,IAAIX,EAAKN,EAAOiB,WAChB,GAAMzD,aAAgBhC,EAAtB,CAGA,IAAI3D,EAAI,CACJ3E,GAAIsK,EAAKtK,GACTgJ,MAAOsB,EAAKtB,MACZK,SAAUiB,EAAKjB,UAWnB,GATMiB,EAAKnB,SACPxE,EAAE2J,SAAWhE,EAAKnB,OAAOnJ,IAEzBsK,EAAKpB,SACLvE,EAAEuE,QAAS,GAEToB,EAAKnB,QAAUmB,EAAKnB,OAAOD,SAC7BvE,EAAEyE,UAAYkB,EAAKlB,WAAarL,EAAUC,KAAO,OAAS,SAE7C,MAAbsM,EAAKrB,KAAc,CACnB,IAAI6E,EAAYxD,EAAKrB,KACrB,IAAK,IAAIyD,KAAKoB,EACVnJ,EAAE+H,GAAKoB,EAAUpB,EAEzB,CACAqB,EAAW7C,KAAKvG,GAEhB,IADA,IAAI4H,EAAKjC,EAAKhB,SAASxL,OACdiD,EAAI,EAAGA,EAAIwL,EAAIxL,IACpBqM,EAAGqB,YAAYnE,EAAKhB,SAASvI,GAAIgN,EAxBrC,CA0BJ,GAGJW,SAAU,CACN1B,QAAS,CACLC,KAAMJ,EACNC,OAAQ,WACR7D,KAAM,+EAEViE,SAAU,SAAUC,GAChB,IAAIC,EAAKN,EAAO4B,SACZrB,EAAO,IAAI7C,EACf6C,EAAKlL,KAAOgL,EAAOF,KAAK9K,KACxBkL,EAAK5C,OAAS0C,EAAOF,KAAKxC,OAC1B4C,EAAK3C,QAAUyC,EAAOF,KAAKvC,QAC3B,IAAIiE,EAAMxB,EAAOlE,KACb2F,EAAUxB,EAAGyB,WAAWF,GACxBG,EAAW1B,EAAG2B,WAAWH,GAE7B,OADAxB,EAAG4B,WAAW3B,EAAM,KAAMyB,GACnBzB,CACV,EAEDE,SAAU,SAAUF,GAChB,IAAID,EAAKN,EAAO4B,SACZzK,EAAO,CAAA,EACXA,EAAKgJ,KAAO,CACR9K,KAAMkL,EAAKlL,KACXsI,OAAQ4C,EAAK5C,OACbC,QAAS2C,EAAK3C,SAElBzG,EAAK6I,OAAS,WACd,IAAImC,EAAY,GAKhB,OAJAA,EAAU/D,KAAK,yBACfkC,EAAG8B,WAAW7B,EAAK1C,KAAMsE,GACzBA,EAAU/D,KAAK,UACfjH,EAAKgF,KAAOgG,EAAUE,KAAK,IACpBlL,CACV,EAED4K,WAAY,SAAUF,GAClB,IAAIC,EAAU,KACVnN,OAAO2N,UAEPR,GADa,IAAIQ,WACAC,gBAAgBV,EAAK,cAGtCC,EAAU,IAAIU,cAAc,qBACpBC,OAAQ,EAChBX,EAAQY,QAAQb,IAEpB,OAAOC,CACV,EAEDG,WAAY,SAAUH,GAKlB,IAJA,IAAI/D,EAAQ+D,EAAQa,WAChBnF,EAAO,KAEPhK,EAAI,KACCS,EAAI,EAAGA,EAAI8J,EAAM/M,OAAQiD,IAE9B,GAAkB,IADlBT,EAAIuK,EAAM9J,IACJG,UAA8B,OAAbZ,EAAEoP,QAAkB,CACvCpF,EAAOhK,EACP,KACJ,CAEJ,GAAMgK,EAAM,CACR,IAAIqF,EAAKrF,EAAKmF,WACdnF,EAAO,KACP,IAASvJ,EAAI,EAAGA,EAAI4O,EAAG7R,OAAQiD,IAE3B,GAAkB,IADlBT,EAAIqP,EAAG5O,IACDG,UAA8B,QAAbZ,EAAEoP,QAAmB,CACxCpF,EAAOhK,EACP,KACJ,CAER,CACA,OAAOgK,CACV,EAED0E,WAAY,SAAU3B,EAAMhD,EAAauF,GACrC,IAAIxC,EAAKN,EAAO4B,SACZ5D,EAAU8E,EAASC,aAAa,MAChCC,EAAaF,EAASC,aAAa,QACnCE,EAAcH,EAASC,aAAa,UAExC,GAAkB,MAAdC,EAGA,IAFA,IAAIE,EAAiBJ,EAASH,WAC1BQ,EAAc,KACTlP,EAAI,EAAGA,EAAIiP,EAAelS,OAAQiD,IAEvC,GAA4B,IAD5BkP,EAAcD,EAAejP,IACbG,UAAyC,gBAAxB+O,EAAYP,QAA2B,CACpEI,EAAaG,EAAYC,YACzB,KACJ,CAGR,IAAIpC,EAAYV,EAAG+C,iBAAiBP,GAChCQ,EACA,aAActC,EAAkC,QAAtBA,EAAUzE,SAAoC,QAAf0G,SACtDjC,EAAUzE,SAEjB,IAAIgH,EAAgBT,EAASC,aAAa,YACtCtB,EAAiB,KACf8B,IACF9B,EAAkC,QAAjB8B,EAA0BtS,EAAUC,KAAOD,EAAUG,OAE1E,IAAIoM,EAAO,KAEPA,EADED,EACKgD,EAAK/B,SACRjB,EACAS,EACAgF,EACAhC,EACAS,EACA6B,GAGG/C,EAAKM,SAAS7C,EAASgF,EAAYhC,GAE9C,IAAIxE,EAAWsG,EAASH,WACpBa,EAAQ,KACZ,IAASvP,EAAI,EAAGA,EAAIuI,EAASxL,OAAQiD,IAEX,IADtBuP,EAAQhH,EAASvI,IACPG,UAAkC,QAAjBoP,EAAMZ,SAC7BtC,EAAG4B,WAAW3B,EAAM/C,EAAMgG,EAGrC,EAEDH,iBAAkB,SAAUP,GAIxB,IAHA,IAAItG,EAAWsG,EAASH,WACpBc,EAAO,KACPC,EAAY,CAAA,EACPzP,EAAI,EAAGA,EAAIuI,EAASxL,OAAQiD,IAEZ,IADrBwP,EAAOjH,EAASvI,IACPG,UAAkC,cAAjBqP,EAAKb,UAC3Bc,EAAUD,EAAKV,aAAa,SAAWU,EAAKV,aAAa,UAGjE,OAAOW,CACV,EAEDtB,WAAY,SAAU5E,EAAM2E,GACxB,IAAI7B,EAAKN,EAAO4B,SACZ+B,EAAM,KACJnG,EAAKnB,QAAUmB,EAAKnB,OAAOD,SAC7BuH,EAAMnG,EAAKlB,YAAcrL,EAAUC,KAAO,OAAS,SAEvDiR,EAAU/D,KAAK,SACf+D,EAAU/D,KAAK,QAAUZ,EAAKtK,GAAK,KAC7ByQ,GACFxB,EAAU/D,KAAK,cAAgBuF,EAAM,KAEpCnG,EAAKjB,UACN4F,EAAU/D,KAAK,kBAEnB+D,EAAU/D,KAAK,UAAYkC,EAAGsD,QAAQpG,EAAKtB,OAAS,MAGpD,IAAI8E,EAAYxD,EAAKrB,KACrB,GAAiB,MAAb6E,EACA,IAAK,IAAIpB,KAAKoB,EACVmB,EAAU/D,KAAK,oBAAsBwB,EAAI,YAAcoB,EAAUpB,GAAK,OAM9E,IADA,IAAIpD,EAAWgB,EAAKhB,SACXvI,EAAI,EAAGA,EAAIuI,EAASxL,OAAQiD,IACjCqM,EAAG8B,WAAW5F,EAASvI,GAAIkO,GAG/BA,EAAU/D,KAAK,UAClB,EAEDwF,QAAS,SAAUpL,GACf,OAAOA,EACFG,QAAQ,KAAM,SACdA,QAAQ,KAAM,QACdA,QAAQ,KAAM,QACdA,QAAQ,KAAM,UACdA,QAAQ,KAAM,SACvB,GAEJH,KAAM,CACF0H,QAAS,CACLC,KAAMJ,EACNC,OAAQ,OACR7D,KAAM,iEAEV0H,YAAa,MACbzD,SAAU,SAAUC,GAChB,IAAIC,EAAKN,EAAOxH,KACZ+H,EAAO,IAAI7C,EACf6C,EAAKlL,KAAOgL,EAAOF,KAAK9K,KACxBkL,EAAK5C,OAAS0C,EAAOF,KAAKxC,OAC1B4C,EAAK3C,QAAUyC,EAAOF,KAAKvC,QAC3B,IAAIkG,EAAQzD,EAAOlE,KAAK4H,MAAM,SAE9B,OADAzD,EAAG0D,YAAYzD,EAAMuD,EAAO,EAAG,GACxBvD,CACV,EAEDyD,YAAa,SAAUzD,EAAMuD,GAGzB,IAFA,IAAIG,EAAY,GACZhQ,EAAI,EACDA,EAAI6P,EAAM9S,QAAQ,CACrB,IAAIkT,EAAOJ,EAAM7P,GACb1B,EAAQ2R,EAAKC,MAAM,OAAO,GAAGnT,OAC7BkL,EAAQgI,EAAKE,OAAO7R,GAExB,GAAa,GAATA,GAAc0R,EAAUjT,OAAS,EAEjC,YADAwB,IAAIN,MAAM,oCAAsCgK,GAGpD,GAAI3J,EAAQ0R,EAAUjT,OAElB,YADAwB,IAAIN,MAAM,+BAAiCgK,GAI/C,IADA,IAAImI,EAAOJ,EAAUjT,OAASuB,EACvB8R,KACHJ,EAAUK,MAGd,GAAa,GAAT/R,GAAkC,GAApB0R,EAAUjT,OAAa,CACrC,IAAIwM,EAAO+C,EAAKM,SAASjM,EAAKqD,KAAKC,QAASgE,GAC5C+H,EAAU7F,KAAKZ,EACnB,KAAO,CACH,IAAIA,EAAO+C,EAAK/B,SACZyF,EAAU1R,EAAQ,GAClBqC,EAAKqD,KAAKC,QACVgE,EACA,CAAE,EACF,MAEJ+H,EAAU7F,KAAKZ,EACnB,CACAvJ,GACJ,CACAgQ,EAAUjT,OAAS,CACtB,EAEDyP,SAAU,SAAUF,GAChB,IAAID,EAAKN,EAAOxH,KACZrB,EAAO,CAAA,EACXA,EAAKgJ,KAAO,CACR9K,KAAMkL,EAAKlL,KACXsI,OAAQ4C,EAAK5C,OACbC,QAAS2C,EAAK3C,SAElBzG,EAAK6I,OAAS,OACd,IAAI8D,EAAQ,GAGZ,OAFAxD,EAAGiE,aAAaT,EAAO,CAACvD,EAAK1C,MAAO,GACpC1G,EAAKgF,KAAO2H,EAAMzB,KAAK,MAChBlL,CACV,EAEDoN,aAAc,SAAUT,EAAO/F,EAAOxL,GAClC,IACsBiS,EADlBC,EAAS,IAAIC,MAAMnS,EAAQ,GAAG8P,KAAK,KAAKsC,EAAAC,EAC3B7G,GAAK,IAAtB,IAAA4G,EAAAjM,MAAA8L,EAAAG,EAAAnR,KAAAqR,MAAwB,CAAA,IAAfrH,EAAIgH,EAAA7H,MACTmH,EAAM1F,KAAKqG,EAASjH,EAAKtB,OACnBsB,EAAKhB,UACPwD,EAAOxH,KAAK+L,aAAaT,EAAOtG,EAAKhB,SAAUjK,EAAQ,EAE/D,CAAC,CAAA,MAAAuS,GAAAH,EAAAnQ,EAAAsQ,EAAA,CAAA,QAAAH,EAAAI,GAAA,CACL,IC/gBKC,EAAY,WAGpB,OAAApS,GAFD,SAAAoS,EAAYC,GAAInS,OAAAkS,GACZlU,KAAKmU,GAAKA,CACd,GAAC,CAAA,CAAAvI,IAAA,OAAAC,MAED,WACItK,EAAON,MAAM,YACjB,GAAC,CAAA2K,IAAA,QAAAC,MACD,WACItK,EAAON,MAAM,aACjB,GAAC,CAAA2K,IAAA,OAAAC,MACD,SAAKuI,GACD,IAAI5E,EAAK,KACLC,EAAO,KAsBX,MAXU,eARFD,EAFiB,WAArBnM,EAAO+Q,GACDA,EAAUlF,OACPkF,EAAUlF,OAEV,YAGJ,YAILO,EAAOP,EAAOiB,WAAWb,SAAS8E,GACrB,aAAN5E,EACPC,EAAOP,EAAOC,UAAUG,SAAS8E,GACpB,YAAN5E,EACPC,EAAOP,EAAO4B,SAASxB,SAAS8E,GACnB,QAAN5E,EACPC,EAAOP,EAAOxH,KAAK4H,SAAS8E,GAE5B7S,EAAOJ,KAAK,sBAETsO,CACX,GAAC,CAAA7D,IAAA,WAAAC,MACD,SAASwI,GACL,IAAIhJ,EAAO,KAYX,MAXmB,cAAfgJ,EACAhJ,EAAO6D,EAAOiB,WAAWR,SAAS3P,KAAKmU,GAAG1E,MACpB,aAAf4E,EACPhJ,EAAO6D,EAAOC,UAAUQ,SAAS3P,KAAKmU,GAAG1E,MACnB,YAAf4E,EACPhJ,EAAO6D,EAAO4B,SAASnB,SAAS3P,KAAKmU,GAAG1E,MAClB,QAAf4E,EACPhJ,EAAO6D,EAAOxH,KAAKiI,SAAS3P,KAAKmU,GAAG1E,MAEpClO,EAAOH,MAAM,eAAiBiT,EAAc,WAEzChJ,CACX,IAAC,CAnDoB,GCMZiJ,EAAc,WAQtB,OAAAxS,GAPD,SAAAwS,EAAYH,EAAII,GAASvS,OAAAsS,GACrBtU,KAAKwU,KAAOD,EACZvU,KAAKmU,GAAKA,EACVnU,KAAKyU,OAA2B,QAAlBzU,KAAKwU,KAAKtM,KACxBlI,KAAK0U,OAAS,KAEd1U,KAAK2U,aAAc,CACvB,GAAC,CAAA,CAAA/I,IAAA,OAAAC,MACD,WACItK,EAAON,MAAM,cACjB,GAAC,CAAA2K,IAAA,QAAAC,MACD,WACItK,EAAON,MAAM,gBACbjB,KAAK0U,OAAS,CAAEhS,EAAG,EAAGkF,EAAG,EAAG7F,EAAG,EAAG2B,EAAG,EACzC,GAAC,CAAAkI,IAAA,iCAAAC,MACD,SAA+Ba,GAC3B,GAAI1M,KAAKyU,OACL,OAAOtU,EAAUG,MAKrB,IAHA,IAAIoL,EAAWgB,EAAKhB,UAAY,GAC5BkJ,EAAelJ,EAASxL,OACxB2U,EAAI,EACC1R,EAAI,EAAGA,EAAIyR,EAAczR,IAC1BuI,EAASvI,GAAGqI,YAAcrL,EAAUC,KACpCyU,IAEAA,IAGR,OAAOD,EAAe,GAAKC,EAAI,EAAI1U,EAAUC,KAAOD,EAAUG,KAClE,GAAC,CAAAsL,IAAA,SAAAC,MACD,WACItK,EAAON,MAAM,iBACbjB,KAAK8U,mBACL9U,KAAK+U,eACT,GAAC,CAAAnJ,IAAA,mBAAAC,MACD,WACI7L,KAAKgV,wBACT,GAAC,CAAApJ,IAAA,yBAAAC,MACD,WACI,IAAIa,EAAO1M,KAAKmU,GAAG1E,KAAK1C,KACpBkI,EAAc,KACd,WAAYvI,EAAKf,MACjBsJ,EAAcvI,EAAKf,MAAMvC,QAEzB6L,EAAc,CAAA,EACdvI,EAAKf,MAAMvC,OAAS6L,GAExB,IAAIvJ,EAAWgB,EAAKhB,SAChBwJ,EAAiBxJ,EAASxL,OAG9B,GAFA+U,EAAYzJ,UAAYrL,EAAUE,OAClC4U,EAAYE,WAAa,EACrBnV,KAAKyU,OAEL,IADA,IAAItR,EAAI+R,EACD/R,KACHnD,KAAKoV,uBAAuB1J,EAASvI,GAAIhD,EAAUG,MAAO6C,OAG9D,CAAIA,EAAI+R,EAER,IAFA,IACIG,EAAU,KACPlS,MACHkS,EAAU3J,EAASvI,IACPqI,WAAarL,EAAUC,KAC/BJ,KAAKoV,uBAAuBC,EAASlV,EAAUC,KAAM+C,GAErDnD,KAAKoV,uBAAuBC,EAASlV,EAAUG,MAAO6C,EAPxC,CAW9B,GAAC,CAAAyI,IAAA,yBAAAC,MACD,SAAuBa,EAAMlB,EAAW2J,GACpC,IAAIF,EAAc,KACd,WAAYvI,EAAKf,MACjBsJ,EAAcvI,EAAKf,MAAMvC,QAEzB6L,EAAc,CAAA,EACdvI,EAAKf,MAAMvC,OAAS6L,GAExB,IAAIvJ,EAAWgB,EAAKhB,SAChBwJ,EAAiBxJ,EAASxL,OAE9B+U,EAAYzJ,UAAYA,EACxByJ,EAAYE,WAAaA,EAEzB,IADA,IAAIhS,EAAI+R,EACD/R,KACHnD,KAAKoV,uBAAuB1J,EAASvI,GAAIqI,EAAWrI,EAE5D,GAAC,CAAAyI,IAAA,gBAAAC,MACD,WACI,IAAIa,EAAO1M,KAAKmU,GAAG1E,KAAK1C,KACpBkI,EAAcvI,EAAKf,MAAMvC,OAC7B6L,EAAYK,SAAW,EACvBL,EAAYM,SAAW,EACvBN,EAAYO,aAAe,EAM3B,IALA,IAAI9J,EAAWgB,EAAKhB,SAChBvI,EAAIuI,EAASxL,OACbuV,EAAa,GACbC,EAAc,GACdL,EAAU,KACPlS,MACHkS,EAAU3J,EAASvI,IACPwI,MAAMvC,OAAOoC,WAAarL,EAAUG,MAC5CoV,EAAYC,QAAQN,GAEpBI,EAAWE,QAAQN,GAG3BJ,EAAYQ,WAAaA,EACzBR,EAAYS,YAAcA,EAC1BT,EAAYW,kBAAoB5V,KAAK6V,wBAAwBJ,GAC7DR,EAAYa,mBAAqB9V,KAAK6V,wBAAwBH,GAC9D1V,KAAK0U,OAAOhR,EAAIgJ,EAAKf,MAAMvD,KAAK+D,MAAQ,EACxCnM,KAAK0U,OAAO3S,EAAI,EAAI/B,KAAK0U,OAAOhR,EAChC1D,KAAK0U,OAAOhS,EAAI,EAChB1C,KAAK0U,OAAO9M,EAAIL,KAAKyB,IAAIiM,EAAYW,kBAAmBX,EAAYa,mBACxE,GAAC,CAAAlK,IAAA,0BAAAC,MAED,SAAwBoB,GASpB,IARA,IAAI8I,EAAe,EACfC,EAAc/I,EAAM/M,OACpBiD,EAAI6S,EACJtJ,EAAO,KACPuJ,EAAoB,EACpBhB,EAAc,KACdiB,EAAS,EACTC,EAAK,KACFhT,KAEH8R,GADAvI,EAAOO,EAAM9J,IACMwI,MAAMvC,OACf,MAAN+M,IACAA,EAAKzJ,EAAKnB,OAAOI,OAGrBsK,EAAoBjW,KAAK6V,wBAAwBnJ,EAAKhB,UACjDgB,EAAKjB,WACNwK,EAAoB,EACpBjW,KAAKoW,YAAY1J,EAAKhB,UAAU,IAEpCuK,EAAoB1O,KAAKyB,IAAI0D,EAAKf,MAAMvD,KAAKgE,OAAQ6J,GACjDvJ,EAAKhB,SAASxL,OAAS,IACvB+V,GAAqBjW,KAAKwU,KAAKhL,cAGnCyL,EAAYO,aAAeS,EAC3BhB,EAAYM,SAAWW,EAASD,EAAoB,EACpDhB,EAAYK,SACRtV,KAAKwU,KAAKnL,OAAS4L,EAAYzJ,UAC9B2K,EAAG/N,KAAK+D,OAASgK,EAAG/M,OAAOoC,UAAYyJ,EAAYzJ,WAAc,EACjEkB,EAAKnB,OAAOD,SACb2J,EAAYK,UAAYtV,KAAKwU,KAAKjL,OAAS0L,EAAYzJ,WAG3D0K,EAASA,EAASD,EAAoBjW,KAAKwU,KAAKlL,OAChDyM,GAAgBE,EAEhBD,EAAc,IACdD,GAAgB/V,KAAKwU,KAAKlL,QAAU0M,EAAc,IAEtD7S,EAAI6S,EAEJ,IADA,IAAIK,EAAgBN,EAAe,EAC5B5S,MACHuJ,EAAOO,EAAM9J,IACRwI,MAAMvC,OAAOmM,UAAYc,EAElC,OAAON,CACX,GAAC,CAAAnK,IAAA,iCAAAC,MAED,SAA+BoB,GAS3B,IARA,IAAI8I,EAAe,EACfC,EAAc/I,EAAM/M,OACpBiD,EAAI6S,EACJtJ,EAAO,KACPuJ,EAAoB,EACpBhB,EAAc,KACdiB,EAAS,EACTC,EAAK,KACFhT,KAEH8R,GADAvI,EAAOO,EAAM9J,IACMwI,MAAMvC,OACf,MAAN+M,IACAA,EAAKzJ,EAAKnB,OAAOI,OAGrBsK,EAAoBjW,KAAKsW,+BAA+B5J,EAAKhB,UACxDgB,EAAKjB,WACNwK,EAAoB,GAExBA,EAAoB1O,KAAKyB,IAAI0D,EAAKf,MAAMvD,KAAKgE,OAAQ6J,GACjDvJ,EAAKhB,SAASxL,OAAS,IACvB+V,GAAqBjW,KAAKwU,KAAKhL,cAGnCyL,EAAYO,aAAeS,EAC3BhB,EAAYM,SAAWW,EAASD,EAAoB,EACpDC,EAASA,EAASD,EAAoBjW,KAAKwU,KAAKlL,OAChDyM,GAAgBE,EAEhBD,EAAc,IACdD,GAAgB/V,KAAKwU,KAAKlL,QAAU0M,EAAc,IAEtD7S,EAAI6S,EAEJ,IADA,IAAIK,EAAgBN,EAAe,EAC5B5S,MACHuJ,EAAOO,EAAM9J,IACRwI,MAAMvC,OAAOmM,UAAYc,EAElC,OAAON,CACX,GAAC,CAAAnK,IAAA,kBAAAC,MACD,SAAgBa,GACZ,IAAIuI,EAAcvI,EAAKf,MAAMvC,OACzBmN,EAAe,KAOnB,GANI,aAActB,GAAejV,KAAK2U,YAClC4B,EAAetB,EAAYuB,UAE3BD,EAAe,CAAExK,GAAI,EAAGE,GAAI,GAC5BgJ,EAAYuB,SAAWD,IAEJ,GAAnBA,EAAaxK,IAA8B,GAAnBwK,EAAatK,EAAS,CAC9C,IAAIF,EAAIkJ,EAAYK,SAChBrJ,EAAIgJ,EAAYM,SACpB,IAAK7I,EAAKpB,OAAQ,CACd,IAAImL,EAAWzW,KAAK0W,gBAAgBhK,EAAKnB,QACzCQ,GAAK0K,EAAS1K,EACdE,GAAKwK,EAASxK,CAClB,CACAsK,EAAaxK,EAAIA,EACjBwK,EAAatK,EAAIA,CACrB,CACA,OAAOsK,CACX,GAAC,CAAA3K,IAAA,iBAAAC,MACD,SAAea,GACX,IAAIiK,EAAYjK,EAAKf,MAAMvD,KACvBqO,EAAWzW,KAAK0W,gBAAgBhK,GAChC3M,EAAI,CAAA,EAGR,OAFAA,EAAEgM,EAAI0K,EAAS1K,EAAK4K,EAAUxK,OAASO,EAAKf,MAAMvC,OAAOoC,UAAY,GAAM,EAC3EzL,EAAEkM,EAAIwK,EAASxK,EAAI0K,EAAUvK,OAAS,EAC/BrM,CACX,GAAC,CAAA6L,IAAA,oBAAAC,MACD,SAAkBa,GAEd,OADQ1M,KAAK0W,gBAAgBhK,EAEjC,GAAC,CAAAd,IAAA,qBAAAC,MACD,SAAmBa,GACf,IAAIuI,EAAcvI,EAAKf,MAAMvC,OACzBwN,EAAa,KAOjB,GANI,WAAY3B,GAAejV,KAAK2U,YAChCiC,EAAa3B,EAAY4B,QAEzBD,EAAa,CAAE7K,GAAI,EAAGE,GAAI,GAC1BgJ,EAAY4B,OAASD,IAEJ,GAAjBA,EAAW7K,IAA4B,GAAjB6K,EAAW3K,EACjC,GAAIS,EAAKpB,OACLsL,EAAW7K,EAAI,EACf6K,EAAW3K,EAAI,MACZ,CACH,IAAI0K,EAAYjK,EAAKf,MAAMvD,KACvBqO,EAAWzW,KAAK0W,gBAAgBhK,GACpCkK,EAAW7K,EACP0K,EAAS1K,GAAK4K,EAAUxK,MAAQnM,KAAKwU,KAAKjL,QAAUmD,EAAKf,MAAMvC,OAAOoC,UAC1EoL,EAAW3K,EAAIwK,EAASxK,CAC5B,CAEJ,OAAO2K,CACX,GAAC,CAAAhL,IAAA,qBAAAC,MACD,SAAmBa,GACf,IAAI3M,EAAIC,KAAK8W,mBAAmBpK,GAC5BqK,EAAO,CAAA,EAOX,OANIrK,EAAKf,MAAMvC,OAAOoC,WAAarL,EAAUG,MACzCyW,EAAKhL,EAAIhM,EAAEgM,EAAI/L,KAAKwU,KAAKjL,OAEzBwN,EAAKhL,EAAIhM,EAAEgM,EAEfgL,EAAK9K,EAAIlM,EAAEkM,EAAI1E,KAAKyP,KAAKhX,KAAKwU,KAAKjL,OAAS,GACrCwN,CACX,GAAC,CAAAnL,IAAA,eAAAC,MACD,WACI,IAAIoB,EAAQjN,KAAKmU,GAAG1E,KAAKxC,MACrBP,EAAO,KACPuK,EAAO,KACX,IAAK,IAAI/J,KAAWD,EAChBP,EAAOO,EAAMC,IACb+J,EAAOjX,KAAK8W,mBAAmBpK,IACtBX,EAAI/L,KAAK0U,OAAOhR,IACrB1D,KAAK0U,OAAOhR,EAAIuT,EAAKlL,GAErBkL,EAAKlL,EAAI/L,KAAK0U,OAAO3S,IACrB/B,KAAK0U,OAAO3S,EAAIkV,EAAKlL,GAG7B,MAAO,CACHhK,EAAG/B,KAAK0U,OAAOhR,EAAI1D,KAAK0U,OAAO3S,EAC/BiB,EAAGhD,KAAK0U,OAAO9M,EAAI5H,KAAK0U,OAAOhS,EAEvC,GAAC,CAAAkJ,IAAA,cAAAC,MACD,SAAYa,GACJA,EAAKpB,SAGLoB,EAAKjB,SACLzL,KAAKkX,cAAcxK,GAEnB1M,KAAKmX,YAAYzK,GAEzB,GAAC,CAAAd,IAAA,cAAAC,MACD,SAAYa,GACRA,EAAKjB,UAAW,EAChBzL,KAAKoX,YAAY1K,GACjB1M,KAAKoW,YAAY1J,EAAKhB,UAAU,GAChC1L,KAAKmU,GAAGkD,oBAAoB1W,EAAUC,KAAM,CACxCmF,IAAK,cACLsF,KAAM,GACNqB,KAAMA,EAAKtK,IAEnB,GAAC,CAAAwJ,IAAA,gBAAAC,MACD,SAAca,GACVA,EAAKjB,UAAW,EAChBzL,KAAKoX,YAAY1K,GACjB1M,KAAKoW,YAAY1J,EAAKhB,UAAU,GAChC1L,KAAKmU,GAAGkD,oBAAoB1W,EAAUC,KAAM,CACxCmF,IAAK,gBACLsF,KAAM,GACNqB,KAAMA,EAAKtK,IAEnB,GAAC,CAAAwJ,IAAA,aAAAC,MACD,WACI,IAEIa,EAFAO,EAAQjN,KAAKmU,GAAG1E,KAAKxC,MACrB3K,EAAI,EAER,IAAK,IAAI4K,KAAWD,GAChBP,EAAOO,EAAMC,IACHzB,WACNiB,EAAKjB,UAAW,EAChBnJ,KAGR,GAAIA,EAAI,EAAG,CACP,IAAIyK,EAAO/M,KAAKmU,GAAG1E,KAAK1C,KACxB/M,KAAKoX,YAAYrK,GACjB/M,KAAKoW,YAAYrJ,EAAKrB,UAAU,EACpC,CACJ,GAAC,CAAAE,IAAA,eAAAC,MACD,WACI,IAEIa,EAFAO,EAAQjN,KAAKmU,GAAG1E,KAAKxC,MACrB3K,EAAI,EAER,IAAK,IAAI4K,KAAWD,GAChBP,EAAOO,EAAMC,IACJzB,WAAaiB,EAAKpB,SACvBoB,EAAKjB,UAAW,EAChBnJ,KAGR,GAAIA,EAAI,EAAG,CACP,IAAIyK,EAAO/M,KAAKmU,GAAG1E,KAAK1C,KACxB/M,KAAKoX,YAAYrK,GACjB/M,KAAKoW,YAAYrJ,EAAKrB,UAAU,EACpC,CACJ,GAAC,CAAAE,IAAA,kBAAAC,MACD,SAAgByL,EAAcC,EAAYC,GACtC,KAAIF,EAAe,GAOnB,IAJA,IAAIrK,EAAQsK,GAAcvX,KAAKmU,GAAG1E,KAAK1C,KAAKrB,SACxC+L,EAAQD,GAAc,EACtBrU,EAAI8J,EAAM/M,OACVwM,EAAO,KACJvJ,KACHuJ,EAAOO,EAAM9J,GACTsU,EAAQH,IACH5K,EAAKjB,UACNzL,KAAKmX,YAAYzK,GAErB1M,KAAK0X,gBAAgBJ,EAAc5K,EAAKhB,SAAU+L,EAAQ,IAE1DA,GAASH,GACL5K,EAAKjB,UACLzL,KAAKkX,cAAcxK,EAInC,GAAC,CAAAd,IAAA,cAAAC,MACD,SAAYa,GACR,IAAIK,EAAO/M,KAAKmU,GAAG1E,KAAK1C,KACxB,GAAMA,EAAM,CACR,IAAI4K,EAAmB5K,EAAKpB,MAAMvC,OAC9BsD,EAAKpB,QACLqM,EAAiB7B,mBAAqB9V,KAAKsW,+BACvCqB,EAAiBjC,aAErBiC,EAAiB/B,kBAAoB5V,KAAKsW,+BACtCqB,EAAiBlC,aAGjB/I,EAAKf,MAAMvC,OAAOoC,WAAarL,EAAUG,MACzCqX,EAAiB7B,mBAAqB9V,KAAKsW,+BACvCqB,EAAiBjC,aAGrBiC,EAAiB/B,kBAAoB5V,KAAKsW,+BACtCqB,EAAiBlC,YAI7BzV,KAAK0U,OAAO9M,EAAIL,KAAKyB,IACjB2O,EAAiB/B,kBACjB+B,EAAiB7B,oBAErB9V,KAAK2U,aAAc,CACvB,MACIpT,EAAOJ,KAAK,0BAEpB,GAAC,CAAAyK,IAAA,cAAAC,MACD,SAAYoB,EAAO2K,GAIf,IAHA,IAAIzU,EAAI8J,EAAM/M,OACVwM,EAAO,KAEJvJ,MACHuJ,EAAOO,EAAM9J,IACMwI,MAAMvC,OACrBsD,EAAKjB,SACLzL,KAAKoW,YAAY1J,EAAKhB,SAAUkM,GAEhC5X,KAAKoW,YAAY1J,EAAKhB,UAAU,GAE/BgB,EAAKpB,SACNoB,EAAKf,MAAMvC,OAAOwO,QAAUA,EAGxC,GAAC,CAAAhM,IAAA,YAAAC,MACD,SAAUa,GACN,OAAOA,EAAKjB,QAChB,GAAC,CAAAG,IAAA,aAAAC,MACD,SAAWa,GACP,IAAIuI,EAAcvI,EAAKf,MAAMvC,OAC7B,QAAI,YAAa6L,IAAgBA,EAAY2C,QAKjD,IAAC,CAzbsB,GCPrBC,EAAQ,WACV,SAAAA,EAAYzP,GAAMpG,OAAA6V,GACd7X,KAAKoI,KAAOA,EACZpI,KAAKwU,KAAOpM,EAAKoM,KACjBxU,KAAK8X,MAAQD,EAASvV,EAAE,OACxBtC,KAAK8X,MAAMC,aAAa,QAAS,UACjC/X,KAAKgY,KAAO,CAAEjW,EAAG,EAAGiB,EAAG,GACvBhD,KAAKgT,MAAQ,GACbhT,KAAKiY,aAAe,CAChBC,SAAUlY,KAAKmY,SACfC,OAAQpY,KAAKqY,YAEjBrY,KAAKsY,QAAUtY,KAAKiY,aAAajY,KAAKwU,KAAK9L,aAAe1I,KAAKiY,aAAaG,MAChF,CAAC,OAAAtW,EAAA+V,EAAA,CAAA,CAAAjM,IAAA,UAAAC,MAID,WACI,OAAO7L,KAAK8X,KAChB,GAAC,CAAAlM,IAAA,WAAAC,MACD,SAAS9J,EAAGiB,GACRhD,KAAKgY,KAAKjW,EAAIA,EACd/B,KAAKgY,KAAKhV,EAAIA,EACdhD,KAAK8X,MAAMC,aAAa,QAAShW,GACjC/B,KAAK8X,MAAMC,aAAa,SAAU/U,EACtC,GAAC,CAAA4I,IAAA,QAAAC,MACD,WAEI,IADA,IAAIsC,EAAMnO,KAAKgT,MAAM9S,OACdiO,KACHnO,KAAK8X,MAAM3R,YAAYnG,KAAKgT,MAAM7E,IAEtCnO,KAAKgT,MAAM9S,OAAS,CACxB,GAAC,CAAA0L,IAAA,YAAAC,MACD,SAAUoL,EAAMsB,EAAKC,EAAQC,GACzB,IAAIrF,EAAOyE,EAASvV,EAAE,QACtB8Q,EAAK2E,aAAa,SAAUU,GAASzY,KAAKwU,KAAK/L,YAC/C2K,EAAK2E,aAAa,eAAgB/X,KAAKwU,KAAKhM,YAC5C4K,EAAK2E,aAAa,OAAQ,eAC1B/X,KAAKgT,MAAM1F,KAAK8F,GAChBpT,KAAK8X,MAAMhV,YAAYsQ,GACvBpT,KAAKsY,QACDlF,EACAmF,EAAIxM,EAAIyM,EAAOzM,EACfwM,EAAItM,EAAIuM,EAAOvM,EACfgL,EAAKlL,EAAIyM,EAAOzM,EAChBkL,EAAKhL,EAAIuM,EAAOvM,EAExB,GAAC,CAAAL,IAAA,UAAAC,MAED,SAAQ6M,EAAiBC,GACrB,IAAIC,EAAM,IAAIC,MACdD,EAAIvU,OAAS,WACTqU,EAAgBI,UAAUF,EAAK,EAAG,GAChCD,GAAYA,KAElBC,EAAIG,IACA,6BAA+BC,MAAK,IAAIC,eAAgBC,kBAAkBlZ,KAAK8X,OACvF,GAAC,CAAAlM,IAAA,aAAAC,MACD,SAAWsN,EAAMC,EAAIC,EAAIC,EAAIC,GACzBJ,EAAKpB,aACD,IACA,KACIqB,EACA,IACAC,EACA,OACCD,EAAkB,GAAXE,EAAKF,GAAW,GACxB,IACAC,EACA,KACAD,EACA,IACAG,EACA,KACAD,EACA,IACAC,EAEZ,GAAC,CAAA3N,IAAA,WAAAC,MACD,SAASsN,EAAMC,EAAIC,EAAIC,EAAIC,GACvBJ,EAAKpB,aAAa,IAAK,KAAOqB,EAAK,IAAMC,EAAK,MAAQC,EAAK,IAAMC,EACrE,IAAC,CAAA,CAAA3N,IAAA,IAAAC,MAnED,SAAStJ,GACL,OAAOV,EAAEI,EAAEuX,gBAAgB,6BAA8BjX,EAC7D,IAAC,CAhBS,GAoFRkX,EAAW,WAYZ,OAAA3X,GAXD,SAAA2X,EAAYrR,GAAMpG,OAAAyX,GACdzZ,KAAKwU,KAAOpM,EAAKoM,KACjBxU,KAAK0Z,SAAW7X,EAAES,EAAE,UACpBtC,KAAK0Z,SAASC,UAAY,SAC1B3Z,KAAK4Z,WAAa5Z,KAAK0Z,SAASG,WAAW,MAC3C7Z,KAAKgY,KAAO,CAAEjW,EAAG,EAAGiB,EAAG,GACvBhD,KAAKiY,aAAe,CAChBC,SAAUlY,KAAKmY,SACfC,OAAQpY,KAAKqY,YAEjBrY,KAAKsY,QAAUtY,KAAKiY,aAAajY,KAAKwU,KAAK9L,aAAe1I,KAAKiY,aAAaG,MAChF,GAAC,CAAA,CAAAxM,IAAA,UAAAC,MACD,WACI,OAAO7L,KAAK0Z,QAChB,GAAC,CAAA9N,IAAA,WAAAC,MACD,SAAS9J,EAAGiB,GACRhD,KAAKgY,KAAKjW,EAAIA,EACd/B,KAAKgY,KAAKhV,EAAIA,EACdhD,KAAK0Z,SAASvN,MAAQpK,EACtB/B,KAAK0Z,SAAStN,OAASpJ,CAC3B,GAAC,CAAA4I,IAAA,QAAAC,MACD,WACI7L,KAAK4Z,WAAWE,UAAU,EAAG,EAAG9Z,KAAKgY,KAAKjW,EAAG/B,KAAKgY,KAAKhV,EAC3D,GAAC,CAAA4I,IAAA,YAAAC,MACD,SAAUoL,EAAMsB,EAAKC,EAAQC,GACzB,IAAIsB,EAAM/Z,KAAK4Z,WACfG,EAAIC,YAAcvB,GAASzY,KAAKwU,KAAK/L,WACrCsR,EAAIE,UAAYja,KAAKwU,KAAKhM,WAC1BuR,EAAIG,QAAU,QACdla,KAAKsY,QAAQyB,EAAKxB,EAAIxM,EAAIyM,EAAOzM,EAAGwM,EAAItM,EAAIuM,EAAOvM,EAAGgL,EAAKlL,EAAIyM,EAAOzM,EAAGkL,EAAKhL,EAAIuM,EAAOvM,EAC7F,GAAC,CAAAL,IAAA,UAAAC,MACD,SAAQ6M,EAAiBC,GACrBD,EAAgBI,UAAU9Y,KAAK0Z,SAAU,EAAG,GAC1Cf,GAAYA,GAClB,GAAC,CAAA/M,IAAA,aAAAC,MACD,SAAWkO,EAAKX,EAAIC,EAAIC,EAAIC,GACxBQ,EAAII,YACJJ,EAAIK,OAAOhB,EAAIC,GACfU,EAAIM,cAAcjB,EAAkB,GAAXE,EAAKF,GAAW,EAAGC,EAAID,EAAIG,EAAID,EAAIC,GAC5DQ,EAAIO,QACR,GAAC,CAAA1O,IAAA,WAAAC,MACD,SAASkO,EAAKX,EAAIC,EAAIC,EAAIC,GACtBQ,EAAII,YACJJ,EAAIK,OAAOhB,EAAIC,GACfU,EAAIQ,OAAOjB,EAAIC,GACfQ,EAAIO,QACR,IAAC,CA/CY,GCjFjB,IAAaE,EAAY,WAoBpB,OAAA1Y,GAnBD,SAAA0Y,EAAYrG,EAAII,GAASvS,OAAAwY,GACrBxa,KAAKwU,KAAOD,EACZvU,KAAKmU,GAAKA,EACVnU,KAAKoJ,OAAS+K,EAAG/K,OAEjBpJ,KAAK+H,UAAY,KACjB/H,KAAKya,QAAU,KACfza,KAAK0a,QAAU,KAEf1a,KAAKgY,KAAO,CAAEjW,EAAG,EAAGiB,EAAG,GAEvBhD,KAAK2a,cAAgB,KACrB3a,KAAK4a,aAAe,KAEpB5a,KAAK6a,MAAQ,KACb7a,KAAK8a,YAAgBvG,EAAQrL,mBACvBlJ,KAAK+a,oBACL/a,KAAKgb,qBACXhb,KAAKib,cAAe,CACxB,GAAC,CAAA,CAAArP,IAAA,OAAAC,MACD,WAAO,IAAAqP,EAAAlb,KAKH,GAJAuB,EAAON,MAAMjB,KAAKwU,MAClBjT,EAAON,MAAM,aAEbjB,KAAK+H,UAAYlG,EAAEsB,EAAEnD,KAAKwU,KAAKzM,WAAa/H,KAAKwU,KAAKzM,UAAYlG,EAAEM,EAAEnC,KAAKwU,KAAKzM,WAC3E/H,KAAK+H,UAAV,CDyGD,IAAoBK,ECrGnBpI,KAAK6a,ODqGczS,ECrGKpI,KDsGI,QCtGEA,KAAKwU,KAAKnM,ODsG9B3H,cAA0B,IAAImX,EAASzP,GAAQ,IAAIqR,EAAYrR,ICpGzEpI,KAAKya,QAAU5Y,EAAES,EAAE,OACnBtC,KAAK0a,QAAU7Y,EAAES,EAAE,WACnBtC,KAAKmb,SAAWtZ,EAAES,EAAE,SACpBtC,KAAKya,QAAQd,UAAY,gCAAkC3Z,KAAKwU,KAAK3L,cACrE7I,KAAKya,QAAQW,SAAW,EACxBpb,KAAKya,QAAQ3X,YAAY9C,KAAK6a,MAAMQ,WACpCrb,KAAKya,QAAQ3X,YAAY9C,KAAK0a,SAE9B1a,KAAKmb,SAASxB,UAAY,gBAC1B3Z,KAAKmb,SAASzW,KAAO,OAErB1E,KAAKsb,aAAe,EAEpB,IAAIC,EAAIvb,KACR6B,EAAE4B,GAAGzD,KAAKmb,SAAU,WAAW,SAAUzX,GACrC,IAAIqC,EAAMrC,GAAK8X,MACI,IAAfzV,EAAI0V,UACJF,EAAEG,gBACF3V,EAAI4V,kBAEZ,IACA9Z,EAAE4B,GAAGzD,KAAKmb,SAAU,QAAQ,SAAUzX,GAClC6X,EAAEG,eACN,IAEA1b,KAAK+H,UAAUjF,YAAY9C,KAAKya,SAE3Bza,KAAK+H,UAAU6T,cAChB,IAAIC,qBAAqB,SAACC,EAAUC,GAAaC,OAAAd,GACzCY,EAAS,GAAGG,iBACZF,EAASG,UAAUlc,KAAKya,SACxBza,KAAKa,WAEZsb,KAACnc,OAACoc,QAAQpc,KAAKya,QApCpB,MAFIlZ,EAAOH,MAAM,qDAwCrB,GAAC,CAAAwK,IAAA,YAAAC,MAED,SAAUwQ,EAAKC,EAAYC,EAAcC,GACrC,IAAIC,EAAWD,EAAmBxc,KAAKya,QAAUza,KAAK0a,QACtD7Y,EAAE4B,GAAGgZ,EAAQH,GAAY,SAAU5Y,GAC/B,IAAIqC,EAAMrC,GAAK8X,MACfe,EAAarV,KAAKmV,EAAKtW,EAC3B,GACJ,GAAC,CAAA6F,IAAA,oBAAAC,MACD,SAAkBwP,GACd,GAAe,MAAXA,EACA,OAAO,KAEX,IAAIvJ,EAAUuJ,EAAQvJ,QAAQpR,cAC9B,MAAe,UAAXoR,GAAkC,cAAXA,EAChBuJ,EAAQpJ,aAAa,UACV,WAAXH,GAAmC,QAAXA,GAAgC,QAAXA,EAC7C,KAEA9R,KAAK0c,kBAAkBrB,EAAQsB,cAE9C,GAAC,CAAA/Q,IAAA,UAAAC,MACD,SAAQwP,GACJ,GAAe,MAAXA,EACA,OAAO,EAEX,IAAIvJ,EAAUuJ,EAAQvJ,QAAQpR,cAC9B,MAAe,UAAXoR,GAEkB,WAAXA,GAAmC,QAAXA,GAAgC,QAAXA,GAG7C9R,KAAKqN,QAAQgO,EAAQsB,cAEpC,GAAC,CAAA/Q,IAAA,cAAAC,MACD,SAAYwP,GACR,MAAwC,cAAjCA,EAAQvJ,QAAQpR,aAC3B,GAAC,CAAAkL,IAAA,QAAAC,MACD,WACItK,EAAON,MAAM,cACbjB,KAAK2a,cAAgB,KACrB3a,KAAK4c,cACL5c,KAAK6c,cACL7c,KAAK8c,aACT,GAAC,CAAAlR,IAAA,cAAAC,MACD,WACI,IAAIkR,EAAa/c,KAAKmU,GAAGI,QAAQtM,MAE7BjI,KAAK0a,QAAQf,UADXoD,EACuB,SAAWA,EAEX,EAEjC,GAAC,CAAAnR,IAAA,qBAAAC,MACD,WACI,IAAIoB,EAAQjN,KAAKmU,GAAG1E,KAAKxC,MACzB,IAAK,IAAI+P,KAAU/P,EACfjN,KAAKid,wBAAwBhQ,EAAM+P,GAE3C,GAAC,CAAApR,IAAA,OAAAC,MACD,WACItK,EAAON,MAAM,aACbjB,KAAKkd,uBAAuBld,KAAKwU,KAAK7L,WACtC3I,KAAKmd,aACLnd,KAAKib,cAAe,CACxB,GAAC,CAAArP,IAAA,cAAAC,MACD,WACI,IAAIuR,EAAWpd,KAAKoJ,OAAOiU,eACvBC,EAAYF,EAASrb,EAAwB,EAApB/B,KAAKwU,KAAKlM,QACnCiV,EAAaH,EAASpa,EAAwB,EAApBhD,KAAKwU,KAAKjM,QACpCiV,EAAWxd,KAAKya,QAAQgD,YACxBC,EAAW1d,KAAKya,QAAQkD,aACxBH,EAAWF,IACXE,EAAWF,GAEXI,EAAWH,IACXG,EAAWH,GAEfvd,KAAKgY,KAAKjW,EAAIyb,EACdxd,KAAKgY,KAAKhV,EAAI0a,CAClB,GAAC,CAAA9R,IAAA,kBAAAC,MACD,SAAgBa,GACZ,IAAIiK,EAAYjK,EAAKf,MAAMvD,KAC3BuO,EAAUxK,MAAQwK,EAAU0E,QAAQoC,YACpC9G,EAAUvK,OAASuK,EAAU0E,QAAQsC,YACzC,GAAC,CAAA/R,IAAA,aAAAC,MACD,WAAa,IAAA+R,EAAA5d,KACLiN,EAAQjN,KAAKmU,GAAG1E,KAAKxC,MACrB4Q,EAAWhc,EAAEI,EAAE6b,yBACnB,IAAK,IAAId,KAAU/P,EACfjN,KAAK+d,oBAAoB9Q,EAAM+P,GAASa,GAE5C7d,KAAK0a,QAAQ5X,YAAY+a,GAEzB7d,KAAKge,2BAA2B,WAC5B,IAAK,IAAIhB,KADyBhB,OAAA4B,GACf3Q,EACfjN,KAAKie,gBAAgBhR,EAAM+P,KAElCb,KAACnc,MACN,GAAC,CAAA4L,IAAA,WAAAC,MACD,SAASa,GAAM,IAAAwR,EAAAle,KACXA,KAAK+d,oBAAoBrR,EAAM1M,KAAK0a,SACpC1a,KAAKge,2BAA2B,WAAMhC,OAAAkC,GAClCle,KAAKie,gBAAgBvR,IACxByP,KAACnc,MACN,GAAC,CAAA4L,IAAA,6BAAAC,MACD,SAA2BsS,GACjBne,KAAK+H,UAAU6T,aACjBuC,KAGJ5c,EAAOJ,KACH,oGAEJnB,KAAKya,QAAQlX,MAAM6a,SAAW,WAC9Bpe,KAAKya,QAAQlX,MAAM8a,IAAM,UACzBxc,EAAEI,EAAE6D,KAAKhD,YAAY9C,KAAKya,SAC1B0D,IACAne,KAAK+H,UAAUjF,YAAY9C,KAAKya,SAChCza,KAAKya,QAAQlX,MAAM6a,SAAW,KAC9Bpe,KAAKya,QAAQlX,MAAM8a,IAAM,KAC7B,GAAC,CAAAzS,IAAA,sBAAAC,MACD,SAAoBa,EAAMD,GACtB,IAAIkK,EAAY,KACZ,SAAUjK,EAAKf,MACfgL,EAAYjK,EAAKf,MAAMvD,MAEvBuO,EAAY,CAAA,EACZjK,EAAKf,MAAMvD,KAAOuO,GAGtB,IAAI1U,EAAIJ,EAAES,EAAE,UACZ,GAAIoK,EAAKpB,OACLrJ,EAAE0X,UAAY,WACX,CACH,IAAI2E,EAAMzc,EAAES,EAAE,cACdT,EAAEY,EAAE6b,EAAK,KACTA,EAAIvG,aAAa,SAAUrL,EAAKtK,IAChCkc,EAAI/a,MAAMoC,WAAa,SACvB8G,EAAY3J,YAAYwb,GACxB3H,EAAU4H,SAAWD,CACzB,CACM5R,EAAKtB,OACPpL,KAAK8a,YAAY7Y,EAAGyK,GAExBzK,EAAE8V,aAAa,SAAUrL,EAAKtK,IAC9BH,EAAEsB,MAAMoC,WAAa,SACrB3F,KAAKwe,yBAAyBvc,EAAGyK,EAAKrB,MAEtCoB,EAAY3J,YAAYb,GACxB0U,EAAU0E,QAAUpZ,CACxB,GAAC,CAAA2J,IAAA,cAAAC,MACD,SAAYa,GACkB,MAAtB1M,KAAK2a,eAAyB3a,KAAK2a,cAAcvY,IAAMsK,EAAKtK,KAC5DpC,KAAK2a,cAAgB,MAEA,MAArB3a,KAAK4a,cAAwB5a,KAAK4a,aAAaxY,IAAMsK,EAAKtK,KAC1DsK,EAAKf,MAAMvD,KAAKiT,QAAQlV,YAAYnG,KAAKmb,UACzCnb,KAAK4a,aAAe,MAIxB,IAFA,IAAIlP,EAAWgB,EAAKhB,SAChBvI,EAAIuI,EAASxL,OACViD,KACHnD,KAAK4O,YAAYlD,EAASvI,IAE9B,GAAIuJ,EAAKf,MAAMvD,KAAM,CACjB,IAAIiT,EAAU3O,EAAKf,MAAMvD,KAAKiT,QAC1BkD,EAAW7R,EAAKf,MAAMvD,KAAKmW,SAC/Bve,KAAK0a,QAAQvU,YAAYkV,GACzBrb,KAAK0a,QAAQvU,YAAYoY,GACzB7R,EAAKf,MAAMvD,KAAKiT,QAAU,KAC1B3O,EAAKf,MAAMvD,KAAKmW,SAAW,IAC/B,CACJ,GAAC,CAAA3S,IAAA,cAAAC,MACD,SAAYa,GACR,IAAIiK,EAAYjK,EAAKf,MAAMvD,KACvBiT,EAAU1E,EAAU0E,QAIxB,GAHM3O,EAAKtB,OACPpL,KAAK8a,YAAYO,EAAS3O,GAE1B1M,KAAKoJ,OAAOqV,WAAW/R,GACvBiK,EAAUxK,MAAQkP,EAAQoC,YAC1B9G,EAAUvK,OAASiP,EAAQsC,iBACxB,CACH,IAAIe,EAAerD,EAAQpJ,aAAa,SACxCoJ,EAAQ9X,MAAQ,sCAChBoT,EAAUxK,MAAQkP,EAAQoC,YAC1B9G,EAAUvK,OAASiP,EAAQsC,aAC3BtC,EAAQ9X,MAAQmb,CACpB,CACJ,GAAC,CAAA9S,IAAA,cAAAC,MACD,SAAYa,GACR,GAAM1M,KAAK2a,cAAe,CACtB,IAAIU,EAAUrb,KAAK2a,cAAchP,MAAMvD,KAAKiT,QAC5CA,EAAQ1B,UAAY0B,EAAQ1B,UAAU9R,QAAQ,iBAAkB,IAChE7H,KAAK2e,mCAAmC3e,KAAK2a,cACjD,CACMjO,IACF1M,KAAK2a,cAAgBjO,EACrBA,EAAKf,MAAMvD,KAAKiT,QAAQ1B,WAAa,YACrC3Z,KAAK4e,iCAAiClS,GAE9C,GAAC,CAAAd,IAAA,eAAAC,MACD,WACI7L,KAAK6e,YAAY,KACrB,GAAC,CAAAjT,IAAA,mBAAAC,MACD,WACI,OAAO7L,KAAK4a,YAChB,GAAC,CAAAhP,IAAA,aAAAC,MACD,WACI,QAAS7L,KAAK4a,YAClB,GAAC,CAAAhP,IAAA,kBAAAC,MACD,SAAgBa,GACZ,GAAKA,EAAKtB,MAAV,CAIyB,MAArBpL,KAAK4a,cACL5a,KAAK0b,gBAET1b,KAAK4a,aAAelO,EACpB,IACI2O,EADY3O,EAAKf,MAAMvD,KACHiT,QACpBjQ,EAAQsB,EAAKtB,MACb0T,EAAMC,iBAAiB1D,GAC3Brb,KAAKmb,SAAStP,MAAQT,EACtBpL,KAAKmb,SAAS5X,MAAM4I,MAChBkP,EAAQoC,YACRhd,SAASqe,EAAIE,iBAAiB,iBAC9Bve,SAASqe,EAAIE,iBAAiB,kBAC9B,KACJ3D,EAAQnY,UAAY,GACpBmY,EAAQvY,YAAY9C,KAAKmb,UACzBE,EAAQ9X,MAAM0b,OAAS,EACvBjf,KAAKmb,SAAS+D,QACdlf,KAAKmb,SAASpa,QAnBd,MAFIQ,EAAOJ,KAAK,yBAsBpB,GAAC,CAAAyK,IAAA,gBAAAC,MACD,WACI,GAAyB,MAArB7L,KAAK4a,aAAsB,CAC3B,IAAIlO,EAAO1M,KAAK4a,aAChB5a,KAAK4a,aAAe,KACpB,IACIS,EADY3O,EAAKf,MAAMvD,KACHiT,QACpBjQ,EAAQpL,KAAKmb,SAAStP,MAC1BwP,EAAQ9X,MAAM0b,OAAS,OACvB5D,EAAQlV,YAAYnG,KAAKmb,UACrBrX,EAAK4D,KAAKC,SAASyD,IAAUsB,EAAKtB,QAAUA,EAC5CpL,KAAK8a,YAAYO,EAAS3O,GAE1B1M,KAAKmU,GAAGgL,YAAYzS,EAAKtK,GAAIgJ,EAErC,CACApL,KAAKya,QAAQyE,OACjB,GAAC,CAAAtT,IAAA,kBAAAC,MACD,WACI,IAAI6I,EAAS1U,KAAKoJ,OAAOsL,OACrB0K,EAAWpf,KAAKmU,GAAGkL,WAEnBC,EAASF,EAASzT,MAAMvD,KAAKmX,SAC7BC,GAAMxf,KAAKgY,KAAKjW,EAAI2S,EAAOhR,EAAIgR,EAAO3S,GAAK,EAC3C0d,EAAKzf,KAAKgY,KAAKhV,EAAI,EACvB,MAAO,CAAE+I,EAAGuT,EAASE,EAAKJ,EAASzT,MAAMvD,KAAK+D,MAAQqT,EAAIvT,EAAGwT,EACjE,GAAC,CAAA7T,IAAA,SAAAC,MACD,WACI7L,KAAK6a,MAAM6E,SAAS,EAAG,GACvB1f,KAAK0a,QAAQnX,MAAM4I,MAAQ,MAC3BnM,KAAK0a,QAAQnX,MAAM6I,OAAS,MAE5BpM,KAAK2f,cACL3f,KAAK4f,OACT,GAAC,CAAAhU,IAAA,sBAAAC,MACD,SAAoByT,GAChB,IAAIF,EAAWpf,KAAKmU,GAAGkL,WAEvBD,EAASzT,MAAMvD,KAAKmX,SAAWD,EAG/B,IADA,IAAIO,EAAYT,EAAS1T,SAChBvI,EAAI,EAAGA,EAAI0c,EAAU3f,OAAQiD,IAClC0c,EAAU1c,GAAGwI,MAAMvC,OAAO0W,SAAWR,EAGzCtf,KAAK+f,aACL/f,KAAKggB,YACT,GAAC,CAAApU,IAAA,QAAAC,MACD,WACI7L,KAAK6a,MAAM6E,SAAS1f,KAAKgY,KAAKjW,EAAG/B,KAAKgY,KAAKhV,GAC3ChD,KAAK0a,QAAQnX,MAAM4I,MAAQnM,KAAKgY,KAAKjW,EAAI,KACzC/B,KAAK0a,QAAQnX,MAAM6I,OAASpM,KAAKgY,KAAKhV,EAAI,KAC1ChD,KAAK+f,aACL/f,KAAKggB,aAELhgB,KAAKmU,GAAGkD,oBAAoB1W,EAAUE,OAAQ,CAAEwK,KAAM,IAC1D,GAAC,CAAAO,IAAA,UAAAC,MACD,SAAQnI,GACJ,OAAO1D,KAAKigB,SAASjgB,KAAKsb,aAAetb,KAAKwU,KAAK1L,KAAKG,KAAMvF,EAClE,GAAC,CAAAkI,IAAA,WAAAC,MACD,SAASnI,GACL,OAAO1D,KAAKigB,SAASjgB,KAAKsb,aAAetb,KAAKwU,KAAK1L,KAAKG,KAAMvF,EAClE,GAAC,CAAAkI,IAAA,WAAAC,MACD,SAAS/C,EAAMpF,GACX,GAAIoF,EAAO9I,KAAKwU,KAAK1L,KAAKC,KAAOD,EAAO9I,KAAKwU,KAAK1L,KAAKE,IACnD,OAAO,EAEX,IAAIkX,EAAelgB,KAAKya,QAAQ0F,wBAChC,GACIrX,EAAO,GACPA,EAAO9I,KAAKsb,cACZtb,KAAKgY,KAAKjW,EAAI+G,EAAOoX,EAAa/T,OAClCnM,KAAKgY,KAAKhV,EAAI8F,EAAOoX,EAAa9T,OAElC,OAAO,EAEX,IAAIgU,EAAgB1c,EACd,CAAEqI,EAAGrI,EAAEqI,EAAImU,EAAanU,EAAGE,EAAGvI,EAAEuI,EAAIiU,EAAajU,GACjD,CAAEF,EAAGmU,EAAa/T,MAAQ,EAAGF,EAAGiU,EAAa9T,OAAS,GACxDiU,GACErgB,KAAKya,QAAQ6F,WAAaF,EAAYrU,GAAKjD,EAAQ9I,KAAKsb,aAAe8E,EAAYrU,EACrFwU,GACEvgB,KAAKya,QAAQ+F,UAAYJ,EAAYnU,GAAKnD,EAAQ9I,KAAKsb,aAAe8E,EAAYnU,EAExFjM,KAAKsb,aAAexS,EACpB,IAAK,IAAI3F,EAAI,EAAGA,EAAInD,KAAKya,QAAQ/O,SAASxL,OAAQiD,IAC9CnD,KAAKya,QAAQ/O,SAASvI,GAAGI,MAAMuF,KAAOA,EAK1C,OAHA9I,KAAK4f,QACL5f,KAAKya,QAAQ6F,WAAaD,EAC1BrgB,KAAKya,QAAQ+F,UAAYD,GAClB,CACX,GAAC,CAAA3U,IAAA,OAAAC,MACD,SAAK4U,GACDlf,EAAON,MAAM,aACbjB,KAAK2f,cACL3f,KAAK4f,QACCa,GACFzgB,KAAK0gB,YAAY1gB,KAAKmU,GAAG1E,KAAK1C,KAEtC,GAAC,CAAAnB,IAAA,WAAAC,MACD,WACI7L,KAAK2f,cACL3f,KAAK4f,OACT,GAAC,CAAAhU,IAAA,gBAAAC,MACD,SAAca,GACV,IAAIZ,EAAKY,EAAKf,MAAMvD,KACpB0D,EAAG6U,gBAAkB,CACjB5U,EAAGtL,SAASqL,EAAGuP,QAAQ9X,MAAMnD,MAAQJ,KAAKya,QAAQ6F,WAClDrU,EAAGxL,SAASqL,EAAGuP,QAAQ9X,MAAM8a,KAAOre,KAAKya,QAAQ+F,UAEzD,GAAC,CAAA5U,IAAA,mBAAAC,MACD,SAAiBa,GACb,IAAIZ,EAAKY,EAAKf,MAAMvD,KACpBpI,KAAKya,QAAQ6F,WAAa7f,SAASqL,EAAGuP,QAAQ9X,MAAMnD,MAAQ0L,EAAG6U,gBAAgB5U,EAC/E/L,KAAKya,QAAQ+F,UAAY/f,SAASqL,EAAGuP,QAAQ9X,MAAM8a,KAAOvS,EAAG6U,gBAAgB1U,CACjF,GAAC,CAAAL,IAAA,cAAAC,MACD,WACI,IAAI4D,EAAOzP,KAAKmU,GAAG1E,KACnB,GAAY,MAARA,EAAJ,CAGA,IAAIxC,EAAQwC,EAAKxC,MACbP,EAAO,KACX,IAAK,IAAIsQ,KAAU/P,GACfP,EAAOO,EAAM+P,IACRrR,MAAMvD,KAAKiT,QAAU,KAC1B3O,EAAKf,MAAMvD,KAAKmW,SAAW,KAE/Bve,KAAK0a,QAAQxX,UAAY,EARzB,CASJ,GAAC,CAAA0I,IAAA,aAAAC,MACD,WACI,IAAIoB,EAAQjN,KAAKmU,GAAG1E,KAAKxC,MACrBP,EAAO,KACPkU,EAAe,KACf7gB,EAAI,KACJ4W,EAAY,KACZkK,EAAc7gB,KAAK8gB,kBACvB,IAAK,IAAI9D,KAAU/P,EAGf2T,GADAjK,GADAjK,EAAOO,EAAM+P,IACIrR,MAAMvD,MACEiT,QACpBrb,KAAKoJ,OAAOqV,WAAW/R,KAASiK,EAAU4I,UAO/Cvf,KAAKid,wBAAwBvQ,GAC7B3M,EAAIC,KAAKoJ,OAAO2X,eAAerU,GAC/BiK,EAAU3K,MAAQ6U,EAAY9U,EAAIhM,EAAEgM,EACpC4K,EAAUzK,MAAQ2U,EAAY5U,EAAIlM,EAAEkM,EACpC2U,EAAard,MAAMnD,KAAOygB,EAAY9U,EAAIhM,EAAEgM,EAAI,KAChD6U,EAAard,MAAM8a,IAAMwC,EAAY5U,EAAIlM,EAAEkM,EAAI,KAC/C2U,EAAard,MAAMyd,QAAU,GAC7BJ,EAAard,MAAMoC,WAAa,UAChC3F,KAAKihB,eAAevU,EAAMmU,KAdtBD,EAAard,MAAMyd,QAAU,OACzBrK,EAAU4H,UAAY5H,EAAU4H,SAAShb,QACzCoT,EAAU4H,SAAShb,MAAMyd,QAAU,QAcnD,GAAC,CAAApV,IAAA,iBAAAC,MACD,SAAea,EAAMmU,GACjB,IAAInU,EAAKpB,OAAT,CAIA,IAAIiT,EAAW7R,EAAKf,MAAMvD,KAAKmW,SAC/B,GAA4B,GAAxB7R,EAAKhB,SAASxL,OAGd,OAFAqe,EAAShb,MAAMyd,QAAU,YACzBzC,EAAShb,MAAMoC,WAAa,UAIhC,IAAIub,EAAgBlhB,KAAKmhB,mBAAmBzU,GAC5C7K,EAAEY,EAAE8b,EAAU2C,GAEd,IAAIE,EAAaphB,KAAKoJ,OAAOiY,mBAAmB3U,GAChD6R,EAAShb,MAAMnD,KAAOygB,EAAY9U,EAAIqV,EAAWrV,EAAI,EAAI,KACzDwS,EAAShb,MAAM8a,IAAMwC,EAAY5U,EAAImV,EAAWnV,EAAI,EAAI,KACxDsS,EAAShb,MAAMyd,QAAU,GACzBzC,EAAShb,MAAMoC,WAAa,UAC5B4Y,EAAShb,MAAM+d,gBAAkB5U,EAAKrB,KAAKkW,WAjB3C,CAkBJ,GAAC,CAAA3V,IAAA,qBAAAC,MAED,SAAmBa,GACf,IAAInJ,EAAUvD,KAAKwU,KAAKrL,eAAiBnJ,KAAKwU,KAAKrL,eAAezI,cAAgB,OAClF,MAAc,WAAV6C,EACOmJ,EAAKhB,SAASxL,OAAS,GAAK,MAAQwM,EAAKhB,SAASxL,OAE/C,SAAVqD,EACOmJ,EAAKjB,SAAW,IAAM,SADjC,CAGJ,GAAC,CAAAG,IAAA,uBAAAC,MAED,SAAqB2V,EAAK9U,GAClB1M,KAAKwU,KAAKrM,aACVtG,EAAEmB,EAAEwe,EAAK9U,EAAKtB,OAEdvJ,EAAEY,EAAE+e,EAAK9U,EAAKtB,MAEtB,GAAC,CAAAQ,IAAA,sBAAAC,MACD,SAAoB2V,EAAK9U,GACN1M,KAAKwU,KAAKtL,mBAAmBlJ,KAAKmU,GAAIqN,EAAK9U,IAEtD1M,KAAKgb,qBAAqBwG,EAAK9U,EAEvC,GAAC,CAAAd,IAAA,0BAAAC,MACD,SAAwBa,GACpB1M,KAAKwe,yBAAyB9R,EAAKf,MAAMvD,KAAKiT,QAAS3O,EAAKrB,KAChE,GAAC,CAAAO,IAAA,2BAAAC,MACD,SAAyB+U,EAAc1Q,GAsBnC,GArBI,qBAAsBA,IACtB0Q,EAAard,MAAM+d,gBAAkBpR,EAAU,qBAE/C,qBAAsBA,IACtB0Q,EAAard,MAAMkV,MAAQvI,EAAU,qBAErC,UAAWA,IACX0Q,EAAard,MAAM4I,MAAQ+D,EAAiB,MAAI,MAEhD,WAAYA,IACZ0Q,EAAard,MAAM6I,OAAS8D,EAAkB,OAAI,MAElD,cAAeA,IACf0Q,EAAard,MAAMke,SAAWvR,EAAU,aAAe,MAEvD,gBAAiBA,IACjB0Q,EAAard,MAAMme,WAAaxR,EAAU,gBAE1C,eAAgBA,IAChB0Q,EAAard,MAAMoe,UAAYzR,EAAU,eAEzC,qBAAsBA,EAAW,CACjC,IAAI0R,EAAkB1R,EAAU,oBAChC,GAAI0R,EAAgB9hB,WAAW,SAAWoQ,EAAiB,OAAKA,EAAkB,OAAG,CACjF,IAAI0I,EAAM,IAAIC,MAEdD,EAAIvU,OAAS,WACT,IAAI/B,EAAIT,EAAES,EAAE,UACZA,EAAE6J,MAAQyU,EAAanD,YACvBnb,EAAE8J,OAASwU,EAAajD,aAExB,GAAIrb,EAAEuX,WAAY,CACJvX,EAAEuX,WAAW,MACnBf,UAHE9Y,KAKF,EACA,EACA4gB,EAAanD,YACbmD,EAAajD,cAEjB,IAAIkE,EAAkBvf,EAAEwf,YACxBlB,EAAard,MAAMqe,gBAAkB,OAASC,EAAkB,GACpE,GAEJjJ,EAAIG,IAAM6I,CACd,MACIhB,EAAard,MAAMqe,gBAAkB,OAASA,EAAkB,IAEpEhB,EAAard,MAAMwe,eAAiB,MAEhC,wBAAyB7R,IACzB0Q,EAAard,MAAMye,UACf,UAAY9R,EAAU,uBAAyB,OAE3D,CACJ,GAAC,CAAAtE,IAAA,qCAAAC,MACD,SAAmCa,GAC/B,IAAIkU,EAAelU,EAAKf,MAAMvD,KAAKiT,QAC/BnL,EAAYxD,EAAKrB,KACjB,qBAAsB6E,IACtB0Q,EAAard,MAAM+d,gBAAkBpR,EAAU,qBAE/C,qBAAsBA,IACtB0Q,EAAard,MAAMkV,MAAQvI,EAAU,oBAE7C,GAAC,CAAAtE,IAAA,mCAAAC,MACD,SAAiCa,GAC7B,IAAIkU,EAAelU,EAAKf,MAAMvD,KAAKiT,QACnCuF,EAAard,MAAM+d,gBAAkB,GACrCV,EAAard,MAAMkV,MAAQ,EAC/B,GAAC,CAAA7M,IAAA,cAAAC,MACD,WACI7L,KAAK6a,MAAMoH,OACf,GAAC,CAAArW,IAAA,aAAAC,MACD,WACI7L,KAAK4c,cACL,IAAI3P,EAAQjN,KAAKmU,GAAG1E,KAAKxC,MACrBP,EAAO,KACP6L,EAAM,KACNtB,EAAO,KACPwB,EAAQ,KACRyJ,EAAUliB,KAAK8gB,kBACnB,IAAK,IAAI9D,KAAU/P,GACfP,EAAOO,EAAM+P,IACF1R,QAGNtL,KAAKoJ,OAAOqV,WAAW/R,KAGxBA,EAAKf,MAAMvC,OAAO0W,WAGtBvH,EAAMvY,KAAKoJ,OAAO+Y,kBAAkBzV,GACpCuK,EAAOjX,KAAKoJ,OAAO0N,mBAAmBpK,EAAKnB,QAC3CkN,EAAQ/L,EAAKrB,KAAK,sBAClBrL,KAAK6a,MAAMuH,UAAUnL,EAAMsB,EAAK2J,EAASzJ,IAEjD,GAAC,CAAA7M,IAAA,yBAAAC,MAED,SAAuBwW,GAAS,IAAAC,EAAAtiB,KAE5B,GADAA,KAAKwU,KAAK7L,UAAY0Z,GACjBriB,KAAKib,aAAc,CACpB,IACIlP,EAAGE,EADHsW,GAAW,EAEXviB,KAAKwU,KAAK5L,iCAEV5I,KAAKya,QAAQlX,MAAQ,oBAGzB1B,EAAE4B,GAAGzD,KAAK+H,UAAW,YAAa,SAAAya,GAAaxG,OAAAsG,GACvCtiB,KAAKwU,KAAK7L,YACV4Z,GAAW,EAEXxW,EAAIyW,EAAUC,QACdxW,EAAIuW,EAAUE,UAErBvG,KAACnc,OAEF6B,EAAE4B,GAAGzD,KAAK+H,UAAW,UAAW,WAAMiU,OAAAsG,GAClCC,GAAW,GACdpG,KAACnc,OAEF6B,EAAE4B,GAAGzD,KAAK+H,UAAW,YAAa,SAAA4a,GAAa3G,OAAAsG,GACvCtiB,KAAKwU,KAAK7L,WACN4Z,IACAviB,KAAKya,QAAQmI,SAAS7W,EAAI4W,EAAUF,QAASxW,EAAI0W,EAAUD,SAE3D3W,EAAI4W,EAAUF,QACdxW,EAAI0W,EAAUD,UAGzBvG,KAACnc,MACN,CACJ,GAAC,CAAA4L,IAAA,cAAAC,MACD,SAAYa,GACR,IAAK1M,KAAKoJ,OAAOqV,WAAW/R,GAExB,OADAnL,EAAOJ,KAAK,wDACL,EAEX,IAAIwV,EAAYjK,EAAKf,MAAMvD,KACvB8X,EAAelgB,KAAKya,QAAQ0F,wBAC5B0C,EACGlM,EAAU3K,MAAQ2K,EAAUxK,MAAQ,EADvC0W,EAEGlM,EAAUzK,MAAQyK,EAAUvK,OAAS,EAM5C,OAJApM,KAAKya,QAAQqI,SACTD,EAAsB7iB,KAAKsb,aAAe4E,EAAa/T,MAAQ,EAC/D0W,EAAsB7iB,KAAKsb,aAAe4E,EAAa9T,OAAS,IAE7D,CACX,GAAC,CAAAR,IAAA,SAAAC,MAED,SAAOnI,GAEH,OADAnC,EAAOJ,KAAK,8BACLnB,KAAK+iB,QAAQrf,EACxB,GAAC,CAAAkI,IAAA,UAAAC,MACD,SAAQnI,GAEJ,OADAnC,EAAOJ,KAAK,+BACLnB,KAAKgjB,SAAStf,EACzB,GAAC,CAAAkI,IAAA,UAAAC,MACD,SAAQ/C,EAAMpF,GAEV,OADAnC,EAAOJ,KAAK,+BACLnB,KAAKigB,SAASnX,EAAMpF,EAC/B,IAAC,CApqBoB,GCDZuf,EAAgB,WAQxB,OAAAnhB,GAPD,SAAAmhB,EAAY9O,EAAII,GAASvS,OAAAihB,GACrBjjB,KAAKmU,GAAKA,EACVnU,KAAKwU,KAAOD,EACZvU,KAAKiK,QAAUsK,EAAQtK,QACvBjK,KAAKgK,QAAUuK,EAAQvK,QACvBhK,KAAKkjB,OAAS,KACdljB,KAAKmjB,SAAW,EACpB,GAAC,CAAA,CAAAvX,IAAA,OAAAC,MACD,WAaI,IAAK,IAAIuX,KAZTvhB,EAAE4B,GAAGzD,KAAKmU,GAAG/L,KAAKqS,QAAS,UAAWza,KAAKqjB,QAAQlH,KAAKnc,OAExDA,KAAKgK,QAAkB,SAAIhK,KAAKsjB,gBAChCtjB,KAAKgK,QAAoB,WAAIhK,KAAKujB,kBAClCvjB,KAAKgK,QAAkB,SAAIhK,KAAKwjB,gBAChCxjB,KAAKgK,QAAiB,QAAIhK,KAAKyjB,eAC/BzjB,KAAKgK,QAAgB,OAAIhK,KAAK0jB,cAC9B1jB,KAAKgK,QAAY,GAAIhK,KAAK2jB,UAC1B3jB,KAAKgK,QAAc,KAAIhK,KAAK4jB,YAC5B5jB,KAAKgK,QAAc,KAAIhK,KAAK6jB,YAC5B7jB,KAAKgK,QAAe,MAAIhK,KAAK8jB,aAEV9jB,KAAKiK,QACpB,GAAMjK,KAAKiK,QAAQmZ,IAAWA,KAAUpjB,KAAKgK,QAAS,CAClD,IAAI+Z,EAAO/jB,KAAKiK,QAAQmZ,GACnBxP,MAAMoQ,QAAQD,KACfA,EAAO,CAACA,IACX,IACmBrQ,EADnBG,EAAAC,EACeiQ,GAAI,IAApB,IAAAlQ,EAAAjM,MAAA8L,EAAAG,EAAAnR,KAAAqR,MAAsB,CAAA,IAAbnI,EAAG8H,EAAA7H,MACR7L,KAAKmjB,SAASvX,GAAO5L,KAAKgK,QAAQoZ,EACtC,CAAC,CAAA,MAAApP,GAAAH,EAAAnQ,EAAAsQ,EAAA,CAAA,QAAAH,EAAAI,GAAA,CACL,CAGkC,mBAA3BjU,KAAKwU,KAAKyP,aACjBjkB,KAAKkjB,OAASljB,KAAKwU,KAAKyP,aAExBjkB,KAAKkjB,OAASpf,EAAKqD,KAAKC,KAEhC,GAAC,CAAAwE,IAAA,kBAAAC,MACD,WACI7L,KAAKwU,KAAKzK,QAAS,CACvB,GAAC,CAAA6B,IAAA,mBAAAC,MACD,WACI7L,KAAKwU,KAAKzK,QAAS,CACvB,GAAC,CAAA6B,IAAA,UAAAC,MACD,SAAQnI,GAIJ,GAHe,GAAXA,EAAEwgB,OACFxgB,EAAEygB,kBAEFnkB,KAAKmU,GAAG/L,KAAKgc,aAAjB,CAGA,IAAIre,EAAMrC,GAAK8X,MACf,IAAKxb,KAAKwU,KAAKzK,OACX,OAAO,EAEX,IAAIsa,EACAte,EAAI0V,SACH1V,EAAIue,SAAW,KACfve,EAAIwe,SAAW,KACfxe,EAAIye,QAAU,KACdze,EAAI0e,UAAY,IACjBJ,KAAMrkB,KAAKmjB,UACXnjB,KAAKmjB,SAASkB,GAAInd,KAAKlH,KAAMA,KAAKmU,GAAIzQ,EAZ1C,CAcJ,GAAC,CAAAkI,IAAA,kBAAAC,MACD,SAAgB6Y,EAAKhhB,GACjB,IAAIiX,EAAgB+J,EAAIC,oBACxB,GAAMhK,EAAe,CACjB,IAAIzN,EAAUlN,KAAKkjB,SACRwB,EAAIhX,SAASiN,EAAezN,EAAS,cAE5CwX,EAAI7F,YAAY3R,GAChBwX,EAAIE,WAAW1X,GAEvB,CACJ,GAAC,CAAAtB,IAAA,oBAAAC,MACD,SAAkB6Y,EAAKhhB,GACnB,IAAIiX,EAAgB+J,EAAIC,oBACxB,GAAMhK,IAAkBA,EAAcrP,OAAQ,CAC1C,IAAI4B,EAAUlN,KAAKkjB,SACRwB,EAAIG,kBAAkBlK,EAAezN,EAAS,cAErDwX,EAAI7F,YAAY3R,GAChBwX,EAAIE,WAAW1X,GAEvB,CACJ,GAAC,CAAAtB,IAAA,kBAAAC,MACD,SAAgB6Y,EAAKhhB,GACjB,IAAIiX,EAAgB+J,EAAIC,oBAClBhK,GACF+J,EAAIE,WAAWjK,EAEvB,GAAC,CAAA/O,IAAA,iBAAAC,MACD,SAAe6Y,EAAKhhB,GAChB,IAAIiX,EAAgB+J,EAAIC,oBAClBhK,IAAkBA,EAAcrP,SAClCoZ,EAAI7F,YAAYlE,EAAcpP,QAC9BmZ,EAAI9V,YAAY+L,GAExB,GAAC,CAAA/O,IAAA,gBAAAC,MACD,SAAc6Y,EAAKhhB,GACf,IAAIqC,EAAMrC,GAAK8X,MACXb,EAAgB+J,EAAIC,oBAClBhK,IACF+J,EAAII,YAAYnK,EAAcvY,IAC9B2D,EAAI4V,kBACJ5V,EAAIoe,iBAEZ,GAAC,CAAAvY,IAAA,YAAAC,MACD,SAAU6Y,EAAKhhB,GACX,IAAIqC,EAAMrC,GAAK8X,MACXb,EAAgB+J,EAAIC,oBACxB,GAAMhK,EAAe,CACjB,IAAIoK,EAAUL,EAAIM,iBAAiBrK,GACnC,IAAKoK,EAAS,CACV,IAAIE,EAAKP,EAAIM,iBAAiBrK,EAAcpP,QACtC0Z,GAAMA,EAAGvZ,SAASxL,OAAS,IAC7B6kB,EAAUE,EAAGvZ,SAASuZ,EAAGvZ,SAASxL,OAAS,GAEnD,CACM6kB,GACFL,EAAI7F,YAAYkG,GAEpBhf,EAAI4V,kBACJ5V,EAAIoe,gBACR,CACJ,GAAC,CAAAvY,IAAA,cAAAC,MACD,SAAY6Y,EAAKhhB,GACb,IAAIqC,EAAMrC,GAAK8X,MACXb,EAAgB+J,EAAIC,oBACxB,GAAMhK,EAAe,CACjB,IAAIuK,EAAYR,EAAIS,gBAAgBxK,GACpC,IAAKuK,EAAW,CACZ,IAAID,EAAKP,EAAIS,gBAAgBxK,EAAcpP,QACrC0Z,GAAMA,EAAGvZ,SAASxL,OAAS,IAC7BglB,EAAYD,EAAGvZ,SAAS,GAEhC,CACMwZ,GACFR,EAAI7F,YAAYqG,GAEpBnf,EAAI4V,kBACJ5V,EAAIoe,gBACR,CACJ,GAAC,CAAAvY,IAAA,cAAAC,MACD,SAAY6Y,EAAKhhB,GACb1D,KAAKolB,kBAAkBV,EAAKhhB,EAAGvD,EAAUC,KAC7C,GAAC,CAAAwL,IAAA,eAAAC,MACD,SAAa6Y,EAAKhhB,GACd1D,KAAKolB,kBAAkBV,EAAKhhB,EAAGvD,EAAUG,MAC7C,GAAC,CAAAsL,IAAA,oBAAAC,MACD,SAAkB6Y,EAAKhhB,EAAGzB,GACtB,IAAI8D,EAAMrC,GAAK8X,MACXb,EAAgB+J,EAAIC,oBACpBjY,EAAO,KACX,GAAMiO,EAAe,CACjB,GAAIA,EAAcrP,OAAQ,CAGtB,IAFA,IAAIhJ,EAAIqY,EAAcjP,SAClBA,EAAW,GACNvI,EAAI,EAAGA,EAAIb,EAAEpC,OAAQiD,IACtBb,EAAEa,GAAGqI,YAAcvJ,GACnByJ,EAAS4B,KAAKnK,GAGtBuJ,EAAOpK,EAAEoJ,EAASnE,KAAK8d,OAAO3Z,EAASxL,OAAS,GAAK,IACzD,MAAO,GAAIya,EAAcnP,YAAcvJ,EAAG,CACtC,IACIiT,GADAxJ,EAAWiP,EAAcjP,UACCxL,OAC1BgV,EAAiB,IACjBxI,EAAOhB,EAASnE,KAAK8d,OAAOnQ,EAAiB,GAAK,IAE1D,MACIxI,EAAOiO,EAAcpP,OAEnBmB,GACFgY,EAAI7F,YAAYnS,GAEpB3G,EAAI4V,kBACJ5V,EAAIoe,gBACR,CACJ,IAAC,CAtLwB,GCFvBmB,EAAc,CAChBC,QAAS,IAaN,SAASC,EAAMrR,EAAII,GACtB1S,EAAEE,EAAE0jB,YAAW,YAKnB,SAAgBtR,EAAII,GAAS,IAAA2G,EAAAlb,KACzBslB,EAAYC,QAAQG,QAAQ,SAAA3lB,GAAC,OAAAic,OAAAd,GAAInb,EAAE4lB,QAAQxR,EAAII,EAAQxU,EAAEwE,MAAM,EAAA4X,KAACnc,MACpE,CANQ4lB,CAAOzR,EAAII,EACd,GAAE,EACP,CAMasR,IAAAA,EAAM/jB,GAEf,SAAA+jB,EAAYthB,EAAMohB,GACd,GADuB3jB,OAAA6jB,IAClBthB,EACD,MAAM,IAAIuhB,MAAM,0BAEpB,IAAKH,GAA8B,mBAAZA,EACnB,MAAM,IAAIG,MAAM,oCAEpB9lB,KAAKuE,KAAOA,EACZvE,KAAK2lB,QAAUA,CACnB,ICxBC9hB,OAAOif,WACRjf,OAAOif,SAAW,SAAUiD,GACxBliB,OAAOyc,WAAayF,EAAO3lB,KAC3ByD,OAAO2c,UAAYuF,EAAO1H,MAI7Bnc,SAAS4D,KAAKgd,WACfkD,QAAQnmB,UAAUijB,SAAW,SAAUiD,GACnC/lB,KAAKsgB,WAAayF,EAAO3lB,KACzBJ,KAAKwgB,UAAYuF,EAAO1H,MAIX4H,IAAAA,EAAM,WAUvB,SAAAA,EAAY1R,GAASvS,OAAAikB,GACjBA,EAAOC,QAAUlmB,KACjBA,KAAKuU,QVmBN,SAAsBA,GACzB,IAAIC,EAAO,CAAA,EAIX,GAHA1Q,EAAKuC,KAAKO,MAAM4N,EAAM1M,GACtBhE,EAAKuC,KAAKO,MAAM4N,EAAMD,IAEjBC,EAAKzM,UACN,MAAM,IAAI+d,MAAM,sDAEpB,OAAOtR,CACX,CU5BuB2R,CAAa5R,GAC5BhT,EAAOE,MAAMT,EAAShB,KAAKuU,QAAQ5S,YACnC3B,KAAK8M,QAAUnN,EACfK,KAAKomB,aAAc,EACnBpmB,KAAKyP,KAAO,KACZzP,KAAKqmB,cAAgB,GACrBrmB,KAAKsmB,MACT,CAAC,OAAAxkB,EAAAmkB,EAAA,CAAA,CAAAra,IAAA,OAAAC,MAED,WACI,IAAM7L,KAAKomB,YAAX,CAGApmB,KAAKomB,aAAc,EACnB,IAAIG,EAAc,CACdre,KAAMlI,KAAKuU,QAAQrM,KACnBmB,OAAQrJ,KAAKuU,QAAQnL,OAAOC,OAC5BC,OAAQtJ,KAAKuU,QAAQnL,OAAOE,OAC5BC,OAAQvJ,KAAKuU,QAAQnL,OAAOG,OAC5BC,aAAcxJ,KAAKuU,QAAQnL,OAAOI,cAElCgd,EAAY,CACZze,UAAW/H,KAAKuU,QAAQxM,UACxBI,aAAcnI,KAAKuU,QAAQpM,aAC3BE,OAAQrI,KAAKuU,QAAQnM,KAAKC,OAC1BC,QAAStI,KAAKuU,QAAQnM,KAAKE,QAC3BC,QAASvI,KAAKuU,QAAQnM,KAAKG,QAC3BC,WAAYxI,KAAKuU,QAAQnM,KAAKI,WAC9BC,WAAYzI,KAAKuU,QAAQnM,KAAKK,WAC9BC,WAAY1I,KAAKuU,QAAQnM,KAAKM,WAC9BC,UAAW3I,KAAKuU,QAAQnM,KAAKO,UAC7BC,+BAAgC5I,KAAKuU,QAAQnM,KAAKQ,+BAClDC,cAAe7I,KAAKuU,QAAQnM,KAAKS,cACjCC,KAAM9I,KAAKuU,QAAQnM,KAAKU,KACxBI,mBAAoBlJ,KAAKuU,QAAQnM,KAAKc,mBACtCC,eAAgBnJ,KAAKuU,QAAQnM,KAAKe,gBAGtCnJ,KAAKqL,KAAO,IAAI6I,EAAalU,MAC7BA,KAAKoJ,OAAS,IAAIkL,EAAetU,KAAMumB,GACvCvmB,KAAKoI,KAAO,IAAIoS,EAAaxa,KAAMwmB,GACnCxmB,KAAK8J,SAAW,IAAImZ,EAAiBjjB,KAAMA,KAAKuU,QAAQzK,UAExD9J,KAAKqL,KAAKib,OACVtmB,KAAKoJ,OAAOkd,OACZtmB,KAAKoI,KAAKke,OACVtmB,KAAK8J,SAASwc,OAEdtmB,KAAKymB,cAELC,EAAc1mB,KAAMA,KAAKuU,QAAQ9J,OAtCjC,CAuCJ,GAAC,CAAAmB,IAAA,eAAAC,MACD,WACI,OAAO7L,KAAKuU,QAAQvM,QACxB,GAAC,CAAA4D,IAAA,cAAAC,MACD,WACI7L,KAAKuU,QAAQvM,UAAW,CAC5B,GAAC,CAAA4D,IAAA,eAAAC,MACD,WACI7L,KAAKuU,QAAQvM,UAAW,CAC5B,GAAC,CAAA4D,IAAA,qBAAAC,MACD,WACI,OAAO7L,KAAKuU,QAAQnM,KAAKO,SAC7B,GAAC,CAAAiD,IAAA,wBAAAC,MACD,WACI7L,KAAKuU,QAAQnM,KAAKO,WAAY,EAC9B3I,KAAKoI,KAAK8U,wBAAuB,EACrC,GAAC,CAAAtR,IAAA,yBAAAC,MACD,WACI7L,KAAKuU,QAAQnM,KAAKO,WAAY,EAC9B3I,KAAKoI,KAAK8U,wBAAuB,EACrC,GAAC,CAAAtR,IAAA,sBAAAC,MAED,SAAoB0Q,GAChBvc,KAAKuU,QAAQ9K,qBAAqB,UAAY8S,EAAe,YAAa,CAC9E,GAAC,CAAA3Q,IAAA,uBAAAC,MAED,SAAqB0Q,GACjBvc,KAAKuU,QAAQ9K,qBAAqB,UAAY8S,EAAe,YAAa,CAC9E,GAAC,CAAA3Q,IAAA,YAAAC,MACD,SAAU5D,GACN,IAAI0e,EAAY3mB,KAAKuU,QAAQtM,MAC7BjI,KAAKuU,QAAQtM,MAAUA,GAAgB,KACnC0e,GAAa3mB,KAAKuU,QAAQtM,QAC1BjI,KAAKoI,KAAK0U,cACV9c,KAAKoI,KAAKwe,qBAElB,GAAC,CAAAhb,IAAA,cAAAC,MACD,WACI7L,KAAKoI,KAAKye,UAAU7mB,KAAM,YAAaA,KAAK8mB,kBAC5C9mB,KAAKoI,KAAKye,UAAU7mB,KAAM,QAASA,KAAK+mB,cACxC/mB,KAAKoI,KAAKye,UAAU7mB,KAAM,WAAYA,KAAKgnB,iBAC3ChnB,KAAKoI,KAAKye,UAAU7mB,KAAM,aAAcA,KAAKinB,mBAAmB,EACpE,GAAC,CAAArb,IAAA,mBAAAC,MACD,SAAiBnI,GACb,GAAK1D,KAAKuU,QAAQ9K,qBAA8C,wBAAhE,CAGA,IAAI4R,EAAU3X,EAAE+Y,QAAUjB,MAAM0L,WAC5Bha,EAAUlN,KAAKoI,KAAKsU,kBAAkBrB,GACpCnO,EACElN,KAAKoI,KAAKiF,QAAQgO,IAClBrb,KAAK6e,YAAY3R,GAGrBlN,KAAKmnB,cART,CAUJ,GAAC,CAAAvb,IAAA,eAAAC,MACD,SAAanI,GACT,GAAK1D,KAAKuU,QAAQ9K,qBAA0C,oBAA5D,CAGA,IAAI4R,EAAU3X,EAAE+Y,QAAUjB,MAAM0L,WAEhC,GADkBlnB,KAAKoI,KAAKgf,YAAY/L,GACvB,CACb,IAAInO,EAAUlN,KAAKoI,KAAKsU,kBAAkBrB,GACpCnO,GACFlN,KAAK8kB,YAAY5X,EAEzB,CARA,CASJ,GAAC,CAAAtB,IAAA,kBAAAC,MACD,SAAgBnI,GACZ,GAAK1D,KAAKuU,QAAQ9K,qBAA6C,wBAG3DzJ,KAAKqnB,eAAgB,CACrB,IAAIhM,EAAU3X,EAAE+Y,QAAUjB,MAAM0L,WAEhC,GADclnB,KAAKoI,KAAKiF,QAAQgO,GACnB,CACT,IAAInO,EAAUlN,KAAKoI,KAAKsU,kBAAkBrB,GACpCnO,GACFlN,KAAK4kB,WAAW1X,EAExB,CACJ,CACJ,GAAC,CAAAtB,IAAA,oBAAAC,MAED,SAAkBnI,GAEd,GAAK1D,KAAKuU,QAAQ9K,qBAA+C,0BAAM/F,EAAE6gB,QAAzE,CAGA,IAAIxe,EAAMrC,GAAK8X,MAEfzV,EAAIoe,iBAEApe,EAAIuhB,OAAS,EACbtnB,KAAKoI,KAAK2a,QAAQhd,GAElB/F,KAAKoI,KAAK4a,SAASjd,EARvB,CAUJ,GAAC,CAAA6F,IAAA,aAAAC,MACD,SAAWa,GACP,IAAKhC,EAAK2C,QAAQX,GAAO,CACrB,IAAIiB,EAAW3N,KAAK4N,SAASlB,GAC7B,OAAKiB,EAIM3N,KAAK4kB,WAAWjX,IAHvBpM,EAAOH,MAAM,eAAiBsL,EAAO,wBAC9B,EAIf,CACI1M,KAAKqnB,eACLrnB,KAAKoI,KAAKmf,gBAAgB7a,GAE1BnL,EAAOH,MAAM,uCAGrB,GAAC,CAAAwK,IAAA,WAAAC,MACD,WACI7L,KAAKoI,KAAKsT,eACd,GAAC,CAAA9P,IAAA,cAAAC,MACD,SAAYa,GACR,IAAKhC,EAAK2C,QAAQX,GAAO,CACrB,IAAIiB,EAAW3N,KAAK4N,SAASlB,GAC7B,OAAKiB,EAIM3N,KAAK8kB,YAAYnX,QAHxBpM,EAAOH,MAAM,eAAiBsL,EAAO,sBAK7C,CACIA,EAAKpB,SAGTtL,KAAKoI,KAAKof,cAAc9a,GACxB1M,KAAKoJ,OAAO0b,YAAYpY,GACxB1M,KAAKoI,KAAKqf,WACVznB,KAAKoI,KAAKsf,iBAAiBhb,GAC/B,GAAC,CAAAd,IAAA,cAAAC,MACD,SAAYa,GACR,IAAKhC,EAAK2C,QAAQX,GAAO,CACrB,IAAIiB,EAAW3N,KAAK4N,SAASlB,GAC7B,OAAKiB,EAIM3N,KAAKmX,YAAYxJ,QAHxBpM,EAAOH,MAAM,eAAiBsL,EAAO,sBAK7C,CACIA,EAAKpB,SAGTtL,KAAKoI,KAAKof,cAAc9a,GACxB1M,KAAKoJ,OAAO+N,YAAYzK,GACxB1M,KAAKoI,KAAKqf,WACVznB,KAAKoI,KAAKsf,iBAAiBhb,GAC/B,GAAC,CAAAd,IAAA,gBAAAC,MACD,SAAca,GACV,IAAKhC,EAAK2C,QAAQX,GAAO,CACrB,IAAIiB,EAAW3N,KAAK4N,SAASlB,GAC7B,OAAKiB,EAIM3N,KAAKkX,cAAcvJ,QAH1BpM,EAAOH,MAAM,eAAiBsL,EAAO,sBAK7C,CACIA,EAAKpB,SAGTtL,KAAKoI,KAAKof,cAAc9a,GACxB1M,KAAKoJ,OAAO8N,cAAcxK,GAC1B1M,KAAKoI,KAAKqf,WACVznB,KAAKoI,KAAKsf,iBAAiBhb,GAC/B,GAAC,CAAAd,IAAA,aAAAC,MACD,WACI7L,KAAKoJ,OAAOue,aACZ3nB,KAAKoI,KAAKqf,UACd,GAAC,CAAA7b,IAAA,eAAAC,MACD,WACI7L,KAAKoJ,OAAOwe,eACZ5nB,KAAKoI,KAAKqf,UACd,GAAC,CAAA7b,IAAA,kBAAAC,MACD,SAAgB4L,GACZzX,KAAKoJ,OAAOsO,gBAAgBD,GAC5BzX,KAAKoI,KAAKqf,UACd,GAAC,CAAA7b,IAAA,SAAAC,MACD,WACI7L,KAAKoI,KAAKyf,QACV7nB,KAAKoJ,OAAOye,QACZ7nB,KAAKqL,KAAKwc,OACd,GAAC,CAAAjc,IAAA,QAAAC,MACD,SAAM4D,GACF,IAAIqY,EAAIrY,GAAQP,EAAOiB,WAAWf,QAElCpP,KAAKyP,KAAOzP,KAAKqL,KAAK0c,KAAKD,GACtB9nB,KAAKyP,MAINlO,EAAON,MAAM,gBAGjBjB,KAAKoI,KAAK2f,OACVxmB,EAAON,MAAM,gBAEbjB,KAAKoJ,OAAOA,SACZ7H,EAAON,MAAM,oBAEbjB,KAAKoI,KAAKxH,MAAK,GACfW,EAAON,MAAM,gBAEbjB,KAAKqX,oBAAoB1W,EAAUC,KAAM,CAAEyK,KAAM,CAACoE,MAf9ClO,EAAOH,MAAM,kBAgBrB,GAAC,CAAAwK,IAAA,OAAAC,MACD,SAAK4D,GACDzP,KAAKgoB,SACLhoB,KAAK4f,MAAMnQ,EACf,GAAC,CAAA7D,IAAA,WAAAC,MACD,WACI,MAAO,CACHtH,KAAMvE,KAAKyP,KAAKlL,KAChBsI,OAAQ7M,KAAKyP,KAAK5C,OAClBC,QAAS9M,KAAKyP,KAAK3C,QAE3B,GAAC,CAAAlB,IAAA,WAAAC,MACD,SAASwI,GACL,IAAI7E,EAAK6E,GAAe,YACxB,OAAOrU,KAAKqL,KAAKsE,SAASH,EAC9B,GAAC,CAAA5D,IAAA,WAAAC,MACD,WACI,OAAO7L,KAAKyP,KAAK1C,IACrB,GAAC,CAAAnB,IAAA,WAAAC,MACD,SAASa,GACL,OAAIhC,EAAK2C,QAAQX,GACNA,EAEJ1M,KAAKyP,KAAK7B,SAASlB,EAC9B,GAAC,CAAAd,IAAA,WAAAC,MACD,SAASY,EAAaS,EAAS9B,EAAOC,EAAMG,GACxC,GAAIxL,KAAKqnB,eAAgB,CACrB,IAAIY,EAAkBjoB,KAAK4N,SAASnB,GAChCjM,EAAML,EAAUI,GAAGiL,QACX0c,IAAR1nB,IACAA,EAAMR,KAAKoJ,OAAO+e,+BAA+BF,IAErD,IAAIvb,EAAO1M,KAAKyP,KAAK/B,SAASua,EAAiB/a,EAAS9B,EAAOC,EAAM7K,GAarE,OAZMkM,IACF1M,KAAKoI,KAAKsF,SAAShB,GACnB1M,KAAKoJ,OAAOA,SACZpJ,KAAKoI,KAAKxH,MAAK,GACfZ,KAAKoI,KAAK6U,wBAAwBvQ,GAClC1M,KAAKmX,YAAY8Q,GACjBjoB,KAAKqX,oBAAoB1W,EAAUG,KAAM,CACrCiF,IAAK,WACLsF,KAAM,CAAC4c,EAAgB7lB,GAAI8K,EAAS9B,EAAOC,EAAM7K,GACjDkM,KAAMQ,KAGPR,CACX,CAEI,OADAnL,EAAOH,MAAM,uCACN,IAEf,GAAC,CAAAwK,IAAA,qBAAAC,MACD,SAAmB2B,EAAaN,EAAS9B,EAAOC,EAAMG,GAClD,GAAIxL,KAAKqnB,eAAgB,CACrB,IAAIe,EAAkBpoB,KAAK4N,SAASJ,GAChChN,EAAML,EAAUI,GAAGiL,QACX0c,IAAR1nB,IACAA,EAAMR,KAAKoJ,OAAO+e,+BAA+BC,EAAgB7c,SAErE,IAAImB,EAAO1M,KAAKyP,KAAK4Y,mBAAmBD,EAAiBlb,EAAS9B,EAAOC,EAAM7K,GAW/E,OAVMkM,IACF1M,KAAKoI,KAAKsF,SAAShB,GACnB1M,KAAKoJ,OAAOA,SACZpJ,KAAKoI,KAAKxH,MAAK,GACfZ,KAAKqX,oBAAoB1W,EAAUG,KAAM,CACrCiF,IAAK,qBACLsF,KAAM,CAAC+c,EAAgBhmB,GAAI8K,EAAS9B,EAAOC,EAAM7K,GACjDkM,KAAMQ,KAGPR,CACX,CAEI,OADAnL,EAAOH,MAAM,uCACN,IAEf,GAAC,CAAAwK,IAAA,oBAAAC,MACD,SAAkBiC,EAAYZ,EAAS9B,EAAOC,EAAMG,GAChD,GAAIxL,KAAKqnB,eAAgB,CACrB,IAAIiB,EAAiBtoB,KAAK4N,SAASE,GAC/BtN,EAAML,EAAUI,GAAGiL,QACX0c,IAAR1nB,IACAA,EAAMR,KAAKoJ,OAAO+e,+BAA+BG,EAAe/c,SAEpE,IAAImB,EAAO1M,KAAKyP,KAAKoV,kBAAkByD,EAAgBpb,EAAS9B,EAAOC,EAAM7K,GAW7E,OAVMkM,IACF1M,KAAKoI,KAAKsF,SAAShB,GACnB1M,KAAKoJ,OAAOA,SACZpJ,KAAKoI,KAAKxH,MAAK,GACfZ,KAAKqX,oBAAoB1W,EAAUG,KAAM,CACrCiF,IAAK,oBACLsF,KAAM,CAACid,EAAelmB,GAAI8K,EAAS9B,EAAOC,EAAM7K,GAChDkM,KAAMQ,KAGPR,CACX,CAEI,OADAnL,EAAOH,MAAM,uCACN,IAEf,GAAC,CAAAwK,IAAA,cAAAC,MACD,SAAYa,GACR,IAAKhC,EAAK2C,QAAQX,GAAO,CACrB,IAAIiB,EAAW3N,KAAK4N,SAASlB,GAC7B,OAAKiB,EAIM3N,KAAK4O,YAAYjB,IAHxBpM,EAAOH,MAAM,eAAiBsL,EAAO,wBAC9B,EAIf,CACA,GAAI1M,KAAKqnB,eAAgB,CACrB,GAAI3a,EAAKpB,OAEL,OADA/J,EAAOH,MAAM,mCACN,EAEX,IAAI8L,EAAUR,EAAKtK,GACf6L,EAAYvB,EAAKnB,OAAOnJ,GACxBqK,EAAczM,KAAK4N,SAASK,GAYhC,OAXAjO,KAAKoI,KAAKof,cAAc/a,GACxBzM,KAAKoI,KAAKwG,YAAYlC,GACtB1M,KAAKyP,KAAKb,YAAYlC,GACtB1M,KAAKoJ,OAAOA,SACZpJ,KAAKoI,KAAKxH,MAAK,GACfZ,KAAKoI,KAAKsf,iBAAiBjb,GAC3BzM,KAAKqX,oBAAoB1W,EAAUG,KAAM,CACrCiF,IAAK,cACLsF,KAAM,CAAC6B,GACPR,KAAMuB,KAEH,CACX,CAEI,OADA1M,EAAOH,MAAM,wCACN,CAEf,GAAC,CAAAwK,IAAA,cAAAC,MACD,SAAYqB,EAAS9B,GACjB,GAAIpL,KAAKqnB,eACL,GAAIkB,EAAM7gB,KAAKC,SAASyD,GACpB7J,EAAOJ,KAAK,oCADhB,CAIA,IAAIuL,EAAO1M,KAAK4N,SAASV,GACzB,GAAMR,EAAM,CACR,GAAIA,EAAKtB,QAAUA,EAGf,OAFA7J,EAAOL,KAAK,wBACZlB,KAAKoI,KAAK+W,YAAYzS,GAG1BA,EAAKtB,MAAQA,EACbpL,KAAKoI,KAAK+W,YAAYzS,GACtB1M,KAAKoJ,OAAOA,SACZpJ,KAAKoI,KAAKxH,MAAK,GACfZ,KAAKqX,oBAAoB1W,EAAUG,KAAM,CACrCiF,IAAK,cACLsF,KAAM,CAAC6B,EAAS9B,GAChBsB,KAAMQ,GAEd,CAjBA,MAmBA3L,EAAOH,MAAM,sCAGrB,GAAC,CAAAwK,IAAA,YAAAC,MACD,SAAUqB,EAASc,EAAWC,EAAWzC,GACrC,GAAIxL,KAAKqnB,eAAT,CACI,IAAI3a,EAAO1M,KAAK4N,SAASV,GACrBsb,EAAexoB,KAAKyP,KAAKgZ,UAAU/b,EAAMsB,EAAWC,EAAWzC,GAC7Dgd,IACFxoB,KAAKoI,KAAK+W,YAAYqJ,GACtBxoB,KAAKoJ,OAAOA,SACZpJ,KAAKoI,KAAKxH,MAAK,GACfZ,KAAKqX,oBAAoB1W,EAAUG,KAAM,CACrCiF,IAAK,YACLsF,KAAM,CAAC6B,EAASc,EAAWC,EAAWzC,GACtCkB,KAAMQ,IAMlB,MAFI3L,EAAOH,MAAM,sCAGrB,GAAC,CAAAwK,IAAA,cAAAC,MACD,SAAYa,GACR,IAAKhC,EAAK2C,QAAQX,GAAO,CACrB,IAAIiB,EAAW3N,KAAK4N,SAASlB,GAC7B,OAAKiB,EAIM3N,KAAK6e,YAAYlR,QAHxBpM,EAAOH,MAAM,eAAiBsL,EAAO,sBAK7C,CACK1M,KAAKoJ,OAAOqV,WAAW/R,KAG5B1M,KAAKyP,KAAKzC,SAAWN,EACrB1M,KAAKoI,KAAKyW,YAAYnS,GACtB1M,KAAKqX,oBAAoB1W,EAAUI,OAAQ,CAAEgF,IAAK,cAAesF,KAAM,GAAIqB,KAAMA,EAAKtK,KAC1F,GAAC,CAAAwJ,IAAA,oBAAAC,MACD,WACI,OAAM7L,KAAKyP,KACAzP,KAAKyP,KAAKzC,SAEV,IAEf,GAAC,CAAApB,IAAA,eAAAC,MACD,WACU7L,KAAKyP,OACPzP,KAAKyP,KAAKzC,SAAW,KACrBhN,KAAKoI,KAAK+e,eAElB,GAAC,CAAAvb,IAAA,kBAAAC,MACD,SAAgBa,GACZ,OAAO1M,KAAKoJ,OAAOqV,WAAW/R,EAClC,GAAC,CAAAd,IAAA,wBAAAC,MACD,SAAsBa,GAClB,GAAKhC,EAAK2C,QAAQX,GASlB1M,KAAKoI,KAAKsY,YAAYhU,OATtB,CACI,IAAIiB,EAAW3N,KAAK4N,SAASlB,GACxBiB,EAGD3N,KAAK0oB,sBAAsB/a,GAF3BpM,EAAOH,MAAM,eAAiBsL,EAAO,sBAK7C,CAEJ,GAAC,CAAAd,IAAA,mBAAAC,MACD,SAAiBa,GACb,IAAKhC,EAAK2C,QAAQX,GAAO,CACrB,IAAIiB,EAAW3N,KAAK4N,SAASlB,GAC7B,OAAKiB,EAIM3N,KAAKglB,iBAAiBrX,QAH7BpM,EAAOH,MAAM,eAAiBsL,EAAO,sBAK7C,CACA,GAAIA,EAAKpB,OACL,OAAO,KAEX,IAAI5I,EAAI,KACR,GAAIgK,EAAKnB,OAAOD,OAIZ,IAHA,IAAIhJ,EAAIoK,EAAKnB,OAAOG,SAChBid,EAAO,KACPC,EAAK,KACAzlB,EAAI,EAAGA,EAAIb,EAAEpC,OAAQiD,IAC1BylB,EAAKtmB,EAAEa,GACHuJ,EAAKlB,YAAcod,EAAGpd,YAClBkB,EAAKtK,KAAOwmB,EAAGxmB,KACfM,EAAIimB,GAERA,EAAOC,QAIflmB,EAAI1C,KAAKyP,KAAK5B,gBAAgBnB,GAElC,OAAOhK,CACX,GAAC,CAAAkJ,IAAA,kBAAAC,MACD,SAAgBa,GACZ,IAAKhC,EAAK2C,QAAQX,GAAO,CACrB,IAAIiB,EAAW3N,KAAK4N,SAASlB,GAC7B,OAAKiB,EAIM3N,KAAKmlB,gBAAgBxX,QAH5BpM,EAAOH,MAAM,eAAiBsL,EAAO,sBAK7C,CACA,GAAIA,EAAKpB,OACL,OAAO,KAEX,IAAI5I,EAAI,KACR,GAAIgK,EAAKnB,OAAOD,QAIZ,IAHA,IAAIhJ,EAAIoK,EAAKnB,OAAOG,SAChBmd,GAAQ,EACRD,EAAK,KACAzlB,EAAI,EAAGA,EAAIb,EAAEpC,OAAQiD,IAE1B,GADAylB,EAAKtmB,EAAEa,GACHuJ,EAAKlB,YAAcod,EAAGpd,UAAW,CACjC,GAAIqd,EAAO,CACPnmB,EAAIkmB,EACJ,KACJ,CACIlc,EAAKtK,KAAOwmB,EAAGxmB,KACfymB,GAAQ,EAEhB,OAGJnmB,EAAI1C,KAAKyP,KAAK1B,eAAerB,GAEjC,OAAOhK,CACX,GAAC,CAAAkJ,IAAA,iBAAAC,MACD,SAAeqB,EAAS4b,EAAUC,GAC9B,IAAI/oB,KAAKqnB,eAaL,OADA9lB,EAAOH,MAAM,uCACN,KAZP,IAAIsL,EAAO1M,KAAKyP,KAAK7B,SAASV,GACxBR,IACIoc,IACFpc,EAAKrB,KAAK,oBAAsByd,GAE9BC,IACFrc,EAAKrB,KAAK,oBAAsB0d,GAEpC/oB,KAAKoI,KAAK6U,wBAAwBvQ,GAM9C,GAAC,CAAAd,IAAA,sBAAAC,MACD,SAAoBqB,EAAS8K,EAAMgR,EAAQzlB,GACvC,IAAIvD,KAAKqnB,eAmBL,OADA9lB,EAAOH,MAAM,uCACN,KAlBP,IAAIsL,EAAO1M,KAAKyP,KAAK7B,SAASV,GACxBR,IACIsL,IACFtL,EAAKrB,KAAK,aAAe2M,GAEvBgR,IACFtc,EAAKrB,KAAK,eAAiB2d,GAEzBzlB,IACFmJ,EAAKrB,KAAK,cAAgB9H,GAE9BvD,KAAKoI,KAAK6U,wBAAwBvQ,GAClC1M,KAAKoI,KAAK+W,YAAYzS,GACtB1M,KAAKoJ,OAAOA,SACZpJ,KAAKoI,KAAKxH,MAAK,GAM3B,GAAC,CAAAgL,IAAA,4BAAAC,MACD,SAA0BqB,EAAS+b,EAAO9c,EAAOC,EAAQ8c,GACrD,IAAIlpB,KAAKqnB,eAsBL,OADA9lB,EAAOH,MAAM,uCACN,KArBP,IAAIsL,EAAO1M,KAAKyP,KAAK7B,SAASV,GACxBR,IACIuc,IACFvc,EAAKrB,KAAK,oBAAsB4d,GAE9B9c,IACFO,EAAKrB,KAAY,MAAIc,GAEnBC,IACFM,EAAKrB,KAAa,OAAIe,GAEpB8c,IACFxc,EAAKrB,KAAK,uBAAyB6d,GAEvClpB,KAAKoI,KAAK6U,wBAAwBvQ,GAClC1M,KAAKoI,KAAK+W,YAAYzS,GACtB1M,KAAKoJ,OAAOA,SACZpJ,KAAKoI,KAAKxH,MAAK,GAM3B,GAAC,CAAAgL,IAAA,+BAAAC,MACD,SAA6BqB,EAASgc,GAClC,IAAIlpB,KAAKqnB,eAiBL,OADA9lB,EAAOH,MAAM,uCACN,KAhBP,IAAIsL,EAAO1M,KAAKyP,KAAK7B,SAASV,GAC9B,GAAMR,EAAM,CACR,IAAKA,EAAKrB,KAAK,oBAIX,OAHA9J,EAAOH,MACH,sEAEG,KAEXsL,EAAKrB,KAAK,uBAAyB6d,EACnClpB,KAAKoI,KAAK6U,wBAAwBvQ,GAClC1M,KAAKoI,KAAK+W,YAAYzS,GACtB1M,KAAKoJ,OAAOA,SACZpJ,KAAKoI,KAAKxH,MAAK,EACnB,CAKR,GAAC,CAAAgL,IAAA,SAAAC,MACD,WACI7L,KAAKoI,KAAKvH,QACd,GAAC,CAAA+K,IAAA,qBAAAC,MAED,SAAmB8M,GACS,mBAAbA,GACP3Y,KAAKqmB,cAAc/Y,KAAKqL,EAEhC,GAAC,CAAA/M,IAAA,uBAAAC,MACD,WACI7L,KAAKqmB,cAAgB,EACzB,GAAC,CAAAza,IAAA,sBAAAC,MACD,SAAoBnH,EAAM2G,GACtB,IAAI8d,EAAInpB,KACR6B,EAAEE,EAAE0jB,YAAW,WACX0D,EAAEC,qBAAqB1kB,EAAM2G,EAChC,GAAE,EACP,GAAC,CAAAO,IAAA,uBAAAC,MACD,SAAqBnH,EAAM2G,GAEvB,IADA,IAAIge,EAAIrpB,KAAKqmB,cAAcnmB,OAClBiD,EAAI,EAAGA,EAAIkmB,EAAGlmB,IACnBnD,KAAKqmB,cAAcljB,GAAGuB,EAAM2G,EAEpC,IAAC,CAAA,CAAAO,IAAA,OAAAC,MAED,SAAY0I,EAAS9E,GACjBlO,EAAOJ,KACH,6GAEJ,IAAIujB,EAAM,IAAIuB,EAAO1R,GAErB,OADAmQ,EAAI9jB,KAAK6O,GACFiV,CACX,IAAC,CAhrBsB,UAgrBtB4E,EAhrBgBrD,EAAM,OACTrZ,GAAI0c,EADDrD,EAAM,OAETvb,GAAI4e,EAFDrD,EAAM,YAGJ9lB,GAASmpB,EAHXrD,EAAM,aAIHtlB,GAAS2oB,EAJZrD,EAAM,IAKZpkB,GAACynB,EALKrD,EAAM,SAMPJ,GAAMyD,EANLrD,EAAM,mBDrBpB,SAAkBxb,GACrB,KAAMA,aAAkBob,GACpB,MAAM,IAAIC,MAAM,4DAKpBR,EAAYC,QAAQjY,KAAK7C,EAC7B,ICoB6C6e,EAPxBrD,EAAM,OAQTsC"}