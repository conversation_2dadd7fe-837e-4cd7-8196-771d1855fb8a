import {
  Button, Dialog, Form, FormItemProps, FormStore, Icon, Popover,
} from '@weapp/ui';
import { getLabel } from '@weapp/utils';
import { toJS } from 'mobx';
import React, { PureComponent } from 'react';
import utils from '../../../utils';
import './index.less';
import { IComCategory } from './types';

interface AddIconProps {
  onChange: () => void;
}

interface AddIconStates {
  visible: boolean;
}

const items: FormItemProps = {
  type: {
    itemType: 'INPUT',
    placeholder: `${getLabel('221747','请输入组件类型，如：RichText')}`,
    required: true,
    autoFocus: true,
    errorType: 'popover',
  },
  package: {
    itemType: 'INPUT',
    placeholder: `${getLabel('221739','请输入组件包名，如：@weapp/ebdcoms')}`,
    required: true,
    errorType: 'popover',
  },
  name: {
    itemType: 'INPUT',
    placeholder: `${getLabel('221745','请输入组件名称，如：富文本')}`,
    required: true,
    errorType: 'popover',
  },
  icon: {
    itemType: 'INPUT',
    placeholder: `${getLabel('221743','请输入图片路径或IconNames，如：Icon-empty-Business-form')}`,
    required: true,
    errorType: 'popover',
  },
  category: {
    itemType: 'INPUT',
    placeholder: `${getLabel('221744','请输入组件分组，如：基础组件。若组件无分组则不填')}`,
    errorType: 'popover',
  },
};

const layout = [
  [
    {
      id: 'type',
      label: `${getLabel('221750','组件类型')}`,
      labelSpan: 6,
      items: ['type'],
      hide: false,
    },
  ],
  [
    {
      id: 'package',
      label: `${getLabel('221748','组件包名')}`,
      labelSpan: 6,
      items: ['package'],
      hide: false,
    },
  ],
  [
    {
      id: 'name',
      label: `${getLabel('72652','组件名称')}`,
      labelSpan: 6,
      items: ['name'],
      hide: false,
    },
  ],
  [
    {
      id: 'icon',
      label: `${getLabel('221749','组件图片')}`,
      labelSpan: 6,
      items: ['icon'],
      hide: false,
    },
  ],
  [
    {
      id: 'category',
      label: `${getLabel('221741','组件分组')}`,
      labelSpan: 6,
      items: ['category'],
      hide: false,
    },
  ],
];

export default class AddCom extends PureComponent<AddIconProps, AddIconStates> {
  formStore: FormStore = new FormStore();

  constructor(props: AddIconProps) {
    super(props);

    this.state = {
      visible: false,
    };

    this.formStore.initForm({
      data: {},
      items,
      layout,
      groups: [],
    });
  }

  changeVisble = () => {
    this.setState((prev) => ({ visible: !prev.visible }));
  };

  onSure = () => {
    const formData = toJS(this.formStore.getFormDatas());

    formData.id = utils.UUID();
    formData.terminalScope = ['PC', 'MOBILE'];
    this.formStore.validate().then((res: any) => {
      const { errors } = res;

      if (Object.keys(errors).length === 0) {
        this.addCom(formData);
        this.changeVisble();
        this.props.onChange();
        this.formStore.resetForm();
      }
    });
  };

  onCancel = () => {
    this.changeVisble();
    this.formStore.resetForm();
  };

  addCom = (formData: any) => {
    const datas = JSON.parse(localStorage.getItem('datas') as string) || [];

    if (!formData.category) {
      if (!datas.some((item: IComCategory) => item.title === getLabel('221740','无分组组件'))) {
        datas.splice(0, 0, {
          id: utils.UUID(),
          title: getLabel('221740','无分组组件'),
          comps: [formData],
        });
      } else {
        datas.map((item: IComCategory) => {
          if (item.title === getLabel('221740','无分组组件')) {
            item.comps?.push(formData);
          }
          return item;
        });
      }
    } else if (datas.some((item: IComCategory) => item.name === formData.category)) {
      datas.map((item: IComCategory) => {
        if (item.name === formData.category) {
          item.comps?.push(formData);
        }
        return item;
      });
    } else {
      datas.push({
        id: utils.UUID(),
        name: formData.category,
        title: formData.category,
        comps: [formData],
      });
    }

    localStorage.setItem('datas', JSON.stringify(datas));
  };

  render() {
    const { visible } = this.state;
    return (
      <>
        <Popover
          weId={`${this.props.weId || ''}_hccvlq`}
          popoverType="tooltip"
          placement="bottom"
          popup={<span>{getLabel('156581','新建组件')}</span>}
        >
          <span onClick={this.changeVisble}>
            <Icon weId={`${this.props.weId || ''}_jvcxez`} name="Icon-add-to01" size="xs" />
          </span>
        </Popover>
        <Dialog
          weId={`${this.props.weId || ''}_p401ie`}
          title={`${getLabel('90837','新增组件')}`}
          closable
          width={480}
          onClose={this.onCancel}
          destroyOnClose
          visible={visible}
          mask
          footer={[
            <Button
              weId={`${this.props.weId || ''}_9le4rz`}
              key="sure"
              type="primary"
              onClick={this.onSure}
            >
              {getLabel('40565', '确定')}
            </Button>,
            <Button weId={`${this.props.weId || ''}_4assuu`} key="cancel" onClick={this.onCancel}>
              {getLabel('53937', '取消')}
            </Button>,
          ]}
        >
          <Form weId={`${this.props.weId || ''}_kofeue`} store={this.formStore} />
        </Dialog>
      </>
    );
  }
}
