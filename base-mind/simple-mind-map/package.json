{"name": "simple-mind-map", "version": "0.10.2-fix.1", "description": "思维导图", "types": "./types/index.d.ts", "typings": "./types/index.d.ts", "license": "MIT", "type": "module", "scripts": {"lint": "eslint src/", "format": "prettier --write .", "types": "npx -p typescript tsc index.js --declaration --allowJs --emitDeclarationOnly --outDir types --target es2017 --skipLibCheck", "wsServe": "node ./bin/wsServer.mjs"}, "module": "index.js", "main": "./dist/simpleMindMap.umd.min.js", "dependencies": {"@svgdotjs/svg.js": "^3.0.16", "deepmerge": "^1.5.2", "eventemitter3": "^4.0.7", "jszip": "^3.10.1", "katex": "^0.16.8", "mdast-util-from-markdown": "^1.3.0", "pdf-lib": "^1.17.1", "quill": "^1.3.6", "tern": "^0.24.3", "uuid": "^9.0.0", "ws": "^7.5.9", "xml-js": "^1.6.11", "y-webrtc": "^10.2.5", "yjs": "^13.6.8"}, "keywords": ["javascript", "svg", "mind-map", "mindMap", "MindMap"], "devDependencies": {"eslint": "^8.25.0", "prettier": "^2.7.1"}}