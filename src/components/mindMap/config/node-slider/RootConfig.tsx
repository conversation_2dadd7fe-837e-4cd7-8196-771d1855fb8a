import { getLabel, isEqual } from '@weapp/utils';
import { FormLayoutProps } from '@weapp/ui';
import { toJS } from 'mobx';
import { NodeNames } from '../../engine/node-config/constants';
import createForm from '../ConfigForm';
import ActionSet from './action-set';
import NodeSizeSet from './node-size';
import NodeStyleSet from './node-style';
import RootConfigIconEidtor from './RootIconEidtor';
import { LocaleExByRootNode } from '../../../common/localeEx';
import ExpandItem from './InputCmp/expandItem';
import { MIND_VIEW } from '../../../../constants/enum';

export interface ConfigFormProps {
  data: any;
  page: any;
  onChange: (changedData: any) => void;
  setConfigRef?: (ref: any) => void;
}

const groups = [
  {
    id: 'showNode',
    title: getLabel('93225', '显示设置'),
    visible: true,
    custom: false,
  },
  {
    id: 'nodeSetGroup',
    title: getLabel('87164', '节点设置'),
    visible: true,
    custom: false,
  },
  {
    id: 'iconSetGroup',
    title: getLabel('238286', '图标设置'),
    visible: true,
    custom: false,
  },
];
const getGroups = (props: any) => {
  const { data, rootProps } = props;
  const fullGroups = groups.filter((group) => {
    let show = true;
    // 非显示设置的隐藏配置 开启后才支持显示
    if (data.isHideRoot && (group.id === 'nodeSetGroup' || group.id === 'iconSetGroup')) {
      show = false;
    }
    // 根节点
    if (`${data.floor}` === '1') {
      // 鱼骨图和双向屏蔽隐藏根节点
      const mindLayout = rootProps.config?.mindLayout;
      if (mindLayout === MIND_VIEW.FISHBONE || mindLayout === MIND_VIEW.MIND_MAP) {
        show = false;
      }
    }
    return show;
  });
  return fullGroups;
};
const RootConfig = createForm({
  groups,
  items: {
    [NodeNames.IsHideRoot]: {
      label: getLabel('147347', '是否隐藏'),
      labelSpan: 8,
      itemType: 'SWITCH',
      layoutWrapClassName: 'isHideRoot',
      groupId: 'showNode',
      helpTip: getLabel('300986', '鱼骨图和双向布局暂不支持隐藏根节点，开启后无效果。')
    },
    [NodeNames.Name]: {
      label: getLabel('56091', '显示名称'),
      labelSpan: 8,
      itemType: 'CUSTOM',
      value: '',
      placeholder: getLabel('87166', '节点名称'),
      customRender: LocaleExByRootNode,
      groupId: 'nodeSetGroup',
    },
    [NodeNames.NameShowSwitch]: {
      label: getLabel('56091', '显示名称'),
      labelSpan: 8,
      itemType: 'SWITCH',
      defaultValue: true,
      groupId: 'nodeSetGroup',
    },
    [NodeNames.DefaultExpendDepth]: {
      label: getLabel('56111', '展开级数'),
      labelSpan: 8,
      groupId: 'nodeSetGroup',
      itemType: 'CUSTOM',
      customRender: ExpandItem,
    },
    [NodeNames.ActionSet]: {
      // 节点动作
      label: getLabel('87169', '节点动作'),
      labelSpan: 8,
      itemType: 'CUSTOM',
      value: '',
      style: { width: '100%' },
      groupId: 'nodeSetGroup',
      customRender: ActionSet,
    },
    [NodeNames.NodeSize]: {
      // 节点动作
      label: getLabel('245996', '节点尺寸'),
      labelSpan: 8,
      itemType: 'CUSTOM',
      value: '',
      style: { width: '100%' },
      groupId: 'nodeSetGroup',
      customRender: NodeSizeSet,
    },
    [NodeNames.NodeStyle]: {
      // 节点动作
      label: getLabel('247418', '节点样式'),
      labelSpan: 8,
      itemType: 'CUSTOM',
      value: '',
      style: { width: '100%' },
      groupId: 'nodeSetGroup',
      customRender: (props: any) => {
        return <NodeStyleSet weId={`${props.weId || ''}_ywecdm`} isRoot={true} {...props} />;
      },
    },
    //图标设置
    [NodeNames.iconInfo]: {
      // 图标
      label: getLabel('56345', '图标'),
      labelSpan: 8,
      itemType: 'CUSTOM',
      value: '',
      style: { width: '100%' },
      groupId: 'iconSetGroup',
      customRender: RootConfigIconEidtor,
    },
    [NodeNames.IconHeight]: {
      // 图标高度
      label: getLabel('54597', '高度'),
      labelSpan: 8,
      itemType: 'CUSTOM',
      groupId: 'iconSetGroup',
      customRender: (props: any) => <ExpandItem weId={`${props.weId || ''}_j57nno`} {...props} value={props.value || 60} min={0} max={300} hideOps={false} align={'right'} suffix={'px'} placeholder={''} />,
    },
    [NodeNames.IconWidth]: {
      // 图标宽度
      label: getLabel('54578', '宽度'),
      labelSpan: 8,
      itemType: 'CUSTOM',
      groupId: 'iconSetGroup',
      customRender: (props: any) => <ExpandItem weId={`${props.weId || ''}_wlglee`} {...props} value={props.value || 60} min={0} max={300} hideOps={false} align={'right'} suffix={'px'} placeholder={''} />,
    },
  },
  customHide: function customHide(col: FormLayoutProps) {
    const { data, rootProps } = this.props;
    const _groups = getGroups(this.props);
    if (!isEqual(_groups, toJS(this.formStore.groups))) {
      this.formStore.setState({ groups: _groups });
    }
     // 根节点
     if (`${data.floor}` === '1') {
      // 鱼骨图和双向屏蔽隐藏根节点
      const mindLayout = rootProps.config?.mindLayout;
      if ((mindLayout === MIND_VIEW.FISHBONE || mindLayout === MIND_VIEW.MIND_MAP) && col.id === NodeNames.IsHideRoot) {
        return { ...col, hide: true }
      }
    }
    return col;
  },
});

export default RootConfig;
