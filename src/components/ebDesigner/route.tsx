import React, { ComponentProps } from 'react';
import {
  Route, RouteComponentProps, Switch, with<PERSON>out<PERSON>,
} from 'react-router-dom';
import { formatParentPath } from '../../utils';
import { EbD<PERSON>er, EbF<PERSON><PERSON><PERSON><PERSON>, Eb<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Doc, EbQuickCreate } from './index';

function RouteEbDesigner(props: RouteComponentProps & ComponentProps<any>) {
  const parentPath: string = formatParentPath(props);
  return (
    <Switch weId={`${props.weId || ''}_u2e66r`}>
      <Route
        weId={`${props.weId || ''}_zutemg`}
        path={`${parentPath}/ebddesigner/:pageId`}
        component={EbDesigner}
        exact
      />
      <Route weId={`${props.weId || ''}_h589yl`}
        path={`${parentPath}/ebddesigner/preview/pc/:pageId`}
        component={EbDesignerViewPage}
      />
      <Route weId={`${props.weId || ''}_vbva8a`} path={`${parentPath}/ebFormEngine`} component={EbFormEngine} exact />
      <Route weId={`${props.weId || ''}_vbva8a`} path={`${parentPath}/ebFormEngine/:apid/form/view/:objId`} component={EbFormEngine} />
      <Route weId={`${props.weId || ''}_vbva8a`} path={`${parentPath}/quickCreate`} component={EbQuickCreate} />
      <Route weId={`${props.weId || ''}_vbva8a`} path={`${parentPath}/doc`} component={Doc} />
    </Switch>
  );
}

export default withRouter(RouteEbDesigner);
