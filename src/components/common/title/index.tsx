/**
 * @created :
 * <AUTHOR> ljc
 * @desc    : 组件标题的复写，自定义头部图标
 */

import React, { Attributes, PureComponent, ReactNode } from 'react';
import { Title, TitleProps } from '@weapp/ui';
import { classnames } from '@weapp/utils';
import { observer } from 'mobx-react';
import { When } from 'react-if';
import AssetsItem from '../assets-item';
import './index.less';

interface TitleNewProps extends Attributes, TitleProps {
  appId: string;
  iconSize?: string;
  hideIcon?: boolean; // 隐藏图标
  customLeftContent?: ReactNode | (() => ReactNode);
}

const prefixCls = 'ui-title';

@observer
export default class TitleNew extends PureComponent<TitleNewProps> {
  customRenderLeft = (dom?: JSX.Element) => {
    const {
      menus, customLeftContent, appId, iconSize, hideIcon,
    } = this.props;
    if (customLeftContent) {
      return (
        <div className={`${prefixCls}-left-custom-content`}>
          {typeof customLeftContent === 'function' ? customLeftContent() : customLeftContent}
        </div>
      );
    }

    return (
      <TitleLeftCustom
        weId={`${this.props.weId || ''}_bssvk6`}
        dom={dom}
        menus={menus}
        appId={appId}
        size={iconSize}
        hideIcon={hideIcon}
      />
    );
  };

  render() {
    return (
      <Title
        weId={`${this.props.weId || ''}_imk65l`}
        {...this.props}
        noIcon
        customRenderLeft={this.customRenderLeft}
      />
    );
  }
}

export function TitleLeftCustom(props: any) {
  const {
    menus, dom, size = 's', appId, hideIcon = false,
  } = props;
  const titleCls = classnames(`${prefixCls}-left`, {
    [`${prefixCls}-left-custom-indialog`]: size !== 's',
    [`${prefixCls}-left-tabs`]: !!menus,
    [`${prefixCls}-left-custom`]: true,
  });
  return (
    <div className={titleCls}>
      <When weId={`${props.weId || ''}_72z71u`} condition={!hideIcon}>
        <div className={`${prefixCls}-icon ${prefixCls}-icon-custom`}>
          <AssetsItem weId={`${props.weId || ''}_b8fyxt`} appId={appId} size={size} />
        </div>
      </When>
      <div className={`${prefixCls}-title ${prefixCls}-title-custom`}>{dom}</div>
    </div>
  );
}
