.vertical-tree-box {
  width: 100%;
  overflow: auto;
  position: relative;
  .ebdf-ds-vertical-tree-box {
    white-space: nowrap;
    padding-right: 3px;
    .ebdf-ds-vertical-tree-item,
    .ebdf-ds-vertical-tree-wrap {
      // position: relative;
      vertical-align: top;
    }
    .ebdf-ds-vertical-tree-item {
      // position: relative;
      height: 30px;
      line-height: 30px;
      width: 150px;
      margin-bottom: var(--h-spacing-lg);
      border: var(--border-solid);
      color: var(--regular-fc);
      border-radius: 3px;
      .rootdisable-box {
        position: relative;
        h6 {
          border-left: 2px solid transparent;
          padding: 0 16px;
          overflow: hidden;
          white-space: nowrap;
          text-overflow: ellipsis;
          font-weight: normal;
          margin: 0;
          cursor: pointer;
          text-align: center;
        }
      }
      /*操作按钮样式*/
      .ebdf-ds-treenode-operate-add,
      .ebdf-ds-treenode-operate-del {
        position: absolute;
        right: -21px;
        top: -1px;
        font-size: calc(12 * 1px);
        z-index: 1;
        cursor: pointer;

        svg {
          width: 18px;
          height: 18px;
        }
      }
      .ebdf-ds-treenode-operate-del {
        right: -38px;
      }
    }

    .ebdf-ds-vertical-tree-wrap {
      position: relative;
      // overflow: hidden;
      margin-left: -4px;

      .ebdf-ds-vertical-tree-box {
        margin-left: 30px;

        &:first-child > .ebdf-ds-vertical-tree-item {
          // &:before {
          //   position: absolute;
          //   left: -18px;
          //   top: -10000px;
          //   bottom: 18px;
          //   border-left: 1px solid #f1f1f1;
          //   content: '';
          // }

          &:after {
            position: absolute;
            top: 15px;
            left: 15px;
            width: 15px;
            border-top: var(--border-solid);
            content: '';
          }
        }

        & + .ebdf-ds-vertical-tree-box > .ebdf-ds-vertical-tree-item {
          // &:before {
          //   position: absolute;
          //   left: -18px;
          //   top: -10000px;
          //   bottom: 18px;
          //   border-left: 1px solid #f1f1f1;
          //   content: '';
          // }

          &:after {
            position: absolute;
            bottom: 15px;
            left: 15px;
            width: 15px;
            border: var(--border-solid);
            border-width: 0 0 1px 1px;
            content: '';
          }
        }
        .ebdf-ds-vertical-tree-box > .ebdf-ds-vertical-tree-item {
          // &:before {
          //   position: absolute;
          //   left: -18px;
          //   top: -10000px;
          //   bottom: 18px;
          //   border-left: 1px solid #f1f1f1;
          //   content: '';
          // }
        }
      }
    }
    .line {
      position: absolute;
      width: 0px;
      border-left: var(--border-solid);
      height: 100%;
      left: 15px;
      bottom: 10px;
      top: -16px;
    }
  }
}
