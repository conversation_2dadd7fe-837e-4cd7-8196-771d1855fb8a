@import (reference) "../../../style/prefix.less";

.mindmap-tree-warp {
    width: auto;
    min-width: 100%;
    .ui-tree-bar {
        height: 40px;
    }
    .ui-tree-bar:hover,
    .ui-tree-bar.isSelected {
        background: #efefef;
    }
    .ui-list-item {
        &:hover {
            cursor: pointer;
        }
    }
    .mindmap-tree-warp-node {
        display: flex;
        flex-direction: row;
        width: 100%;
        .node-left {
            flex: 1;
            align-items: center;
            display: flex;
        }
        .node-right {
            margin-right: 10px;
            color: var(--primary);
            .treenode-operate-add:not(:first-child) {
                margin-left: var(--h-spacing-sm);
            }
        }
    }
    .ui-list-item:hover {
        background: #efefef;
    }
    .ui-list-body div:not(:last-child) .ui-list-content {
        border-bottom: 0;
    }
    .ui-list-grid-expandable-btn-switcher:after {
        top: -1px;
        position: relative;
    }
    .mindmap-tree-warp-sort-handle-disabled {
        color: var(--secondary-fc);
    }
    .mindmap-tree-warp-sort-handle {
        margin-right: 5px;
    }
}

.@{prefix}-mindmap-config-btn {
    width: 100%;
    & > button {
        width: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
    }
}

.ebcoms-config-MindMap {
    .@{prefix}-field-setting-btn,
    .ui-btn {
        width: 100%;
        display: flex;
        justify-content: center;
    }
    .isHideRoot{
        .ui-formItem-label-span{
            position: relative;
            top: 2px;
        }
    }
}

.@{prefix}-funcSetting {
    &-icon{
        color:var(--secondary-fc);
        cursor: pointer;
        display: flex;
        justify-content: center;
        transition: all .3s;
        padding-right: 8px;
        &:hover{
            color: var(--primary);
        }
    }
}
.@{prefix}-fucsetting-icon{
    color:var(--secondary-fc)
}

