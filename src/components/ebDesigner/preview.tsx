import React from 'react';
import { CorsComponent } from '@weapp/ui';
import { withRouter } from 'react-router-dom';

interface FlowDesignerDemoStates {
}
class FlowDesignViewDemo extends React.PureComponent<any, FlowDesignerDemoStates> {
    render() {
        const { pageId } = this.props.match.params;
        if (!pageId) return
        return <CorsComponent weId={`${this.props.weId || ''}_no38e1`}
            app="@weapp/ebdpage"
            compName="PageView"
            pageId={pageId}
            // type={'EB_FORM_VIEW'}
            // type={'EB_PAGE'}
        />
    }
}

export default withRouter(FlowDesignViewDemo);
