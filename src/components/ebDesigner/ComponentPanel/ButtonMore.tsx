import { Icon, Menu } from '@weapp/ui';
import React, { PureComponent } from 'react';
import { IComCategory, IComDescription } from './types';
import { getLabel } from '@weapp/utils';

interface DeleteComProps {
  onChange: () => void;
  data: IComDescription;
}

const dialogData = [
  { id: 'export', content: getLabel('55843','导出') },
  { id: 'delete', content: getLabel('53951','删除') },
];

export default class DeleteCom extends PureComponent<DeleteComProps> {
  onDeleteCom = () => {
    const { data, onChange } = this.props;
    const datas = JSON.parse(localStorage.getItem('datas') as string);
    const newDatas = datas
      .map((items: IComCategory) => {
        items.items = items.comps?.filter(
          (item: IComDescription) => JSON.stringify(item) !== JSON.stringify(data),
        );
        if (items.items.length === 0) {
          return null;
        }

        return items;
      })
      .filter((item: IComCategory) => item);

    localStorage.setItem('datas', JSON.stringify(newDatas));
    onChange();
  };

  onExportCom = () => {};

  onChange = (value: any) => {
    if (value === 'delete') {
      this.onDeleteCom();
    }
    if (value === 'export') {
      this.onExportCom();
    }
  };

  render() {
    return (
      <Menu
        weId={`${this.props.weId || ''}_nbhhsv`}
        data={dialogData}
        type="select"
        selectType="normal"
        triggerProps={{}}
        customSelectContent={
          <span>
            <Icon weId={`${this.props.weId || ''}_3eyiyt`} name="Icon-more" />
          </span>
        }
        onChange={this.onChange}
      />
    );
  }
}
