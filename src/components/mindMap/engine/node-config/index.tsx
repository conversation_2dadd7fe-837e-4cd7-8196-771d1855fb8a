import { <PERSON><PERSON>, <PERSON><PERSON>, FormStore } from '@weapp/ui';
import { classnames, getLabel } from '@weapp/utils';
import { observer } from 'mobx-react';
import React, { PureComponent } from 'react';
import { Else, If, Then } from 'react-if';
import GroupCard from '../../../common/group-card';
import { DataSetItem } from '../../../common/plugins/DataSet/types';
import ActionSetPanel from '../action-set/ActionSetPanel';
import BaseForm from './BaseForm';
import {dlgIconName} from '../../../../constants';
import './index.less';
interface ConfigDialogProps {
  visible: boolean;
  onCancel: () => void;
  selector: any;
  doSave: () => void;
  afterFormInit: (store: FormStore) => void;
  fieldChange: (data: any) => void;
  actions: any[];
  setActions: (actions: any[]) => void;
  appId: string;
  dataset: DataSetItem;
  leftContent?: React.ReactNode;
  refreshKey?: string;
  isSave?: boolean;
  isEngine?: boolean;
}

@observer
class ConfigDialog extends PureComponent<ConfigDialogProps & React.Attributes> {
  render() {
    const {
      visible,
      onCancel,
      selector = {},
      doSave,
      afterFormInit,
      fieldChange,
      setActions,
      actions,
      appId,
      dataset,
      leftContent,
      refreshKey = '',
      isSave,
      isEngine
    } = this.props;
    const { name = '' } = selector;
    const dom = (
      <div className="mindmap-baseinfo-content">
        <GroupCard weId={`${this.props.weId || ''}_jisw7e`} title={getLabel('54061', '基本信息')}>
          <BaseForm
            weId={`${this.props.weId || ''}_sstcp9`}
            selector={selector}
            appId={appId}
            afterInit={afterFormInit}
            onChange={fieldChange}
            key={refreshKey}
            isEngine={isEngine}
          />
        </GroupCard>
        {dataset?.id && (
          <ActionSetPanel
            weId={`${this.props.weId || ''}_ocn0pu`}
            actions={actions}
            onSave={setActions}
            appId={appId}
            selectedFormId={dataset?.id}
            formName={dataset?.text}
            dataset={dataset}
            isEngine={isEngine}
          />
        )}
      </div>
    );
    const hasLeftContent = !!leftContent;
    const nameText = typeof name === 'string' ? name : name.nameAlias;
    return (
      <Dialog
        weId={`${this.props.weId || ''}_mvyzz5`}
        visible={visible}
        onClose={onCancel}
        title={`${getLabel('87164', '节点设置')}:${nameText}`}
        closable
        destroyOnClose
        placement="right"
        noMaskClose
        resize
        width={800}
        minResizeWidth={800}
        maxResizeWidth={1400}
        icon={dlgIconName}
        className={classnames({ 'mindmap-baseinfo-wrapper-dlg': hasLeftContent })}
        buttons={
          <Button
            weId={`${this.props.weId || ''}_yll2be`}
            type="primary"
            onClick={doSave}
            disabled={!isSave}
          >
            {getLabel('40496', '保存')}
          </Button>
        }
      >
        <If weId={`${this.props.weId || ''}_htgic0`} condition={hasLeftContent}>
          <Then weId={`${this.props.weId || ''}_uqgc2p`}>
            <div className="mindmap-baseinfo-wrapper">
              <div className="mindmap-baseinfo-content-left">{leftContent}</div>
              <div className="mindmap-baseinfo-content-right">{dom}</div>
            </div>
          </Then>
          <Else weId={`${this.props.weId || ''}_4ol93l`}>{dom}</Else>
        </If>
      </Dialog>
    );
  }
}

export default ConfigDialog;
