// import Loadable from '../react-loadable';
import Loadable from '../utils/react-loadable';
import { MindMapType } from './types';
const mindMapClsPrefix = 'ui-mind-map';

const MindMap = Loadable({
  name: 'MindMap',
  loader: () => import(
    /* webpackChunkName: "ebdmindmap_mindMap" */
    './MindMap'
  ),
}) as MindMapType;

MindMap.defaultProps = {
  prefixCls: mindMapClsPrefix,
  draggable: false,
  datas: {
    meta: {
      name: '',
    },
    format: 'node_array',
    data: [],
  },
};

export type { MindFormatType, MindMapProps, MindMapType } from './types';

export default MindMap;
