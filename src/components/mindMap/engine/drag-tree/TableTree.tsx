import { observer } from 'mobx-react';
import React from 'react';
import TreeNode from './TreeNode';
import { TableTreeProps } from './types';

@observer
class TableTree extends React.PureComponent<TableTreeProps> {
  renderNode(node: any) {
    const {
      onRemove, onNodeOptClick, selector, onCreate,
    } = this.props;
    return (
      <TreeNode
        weId={`${this.props.weId || ''}_57bvjf`}
        node={node}
        key={node.id}
        onRemove={onRemove}
        onNodeOptClick={onNodeOptClick}
        onCreate={onCreate}
        selector={selector}
      >
        {node.children.map((n: any) => this.renderNode(n))}
      </TreeNode>
    );
  }

  render() {
    const { data } = this.props;
    const node = data[0];
    if (!node) {
      return null;
    }
    return this.renderNode(node);
  }
}

export default TableTree;
