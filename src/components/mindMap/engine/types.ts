import { AnyObj, FormDatas } from '@weapp/ui';
import { Attributes, ReactText } from 'react';
import { MindMapEngineStore } from './store';

export interface MindMapViewProps extends Attributes {
  MindMapViewStore: MindMapEngineStore;
  data: any;
}

export interface MindMapNodeConfigProps extends Attributes {
  node: TreeData;
}

/** *
 * 根节点字段
 */
export interface MindMapRootData extends AnyObj {
  name: string;
  defaultexpenddepth: ReactText | string[];
  id: string;
  isHideRoot: string; // 是否隐藏
}

/** *
 * 配置交互信息结构
 */
export interface MindMapDataProps {
  [key: string]: TreeData;
}

export interface NodeConfigProps extends Attributes {
  node: TreeData;
  visible: boolean | undefined;
  onCancel: () => void;
  onConfirm: (data: FormDatas, floor: number) => void;
}

export type TreeData = {
  name: string;
  pid: string;
  id: string;
  floor: number; // 层级
  children?: TreeData[];
  [key: string]: any;
};
