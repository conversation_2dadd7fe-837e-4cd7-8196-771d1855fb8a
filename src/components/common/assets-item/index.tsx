/**
 * @created : 2021/2/25
 * <AUTHOR> ljc
 * @desc    : 组件：应用图标,主要pc使用，暂时没有给h5
 */
import { CorsComponent } from '@weapp/ui';
import { observer } from 'mobx-react';
import React, { Attributes, PureComponent } from 'react';

interface AssetsIconProps extends Attributes {
  className?: string;
  path: string;
  style?: any;
  size?: string; // s 24 sm 30 md 36 auto 100%
}
@observer
export class AssetsIcon extends PureComponent<AssetsIconProps> {
  render() {
    const {
      className, path, style, size = 's',
    } = this.props;
    return (
      <CorsComponent
        weId={`${this.props.weId || ''}_3766hu`}
        app="@weapp/ebdcoms"
        compName="AssetsItem"
        className={className}
        path={path}
        style={style}
        size={size}
      />
    );
  }
}

/**
 * 当前应用图标
 */
interface AssetsItemProps extends Attributes {
  className?: string;
  appId: string;
  size?: string; // s 24 sm 30 md 36 auto 100%
}
@observer
class AssetsItem extends PureComponent<AssetsItemProps> {
  render() {
    const { className, appId, size = 's' } = this.props;
    if (!appId) {
      return null;
    }
    return (
      <CorsComponent
        weId={`${this.props.weId || ''}_3766hu`}
        app="@weapp/ebdcoms"
        compName="EBAppIcon"
        className={className}
        size={size}
        appId={appId}
      />
    );
  }
}

export default AssetsItem;
