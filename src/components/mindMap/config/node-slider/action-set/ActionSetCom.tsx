import { CorsComponent, utils } from '@weapp/ui';
import { getLabel } from '@weapp/utils';
import { toJS } from 'mobx';
import { observer } from 'mobx-react';
import React from 'react';
import { ActionSetComProps } from './types';
import { NotUseFields } from "../../../../../constants";

const { isEmpty } = utils;

// 动作来源
export enum ActionFromType {
  FieldLink = 'fieldLink', // 表格链接
  Btn = 'button' // 按钮
}

export enum ActionType {
  System = 'system',
  Custom = 'custom',
  OpenPage = 'openpage',
  CustomInterface = 'customInterface',
  Esb = 'esb',
  Reply = 'reply'
}

@observer
export default class ActionSet extends React.PureComponent<ActionSetComProps, any> {
  constructor(props: ActionSetComProps) {
    super(props);
    this.state = {
      visible: false,
      targetId: `${Date.now()}`,
    };
  }

  onActionSure = (actions: any) => {
    actions = actions.map((item: any) => ({
      ...item,
      enable: item.enable ? 1 : 0,
    }));
    const { onChange } = this.props;
    const empty = isEmpty(actions); // 无链接

    if (!empty) {
      onChange(toJS(actions));
    } else {
      onChange([]);
    }
    this.closeActionSetDialog();
  };

  closeActionSetDialog = () => {
    this.setState({ targetId: '', visible: false });
  };

  handleAction = () => {
    this.setState({ targetId: `${new Date().getTime()}`, visible: true });
  };

  customRenderButton = (module: any) => {
    const { value } = this.props;
    const emptyBool = isEmpty(value);
    const { ButtonEx } = module;
    return (<ButtonEx
      weId={`${this.props.weId || ''}_3na4l8`}
      inline={false}
      onClick={this.handleAction}
    >
      {emptyBool ? (
        <span>+ {getLabel('53984', '添加动作')}</span>
      ) : (
        getLabel('56793', '编辑动作')
      )}
            </ButtonEx>);
  };

  fieldFilter = (field:any)=>{
    //屏蔽不支持字段
    if (NotUseFields.includes(field?.id || field?.componentKey)) {
      return false
    } 
    return true
  }

  render() {
    const { visible, targetId } = this.state;
    const { value, appid, config } = this.props;
    const {
      dataset = {
        id: '',
        groupId: '',
        text: '',
        type: '',
      },
    } = config;
    return (
      <>
      <CorsComponent
        weId={`${this.props.weId || ''}_pjlz66`}
        app="@weapp/ebdcoms"
        compName="ButtonEx"
        customRender={this.customRenderButton}
      />
      <CorsComponent
        weId={`${this.props.weId || ''}_hx4kyh`}
        app="@weapp/ebdform"
        compName="ActionSet"
        visible={visible}
        actionDialogSure={this.onActionSure}
        closeDialog={this.closeActionSetDialog}
        targetId={targetId}
        actionDatas={toJS(value || [])}
        appId={appid || dataset?.groupId}
        selectedFormId={dataset?.id}
        editFormData={
          {
            buttonKey: 'link',
          } as any
        }
        onSure={this.onActionSure}
        title={getLabel('53978', '动作')}
        showActionTypes={[
          // 暂时隐藏esb动作流
          ActionType.OpenPage,
          // ActionType.Reply,
          // ActionType.CustomInterface,
        ]}
        type={ActionFromType.FieldLink}
        formName={dataset?.text}
        fieldFilter={this.fieldFilter}
        useDataSetField
      />
      </>
    );
  }
}
