@import (reference) '../../../../../style/index.less';


.@{prefix}-fieldFilterSet {
  .ui-icon{
    cursor: pointer;
    color:var(--secondary-fc);
    transition: .3s;
    padding-right: 8px;
    &:hover{
      color: var(--primary);
    }
  }
  &-dialog {
    &-content {
      &-item {
        margin-bottom: 4px;

        &-label {
          font-size: var(--font-size-12);
        }
      }
    }
  }

  &-switch {
    border-radius: 4px;
    background: var(--base-white);
    margin-bottom: 10px;
    box-shadow: var(--box-shadow-spread);
    border: var(--border-solid);
    display: flex;
    justify-content: space-between;
    color: var(--main-fc);
    padding: 10px var(--v-spacing-lg);

    &-left {
      display: flex;
      flex-direction: column;
      justify-content: center;
      font-size: var(--font-size-12);
      &-label{
        margin-bottom: 4px;
      }
      &-desc{
        color: var(--secondary-fc);
      }
    }

    &-right {
      display: flex;
      align-items: center;
      margin-left: 20px;
    }
  }
}
