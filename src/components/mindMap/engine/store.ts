import { AnyObj, Dialog, FormStore } from '@weapp/ui';
import { getLabel } from '@weapp/utils';
import {
  action, computed, observable, runInAction, toJS,
} from 'mobx';
import { ajax } from '../../common/utils/ajax';
import { getEbFields } from '../../../api/common';
import EngineBaseStore from '../../common/EngineBaseStore';
import { DataSetItem } from '../../common/plugins/DataSet/types';
import { changeNumToHan } from '../utils';
import { transferToTreeData, transferToUITreeData } from './drag-tree/utils';
import { NodeNames } from './node-config/constants';
import { MindMapDataProps, TreeData } from './types';
import { getNodeFieldsData } from '../../../utils';
export class MindMapEngineStore extends EngineBaseStore {
  @observable mindMapId: string = '';

  @observable compId: string = '';

  @observable treeData: TreeData[] = []; // 树形数据架构。dmtables转的

  @observable nodeTreeList: TreeData[] = [];

  @observable mindmapData: any = {};

  @observable dmTables: TreeData[] = []; // 数据列表式数据

  @observable nodeTreeInfo: MindMapDataProps | null = null;

  @observable selector: any = {};

  @observable formStore: FormStore | null = null;

  @observable actions: any[] = [];

  @observable formInfo: any = {
    // ebdform默认我的表单
    id: '',
    name: '',
  };

  @observable dataset: DataSetItem = {
    id: '',
    text: '',
    groupId: '',
    type: 'FORM',
  };

  /**
   * 应用构建
   * */
  @observable callbackFunc: any = {
    // 外部回调函数函数变量
    refresh: undefined,
  };

  @observable isAllTree: boolean = false; // 应用构建，会直接编辑整个思维导图树

  @observable needDeleteNodesCache: any[] = []; // isAllTree为true时，删除使用

  @observable isSave: boolean = true; // 点击保存 接口未回调成功时 控制保存按钮点击无效

  @action
  init = (params: any, refreshFn?: () => void, isAllTree: boolean = false) => {
    this.selectedFormId = params?.objId || '';
    this.appId = params?.appId || '';
    this.mindMapId = params?.id || '';
    this.currentCardData = params;
    if (refreshFn) {
      this.callbackFunc.refresh = refreshFn;
    }
    this.isAllTree = isAllTree;
    this.getMindById();
  };

  @action
  getMindById = async () => {
    const data = await ajax({
      url: `/api/bs/ebuilder/form/mindmap/getMindMapNodeConfig?apid=${this.appId}`,
      method: 'GET',
      params: { id: this.mindMapId },
      ebBusinessId: this.appId,
    });
    if (data) {
      runInAction(() => {
        if (data?.formInfo) {
          this.formInfo = data?.formInfo;
        }
        this.updateStateData(data);
      });
    }
  };

  @action
  updateStateData = (data: any) => {
    this.mindmapData = data;
    const {
      nodeTreeList = [], nodeTreeInfo = {}, compId, pageId,
    } = data;
    this.compId = compId;
    this.pageId = pageId;
    this.dmTables = [...nodeTreeList];
    this.treeData = transferToTreeData(nodeTreeList);
    this.nodeTreeInfo = nodeTreeInfo || {};

    this.nodeTreeList = [...nodeTreeList];
    this.needDeleteNodesCache = [];
  };

  @computed
  get getTreeData() {
    const { nodeTreeInfo } = this;
    return transferToUITreeData(this.dmTables, nodeTreeInfo!);
  }

  @action
  onNodeCreate = (node: any) => {
    if (node.id.indexOf('node_') < 0) {
      const tempNodeId = `node_${new Date().getTime()}`;
      const len = this.dmTables.filter((n: any) => n.floor === node.floor + 1);
      const name = `${changeNumToHan(node.floor + 1)}${getLabel('129034', '级')}${getLabel('115351', '节点')}${len.length + 1}`;
      this.dmTables = [
        ...this.dmTables,
        {
          id: tempNodeId,
          floor: node.floor + 1,
          name,
          pid: node.id,
          children: [],
          canDelete: true,
        },
      ];
      this.treeData = transferToTreeData(this.dmTables);
      this.nodeTreeInfo = {
        ...this.nodeTreeInfo,
        [tempNodeId]: {
          id: tempNodeId,
          pid: node.id,
          floor: node.floor + 1,
          dataFilter: {},
          dataSort: [],
          dataset: {},
          name,
          showFields: {},
          supField: '',
          actions: [],
        },
      };
    } else {
      Dialog.message({ type: 'info', content: getLabel('129039', '请先保存当前节点!') });
    }
  };

  @action
  onTableRemove = (node: TreeData, notTemp?: boolean) => {
    if (node.id.indexOf('node_') === -1 || notTemp) {
      ajax({
        url: `/api/bs/ebuilder/form/mindmap/deleteNode?apid=${this.appId}`,
        method: 'POST',
        data: {
          mindMapId: this.mindMapId,
          id: node.id,
        },
        ebBusinessId: this.appId,
      }).then((res) => {
        this.updateStateData(res);
      });
    } else {
      this.dmTables = this.dmTables.filter((t) => {
        const canRemove = t.id === node.id;
        return !canRemove;
      });
      this.treeData = transferToTreeData(this.dmTables);
    }
  };

  @action
  onNodeOptClick = (node: any) => {
    this.visible = true;
    this.selector = {
      ...node,
      ...this.nodeTreeInfo![node.id],
    };
    if (node.floor === 1) {
      // 根节点,数据源单独处理，使用默认数据源
      this.selector = {
        ...this.selector,
        dataset: {
          id: this.formInfo.id,
          text: this.formInfo.name,
        },
      };
    }
    this.actions = this.selector?.actions || [];
    this.dataset = this.selector?.dataset || {};
  };

  @action
  afterFormInit = (store: FormStore) => {
    this.formStore = store;
    const data: any = (this.nodeTreeInfo && this.nodeTreeInfo[this.selector.id]) || {};
    store.updateDatas(toJS(data));
    if (this.selector.floor >= 2) {
      if (this.selector.floor === 2 && this.selector.canDelete === false) {
        // 如果是默认生成的二级节点，数据源已默认生成，不允许删除和更改，隐藏该配置
        this.formStore.setHide(NodeNames.Dataset, true);
      }
      this.getCurEbSelectFields();
      this.getSupEbSelectFields();
    }
  };

  @action
  fieldChange = (value: any) => {
    this.cacheTreeProps(value);
    if (value) {
      if ('dataset' in value) {
        this.dataset = value.dataset;
        this.getCurEbSelectFields();
        this.actions = [];
      }
    }
  };

  @action
  cacheTreeProps = (value: AnyObj) => {
    this.selector = {
      ...this.selector,
      ...value,
    };
    const info = this.nodeTreeInfo ? this.nodeTreeInfo[this.selector.id] : {};
    this.nodeTreeInfo = {
      ...this.nodeTreeInfo,
      [this.selector.id]: {
        ...info,
        ...value,
      },
    };
  };

  getSelectData = (ops: any) => {
    const options = ops
      .filter((f: any) => `${f.multiSelect}` !== 'true')
      .map((d: any) => ({ ...d, content: d.showName }));
    return [{ id: '', content: getLabel('40502', '请选择') }, ...options];
  };

  @action('获取ebuilder字段-本级相关')
  getCurEbSelectFields = () => {
    const data: any = (this.nodeTreeInfo && this.nodeTreeInfo[this.selector.id]) || {};
    const currentDataId = data?.dataset?.id;
    if (currentDataId) {
      getEbFields(currentDataId, this.appId).then((res) => {
        this.formStore?.setItemProps('supField', { data: getNodeFieldsData(data?.dataset, 'supField', res.ebuilderFields, true) });
        if (this.selector.floor >= 3) {
          this.formStore?.setItemProps('curnodeField', {
            data: getNodeFieldsData(data?.dataset, 'curnodeField', res.ebuilderFields, true),
          });
        }
      });
    }
  };

  @action('获取ebuilder字段-上级相关')
  getSupEbSelectFields = () => {
    const supData: any = (this.nodeTreeInfo && this.nodeTreeInfo[this.selector.pid]) || {};
    const supDataId = supData?.dataset?.id;
    if (supDataId) {
      getEbFields(supDataId, this.appId).then((res) => {
        this.formStore?.setItemProps('supnodeField', {
          data: getNodeFieldsData(supData?.dataset, 'supnodeField', res.ebuilderFields, true),
        });
      });
    }
  };

  @action
  closeNodeConfig = () => {
    this.visible = false;
  };

  @action
  setActions = (actions: any[]) => {
    this.actions = actions;
    if (this.isAllTree) {
      // 全树修改时，将修改的form数据存储
      this.cacheTreeProps({ actions });
    }
  };

  @action
  doSave = async () => {
    if (!this.isSave) {
      return;
    }
    this.isSave = false;
    const params = this.formStore?.getFormDatas();
    const data = {
      ...params,
      pid: this.selector?.pid,
      id: this.selector?.id,
      mindMapId: this.mindMapId,
      floor: this.selector.floor,
      actions: this.actions,
    };
    const _data = await ajax({
      url: `/api/bs/ebuilder/form/mindmap/saveNode?apid=${this.appId}`,
      method: 'POST',
      data,
      ebBusinessId: this.appId,
    });
    if (_data) {
      runInAction(() => {
        Dialog.message({ type: 'success', content: getLabel('54117', '保存成功') });
        this.updateStateData(_data);
        this.selector = {
          ...this.selector,
          id: _data.nodeId,
        };
        this.isSave = true;
      });
    } else {
      runInAction(() => {
        this.isSave = true;
      });
    }
  };

  @action('编辑整棵树')
  onTableTempRemove = (node: TreeData) => {
    if (node.id.indexOf('node_') === -1) {
      this.needDeleteNodesCache.push(node);
    }
    this.dmTables = this.dmTables.filter((t) => {
      const canRemove = t.id === node.id;
      return !canRemove;
    });
    if (this.nodeTreeInfo && this.nodeTreeInfo[node.id]) {
      delete this.nodeTreeInfo[node.id];
    }
  };

  @action
  closeNodeConfigAndClear = () => {
    this.visible = false;
    this.dmTables = [...this.nodeTreeList];
    this.needDeleteNodesCache = [];
  };

  @action
  doSaveAllTree = async () => {
    const _nodeTreeInfo = toJS(this.nodeTreeInfo)
    for (let i in _nodeTreeInfo) {
      const item = _nodeTreeInfo[i]
      for (let k in item) {
        if (k === 'showFields' && typeof item[k] !== 'string') {
          item[k] = JSON.stringify(item[k])
        }
      }
    }
    const data = {
      pageId: this.pageId,
      compId: this.compId,
      nodeTreeList: toJS(this.dmTables),
      nodeTreeInfo: _nodeTreeInfo,
    };
    const _data = await ajax({
      url: `/api/bs/ebuilder/form/mindmap/saveMindMapCfg?apid=${this.appId}`,
      method: 'POST',
      data,
      ebBusinessId: this.appId,
    });
    if (_data) {
      runInAction(() => {
        Dialog.message({ type: 'success', content: getLabel('54117', '保存成功') });
        this.visible = false;
        this.needDeleteNodesCache = [];
        this.callbackFunc.refresh?.();
      });
    }
  };
}

export default new MindMapEngineStore();
