import { CorsComponent } from '@weapp/ui';
import React, { PureComponent } from 'react';
import AddCom from './AddCom';
import ButtonMore from './ButtonMore';
import './index.less';
import { IComDescription } from './types';
import { getLabel } from '@weapp/utils';

export default class ComponentPanel1 extends PureComponent {
  datas = JSON.parse(localStorage.getItem('datas') as string) || [
    {
      id: 'e2ef801faa4c44389ae77b7b679f67db',
      title: getLabel('221740','无分组组件'),
      comps: [],
    },
  ];

  getCategories = () => {
    const datas = JSON.parse(localStorage.getItem('datas') as string) || [];

    return datas.map((item: any) => {
      item.id = item.category;
      item.name = item.category;

      delete item.category;
      delete item.items;

      return item;
    });
  };

  onChange = () => {
    this.datas = JSON.parse(localStorage.getItem('datas') as string);
    this.forceUpdate();
  };

  renderCompExtra = (data: IComDescription) => (
    <>
      <ButtonMore weId={`${this.props.weId || ''}_vvbdqj`} onChange={this.onChange} data={data} />
    </>
  );

  render() {
    this.getCategories();

    return (
      <>
        <div>
          <AddCom weId={`${this.props.weId || ''}_q4uydi`} onChange={this.onChange} />
        </div>
        <CorsComponent
          weId={`${this.props.weId || ''}_tpwtve`}
          app="@weapp/designer"
          compName="ComponentPanel"
          datas={this.datas}
          renderCompExtra={this.renderCompExtra}
        />
      </>
    );
  }
}
