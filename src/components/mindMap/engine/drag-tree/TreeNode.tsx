import { Dialog, Icon } from '@weapp/ui';
import { classnames, getLabel } from '@weapp/utils';
import { observer } from 'mobx-react';
import React, { createRef } from 'react';
import { TreeNodeProps } from './types';

@observer
class TreeNode extends React.PureComponent<TreeNodeProps> {
  container = createRef<HTMLDivElement>();

  delNode = (oEvent: React.SyntheticEvent) => {
    const { onRemove, node } = this.props;

    Dialog.confirm({
      mask: true,
      content: getLabel('56178', '确定要刪除此分支？'),
      onOk: () => {
        onRemove(node);
      },
    });
    oEvent.stopPropagation();
  };

  addNode = () => {
    const { node, onCreate } = this.props;
    onCreate(node);
  };

  handleClick = () => {
    const { node, onNodeOptClick } = this.props;

    onNodeOptClick(node);
  };

  render() {
    const { node, selector } = this.props;
    const {
      name = '', children, pid, canDelete, id,
    } = node;
    const hasNode = children && children.length > 0;
    const nameTxt = typeof name === 'string' ? name : name.nameAlias;
    const classNames = classnames({
      'mindmap-ds-tree-item': true,
      'mindmap-ds-treeroot-item': !node.pid,
      'mindmap-ds-treenode-item': node.pid,
      'mindmap-ds-active': selector?.id === node.id,
      'mindmap-ds-tree-item-hasNode': hasNode,
      on: false,
    });

    return (
      <div
        className={`mindmap-ds-tree-box ${
          children && children?.length === 1 && 'mindmap-ds-tree-box-isone'
        }`}
        key={id}
        ref={this.container}
      >
        <div className={classNames} data-nodeid={id}>
          <div className="rootdisable-box">
            <h6 className="rootdisable" onClick={this.handleClick} title={nameTxt}>
              {nameTxt}
            </h6>
          </div>
          <div className="mindmap-ds-treenode-operate-add">
            <Icon
              weId={`${this.props.weId || ''}_6k0u79`}
              name="Icon-add-to02"
              onClick={this.addNode}
            />
          </div>
          {!hasNode && pid && canDelete && (
            <div className="mindmap-ds-treenode-operate-del">
              <Icon
                weId={`${this.props.weId || ''}_71gvf1`}
                name="Icon-reduce02"
                onClick={this.delNode}
              />
            </div>
          )}
        </div>
        <div className="mindmap-ds-tree-wrap">{this.props.children}</div>
      </div>
    );
  }
}

export default TreeNode;
