import { BaseProps, DialogProps } from '@weapp/ui';
import { Attributes } from 'react';

// innerPage参数
export interface DockerInnerPageProps extends Attributes {
  dataSource: DataSourceProps; // 路由参数
  dockerCallBack?: (obj: any) => void;
  hideTop?: boolean;
  deleteDom?:any
}

// urlDocker参数
export interface UrlDockerProps extends Attributes, BaseProps {
  type?: 'href' | 'param'; // url模式或者datasource模式
  url?: string; // 路由地址
  dataSource?: DataSourceProps; // 路由参数
  title?: string;
  hideTop?: boolean;
  dockerCallBack?: (obj: any) => void;
  deleteDom?:any
}

// UrlDockerDialog参数
export interface UrlDockerDialogProps extends UrlDockerProps {
  dialogParams: DialogProps; // dialog的参数
}

export enum UrlDockerPageTypes {
  WORKFLOW = 'WORKFLOW',
  SEARCH = 'SEARCH',
  FORM = 'LAYOUT',
  PAGE = 'PAGE',
  REPORT = 'REPORT',
  SYSTEMPAGE = 'SYSTEMPAGE',
  LINK = 'LINK',
  VIEWPORT = 'VIEWPORT', // 日历等视图
  Scene ='SCENE'
}

export type DataSourcePageProps = {
  pageId?: string;
  pageType?: UrlDockerPageTypes; // 地址类型
  appid?: string;
  pageViewType?:string
};

export type DataSourceProps = {
  page: DataSourcePageProps;
  params?: Array<any>;
  [_: string]: any;
};
