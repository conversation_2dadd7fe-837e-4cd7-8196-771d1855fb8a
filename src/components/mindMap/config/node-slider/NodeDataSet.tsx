import { observer } from 'mobx-react';
import React from 'react';
import { isEqual } from "@weapp/utils"
// import isEqual from 'lodash-es/isEqual';
import DataSet from '../../../common/plugins/DataSet';
import { DataSetItem, DataSetProps } from '../../../common/plugins/DataSet/types';
import silderStore from './store';
import { NodeNames } from '../../engine/node-config/constants';
import { ebdfClsPrefix } from '../../../../constants';

interface CalDataSetProps extends Omit<DataSetProps, 'config'> {
  objId?: string;
  rootProps: any;
  config: any;
}

@observer
export default class BoardDataSet extends React.PureComponent<CalDataSetProps> {
  getBaseInfo = async () => {
    const { config } = this.props;
    const { dataset } = config;
    if (dataset) {
      await silderStore.getFormField(dataset);
      await silderStore.getDataFields(dataset);
    } else {
      silderStore.reset();
    }
  };

  componentDidMount() {
    this.getBaseInfo();
  }

  componentDidUpdate(oldProps: CalDataSetProps) {
    const { dataset } = this.props.config;
    if (oldProps.config.dataset?.id !== dataset?.id) {
      this.getBaseInfo();
    }
  }

  onChange = async (dataset: DataSetItem) => {
    const { value = {} as any, onChange, onConfigChange } = this.props;
    await silderStore.getDataFields(dataset);
    if (!isEqual(dataset, value)) {
      // 如果只是改变权限，不清空 筛选、字段选项
      if ((dataset.id === value.id) && !isEqual(dataset?.ebuilderDataConfig, value?.ebuilderDataConfig)) {
        onChange(dataset);
      } else {
        const configVal = {
          dataset,
          [NodeNames.ShowFields]: {},
          // 切换数据源 清空链接地址参数配置
          [NodeNames.DataFilter]: {},
          [NodeNames.DataSort]: [],
          [NodeNames.ActionSet]: [],
          [NodeNames.SupField]: '',
          [NodeNames.Curnodefield]: '',
          [NodeNames.Supnodefield]: '',
        };
        onConfigChange(configVal);
      }
    }
  };

  render() {
    const { config } = this.props;
    const { dataset } = config;
    const isEbForm = dataset?.type === 'FORM'
    return (
      <div className={`${ebdfClsPrefix}-dataset-config ${isEbForm ? '' : 'hide-perm'}`}>
        <DataSet weId={`${this.props.weId || ''}_psw6s6`} {...this.props} value={dataset} onChange={this.onChange} />
      </div>
    );
  }
}
