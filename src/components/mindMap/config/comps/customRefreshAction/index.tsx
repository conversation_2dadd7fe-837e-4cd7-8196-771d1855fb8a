// 刷新组件扩展

import { useEffect, useState } from 'react';
import { CorsComponent, Layout } from '@weapp/ui';
import { getLabel, isEmpty } from '@weapp/utils';
// import isEmpty from 'lodash-es/isEmpty';
import { getPageLinkConfig } from '../../../../../utils/getPageLinkParams';

const { Col, Row } = Layout;
const CustomRefreshAction = (props: any) => {
  const { datasetMap, onChange, refreshInfo } = props || {};
  const [paramOpts, setParamOpts] = useState({} as any);
  useEffect(() => {
    getPageLink();
  }, []);
  const getPageLink = async () => {
    const paramOptsSync = await getPageLinkConfig(datasetMap);
    setParamOpts(paramOptsSync);
  };
  if (isEmpty(paramOpts)) {
    return <div />;
  }
  const onHandleChange = (value: any) => {
    onChange && onChange({ paramsSet: value })
  };
  return (
    <Row weId={`${props.weId || ''}_iz9zn2`} className='ui-formItem-module' style={{padding: '0'}}>
      <Col weId={`${props.weId || ''}_cairnz`} span={6} className='ui-formItem-label'>{getLabel('260283', '参数设置')}</Col>
      <Col weId={`${props.weId || ''}_j95wor`} span={18} style={{display: 'flex', alignItems: 'center'}}>
        <CorsComponent weId={`${props.weId || ''}_96hbli`} app='@weapp/components' compName='LinkParams' paramOpts={paramOpts.paramOpts} value={refreshInfo?.paramsSet || undefined} onChange={onHandleChange} />
      </Col>
    </Row>
  );
};

export default CustomRefreshAction;
