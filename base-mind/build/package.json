{"name": "mindmap", "version": "0.0.1", "type": "module", "private": true, "main": "build/mind.js", "scripts": {"build": "rollup -c ./rollup.config.js --format iife"}, "keywords": ["mindmap"], "devDependencies": {"@babel/core": "^7.24.4", "@babel/preset-env": "^7.24.4", "@rollup/plugin-babel": "^6.0.4", "@rollup/plugin-commonjs": "^25.0.7", "@rollup/plugin-json": "^6.1.0", "@rollup/plugin-node-resolve": "^15.2.3", "@rollup/plugin-replace": "^5.0.5", "@rollup/plugin-terser": "^0.4.4", "rollup": "^4.13.2", "rollup-plugin-cleanup": "^3.2.1", "rollup-plugin-copy": "^3.5.0", "rollup-plugin-filesize": "^10.0.0", "rollup-plugin-terser": "^7.0.2"}, "dependencies": {}}