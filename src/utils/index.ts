import { RouteComponentProps } from 'react-router-dom';
import { qs, getLabel, isString, isEmpty, set } from '@weapp/utils';
import { AnyObj } from '@weapp/ui';
import { DataSet } from '@weapp/ebdcoms';
import { SourceType } from '../types/common';
// import isString from 'lodash-es/isString';
// import isEmpty from 'lodash-es/isEmpty';
// import set from 'lodash-es/set';
import { EtComponentKey, BrowserType } from '../components/common/constants/EtComponent';
import { DataSetItem } from '../components/common/types';
import { NodeNames } from '../components/mindMap/engine/node-config/constants';
/*
 * 单级路由组件，可以动态挂在任何一级路由下
 * 需要获取父级路由
 */
export function fomatParentPath(props: RouteComponentProps) {
  return props.match.url.replace(/\/$/, () => '');
}

export function needLayout() {
  return window.location.pathname.indexOf('/splayout') === 0;
}

export function isMobile() {
  return window.location.pathname.indexOf('/mobile') === 0;
}
// 按钮那边没返回type，因此先把所有都拼接上去
// .filter(({ type, nameType }) => type === 'fixed' || nameType === 'fixed')
// 判断是否是关联浏览-eb自定义浏览
export const isEbRelationBrowser = (field: any) => {
  const { componentKey, otherProperties, config, compType } = field;
  let params = otherProperties || config || '{}';
  params = isString(params) ? JSON.parse(params) : params;
  return (componentKey === EtComponentKey.RelateBrowser || compType === EtComponentKey.RelateBrowser) && !!params?.ebuilderRelateBrowser;
};

const utils = {
  UUID: (len = 32) =>
    'xxxxxxxxxxxx4xxxyxxxxxxxxxxxxxxx'
      .replace(/[xy]/g, (c) => {
        /* eslint-disable no-bitwise */
        const r = (Math.random() * 16) | 0;
        const v = c === 'x' ? r : (r & 0x3) | 0x8;

        return v.toString(16);
      })
      .substring(0, len),
  random16NumberID: () => {
    const _random = (min: number, max: number) => {
      if (max == null) {
        max = min;
        min = 0;
      }
      return min + Math.floor(Math.random() * (max - min + 1));
    };
    return `${_random(100000, 999999)}${new Date().getTime().toString().substr(3)}`;
  },
  /**
   * 获取url的参数
   */
  getUrlParams: () => {
    const location = window.__ebdflocation__ || window.__location__ || window.location;
    const lastIndex = location.href.lastIndexOf('?');
    const search = lastIndex === -1 ? '' : location.href.slice(lastIndex).replace(/^\?/, '');
    // 重置掉__location__ ,放置影响关联页面参数取值
    // ios下页面url参数取参存在问题
    window.__location__ = null;
    window.__ebdflocation__ = null;
    return {
      ...qs.parse(search),
    };
  },
  /**
   * 获取地址上的参数
   * @param href 地址
   * @returns
   */
  getQueryParams: (href: string) => {
    const lastofnum = href.lastIndexOf('?');
    href = decodeURIComponent(href).slice(lastofnum + 1);
    const querys = href ? href.split('&') : [];
    const queryObj: any = {};
    querys.map((q) => {
      const index = q.indexOf('=');
      const name = q.substring(0, index);
      const value = q.substring(index + 1);
      set(queryObj, name, value);
      return q;
    });
    return queryObj;
  },
  /**
   * 检查是否是本项目内路由
   * @param href
   * @returns
   */
  checkHref: (href: string) => {
    const exceptArr = ['/ebdfpage/card'];
    for (let index = 0; index < exceptArr.length; index++) {
      const element = exceptArr[index];
      if (href.indexOf(element) > -1) {
        return true;
      }
    }
    return false;
  },
  /**
   * 创建一个canvas元素来计算文本宽度
   * @param text
   * @param font
   * @returns
   */
  getTextWidthOrHeight(text: string, tag: 'width' | 'height', font?: string) {
    const canvas = document.createElement('canvas');
    const context: any = canvas.getContext('2d');
    context.font = font || '12px sans-serif';
    const width = context.measureText(text)[tag];
    return width;
  },
};
// 是否是单页显示的卡片
export function isSpaCard() {
  return window.location.pathname.indexOf('/ebdfpage/card') >= 0;
}
/*
 * 单级路由组件，可以动态挂在任何一级路由下
 * 需要获取父级路由
 */
export function formatParentPath(props: RouteComponentProps) {
  return props.match.url.replace(/\/$/, () => '');
}

export const deleteTreeNodeById = (objAry: any, key: string | number) => {
  //根据节点id删除该节点
  objAry = objAry.filter((it: any) => it.id !== key && it.originData?.id !== key);
  objAry.forEach((item: any) => {
    if (!item.children) {
      item.children = [];
    }
    item.children = item.children.filter((it: any) => it.id !== key && it.originData?.id !== key);
    deleteTreeNodeById(item.children, key);
  });
  return objAry;
};
export const addTreeNodeByBroId = (objAry: any, key: string | number, pushData: any, positon: string) => {
  //根据兄弟节点id和插入位置，插入新节点
  for (let index = 0; index < objAry.length; index++) {
    const element = objAry[index];
    if (element.id === key) {
      if (positon === 'top') {
        //插入到上方
        objAry.splice(index, 0, { ...pushData, pid: element.pid });
      } else if (positon === 'bottom') {
        objAry.splice(index + 1, 0, { ...pushData, pid: element.pid });
      }
      break;
    }
    if (element.children) {
      addTreeNodeByBroId(element.children, key, pushData, positon);
    }
  }
  return objAry;
};

/**
 *
 * @method tree转换成list
 */
export const treeToList = (array: any = [], currentOrg: any = []) => {
  return array.reduce((arr: any, curItem: any) => {
    // if (curItem?.children?.length) { curItem.hasChild = true };
    return arr.concat([curItem], treeToList(curItem.children, currentOrg));
  }, []);
};
/**
 * 获取所有节点key
 */
export function getAllKeys(datas: any = []) {
  let treeKeys: any = [];
  datas.map((i: AnyObj) => {
    treeKeys.push(i.id);
    if (i.children && i.children?.length > 0) {
      treeKeys = [...treeKeys, ...getAllKeys(i.children)];
    }
  });
  return treeKeys;
}

/**
 * 获取maxLev 层级节点数据
 */
export function getDefautDatasShow(datas: AnyObj[], maxLev: number = 1, lev = 1) {
  return lev > maxLev
    ? []
    : datas.map((data) => {
        let _data = { ...data };
        if (_data.children && _data.children.length > 0) {
          _data.children = getDefautDatasShow(data.children, maxLev, lev + 1);
        }
        return _data;
      });
}

export const invoke = (plugin: any, pluginName: string, actionName: string, params?: AnyObj) => {
  if (isEmpty(plugin) || isEmpty(plugin.pluginNames) || !plugin.pluginNames.includes(pluginName)) return;
  plugin?.use(pluginName);
  const result = plugin?.invoke(actionName, params);
  return result;
};
// 是否在插件包里注册过事件
export const hasRegisterFuncInPlugin = (plugin: any, pluginName: string, actionName: string) => {
  if (isEmpty(plugin) || !pluginName || !actionName || !plugin.pluginNames.includes(pluginName)) return false;
  if (!plugin?.enabledPluginRecord[pluginName][actionName]) return false;
  return true;
};
// 是否为数仓数据源
export const isEteams = (dataset: DataSetItem) => {
  return dataset?.type === 'ETEAMS';
};
// 节点字段通用过滤器
export const getNodeFieldsData = (dataset: DataSetItem, type: any, fields: any, isEngine: boolean = false) => {
  let result = [] as any;
  switch (type) {
    // 上级字段
    case NodeNames.SupField:
      result = fields.filter((f: any) => {
        if (`${f.multiSelect}` !== 'true' || isEteams(dataset)) {
          // 表单高级视图下目前暂时没有mainPrimaryKey 以id === '5'判断主键
          if (f.mainPrimaryKey || f.componentKey === EtComponentKey.Ebuilder || (isEngine && f.id === '5')) {
            return true;
          }
        }
        return false;
      });
      break;
    //本级节点字段
    case NodeNames.Curnodefield:
      result = fields.filter((f: any) => {
        if (`${f.multiSelect}` !== 'true' || isEteams(dataset)) {
          if (f.fieldName === 'id' || f.mainPrimaryKey || f.fieldName === 'name' || (isEngine && f.id === '5')) {
            // id,标题
            return true;
          }
          if ([EtComponentKey.Text, EtComponentKey.TextArea, EtComponentKey.Employee, EtComponentKey.Creator, EtComponentKey.Updater, EtComponentKey.Department, EtComponentKey.Subcompany, EtComponentKey.Number].includes(f.componentKey)) {
            return true;
          }
          if (isEbRelationBrowser(f)) {
            return true;
          }
          if (BrowserType.includes(f.componentKey)) {
            return true;
          }
        }
        return false;
      });
      break;
    // 上级节点字段
    case NodeNames.Supnodefield:
      result = fields.filter((f: any) => {
        if (`${f.multiSelect}` !== 'true' || isEteams(dataset)) {
          if (f.fieldName === 'id' || f.fieldName === 'name' || (isEngine && f.id === '5')) {
            // id,标题
            return true;
          }
          if ([EtComponentKey.Text, EtComponentKey.TextArea, EtComponentKey.Employee, EtComponentKey.Creator, EtComponentKey.Updater, EtComponentKey.Department, EtComponentKey.Subcompany, EtComponentKey.Number].includes(f.componentKey)) {
            return true;
          }
          if (isEbRelationBrowser(f)) {
            return true;
          }
          if (BrowserType.includes(f.componentKey)) {
            return true;
          }
        }
        return false;
      });
      break;
  }
  return [{ id: '', content: getLabel('40502', '请选择') }, ...result.map((d: any) => ({ ...d, content: d.showName }))];
};

export const removeHtmlTag = (htmlStr: string) => {
  return htmlStr.replace(/<[^>]+>/g, '').replace(/&nbsp;/g, ' ');
};
export const isIE = () => {
  let ua = window.navigator.userAgent;
  let isIE = ua.indexOf('MSIE ') > -1 || ua.indexOf('Trident/') > -1;
  return isIE;
};
// 是否可以看到预发布的功能
export const isAdvancedView = () => {
  const isAdvancedView = localStorage.getItem('EbdMind_Advanced_View');
  return isAdvancedView === '1';
};

// 是否可以请求数据
export const canLoadData = (config: any = {}) => {
  const { nodeTreeInfo, nodeTreeList } = config;
  const notRootNodes = nodeTreeList.filter((i: any) => i.id !== 'root_');
  // 暂时先以不是根节点为条件
  if (isEmpty(notRootNodes)) return 'noData';
  // 其余节点都需要配置数据源
  return notRootNodes.every((i: any) => !isEmpty(nodeTreeInfo[i.id].dataset));
};
/**
 * @function JSONSafeParse
 * @desc 封装JSON.parse方法，防止空值报错等问题
 * @param {string} value
 * @param {any} defaultValue
 * */
export const JSONSafeParse = (value: string, defaultValue?: any) => {
  let parsedData;
  try {
    if (typeof value === 'string' && (value.startsWith('{') || value.startsWith('['))) {
      parsedData = JSON.parse(value);
    } else {
      parsedData = value;
    }
  } catch (error) {
    parsedData = defaultValue || value; // 其他默认值或数据本身
  }
  return parsedData;
};
export const isEbFormData = (dataset?: DataSet) => dataset
  && dataset.type === SourceType.LOGIC
  && dataset.groupId === 'weaver-ebuilder-form-service';
export default utils;
