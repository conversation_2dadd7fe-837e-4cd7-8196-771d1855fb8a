import {
  Form, FormDatas, FormLayoutProps, FormStore, FormSwitchProps,
} from '@weapp/ui';
import { getLabel, isEmpty } from '@weapp/utils';
// import isEmpty from 'lodash-es/isEmpty';
import { toJS } from 'mobx';
import React, { PureComponent } from 'react';
import { ebdfClsPrefix } from '../../../../constants';
// import DataSort from '../../../plugins/data-sort';
import DataSort from '../../../common/plugins/data-sort';
import FieldLayout from '../../../common/plugins/field-layout';
import DataFilter from '../../../common/plugins/Filter';
import { specialFieldsFilter } from '../../../common/utils';
import ColDataSet from './ColDataSet';
import { getItemAndLayout, NodeNames } from './constants';
import './index.less';
import LocaleInput from '../../../common/locale/LocaleInput';
export default class BaseForm extends PureComponent<any> {
  _store = new FormStore();

  componentDidMount() {
    const { selector, afterInit } = this.props;
    const { items, layout } = getItemAndLayout(selector.floor);
    this._store.initForm({
      items,
      layout,
      data: {},
      groups: [],
    });
    afterInit(this._store);
  }

  onOk = () => {
    const { onConfirm, onCancel, node } = this.props;
    this._store.validate().then((res: any) => {
      if (isEmpty(res.errors)) {
        onConfirm(this._store.getFormDatas(), node.floor);
        onCancel();
      }
    });
  };

  customRenderFormSwitch = (key: string, props: FormSwitchProps) => {
    const { dataset = {} } = this._store.getFormDatas();
    if (key === NodeNames.Name) {
      const { value = '', onChange, ...restProps } = props.props;
      return (
        <LocaleInput
          weId={`${this.props.weId || ''}_xdecnl`}
          {...restProps}
          value={value}
          onChange={onChange}
          ebBusinessId={this.props.appId}
        />
      );
    }
    if (key === NodeNames.Dataset) {
      return (
        <ColDataSet weId={`${this.props.weId || ''}_6ikmvz`} {...props.props} value={dataset} isEngine={this.props.isEngine} />
      );
    }
    if (key === NodeNames.ShowFields) {
      const { value = [], onChange } = props.props;
      const _dataset = toJS(dataset)
      let datasetValue = isEmpty(_dataset) ? undefined : _dataset
      return (
        <FieldLayout
          weId={`${this.props.weId || ''}_uf2367`}
          config={{
            dataset: datasetValue,
          }}
          dataset={datasetValue}
          value={value}
          onChange={onChange}
        />
      );
    }
    if (key === NodeNames.DataSort) {
      const { value = [], onChange } = props.props;
      const realValue = value && typeof value === 'string' ? JSON.parse(value) : value;
      const isChecked = realValue?.length > 0;
      return (
        <span className={`mindmap-baseinfo-datasort-checked-${isChecked}`}>
          <DataSort
            weId={`${this.props.weId || ''}_byttyz`}
            value={value}
            dataset={toJS(dataset)}
            onChange={onChange}
            btnElmType="icon"
            checked={value.length > 0}
          />
        </span>
      );
    }
    if (key === NodeNames.DataFilter) {
      const { value = [], onChange } = props.props;
      return (
        <DataFilter
          weId={`${this.props.weId || ''}_rmgdha`}
          value={value}
          dataset={dataset}
          onChange={onChange}
          title={getLabel('129036', '思维导图固定查询条件')}
          filter={specialFieldsFilter}
        />
      );
    }
    return <div />;
  };

  formChange = (value?: FormDatas | undefined) => {
    const { selector } = this.props;
    if (value) {
      if ('dataset' in value) {
        const updatas: any = {
          [NodeNames.DataFilter]: {},
          [NodeNames.ShowFields]: {},
          [NodeNames.DataSort]: [],
          [NodeNames.SupField]: '',
        };
        if (selector.floor >= 3) {
          updatas.curnodeField = '';
        }
        this._store.updateDatas(updatas);
      }
    }
    this._store.updateDatas(value);
    this.props.onChange?.(value);
  };

  customHide = (col: FormLayoutProps) => col;

  render() {
    return (
      <div className={`${ebdfClsPrefix}-view-mindmap-baseinfo`}>
        <Form
          weId={`${this.props.weId || ''}_a87v4h`}
          store={this._store}
          customHide={this.customHide}
          customRenderFormSwitch={this.customRenderFormSwitch}
          onChange={this.formChange}
          noLine
          className={`${ebdfClsPrefix}-form-label-w116 `}
        />
      </div>
    );
  }
}
