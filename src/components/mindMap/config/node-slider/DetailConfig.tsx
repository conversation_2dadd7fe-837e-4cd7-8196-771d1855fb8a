import { utils } from '@weapp/ui';
import { getLabel } from '@weapp/utils';
import { toJS } from 'mobx';
import Sort from './sort';
import { NodeNames } from '../../engine/node-config/constants';
import createForm from '../ConfigForm';
import RequiredInput from '../RequiredInput';
import ActionSet from './action-set';
import FieldLink from './FieldLink';
import NodeDataSet from './NodeDataSet';
import ParamCondition from './ParamCondition';
import ShowFields from './ShowFieldsCom';
import NodeSizeSet from './node-size';
import NodeStyleSet from './node-style';
import NodeKeySet from './node-key-set';
import { isEbFormData } from '../../../../utils';

const { isEqual } = utils;

export interface ConfigFormProps {
  data: any;
  page: any;
  onChange: (changedData: any) => void;
  setConfigRef?: (ref: any) => void;
}

const groups = [
  {
    id: 'nodeSetGroup',
    title: getLabel('87164', '节点设置'),
    visible: true,
    custom: false,
  },
  {
    id: 'datasetGroup',
    title: getLabel('55058', '数据源'),
    visible: true,
    custom: false,
  },
  {
    id: 'showFieldGroup',
    title: getLabel('53855', '显示字段'),
    visible: true,
    custom: false,
  },
  {
    id: 'filterGroup',
    title: getLabel('53857', '数据过滤'),
    visible: true,
    custom: false,
  },
  {
    id: 'sortGroup',
    title: getLabel('54298', '排序'),
    visible: true,
    custom: false,
  },
  {
    id: 'subFieldGroup',
    title: getLabel('97200', '上级字段'),
    visible: true,
    custom: false,
  },
  {
    id: 'actionGroup',
    title: getLabel('87169', '节点动作'),
    visible: true,
    custom: false,
  },
  {
    id: 'upLowNodeGroup',
    title: getLabel('129035', '上下级节点设置'),
    visible: true,
    custom: false,
  },
  {
    id: 'nodeKeySet',
    title: getLabel('262663', '节点图例设置'),
    visible: true,
    custom: false,
  },
];

const getGroups = (props: any, isMobile: boolean, floor: number, canDelete: boolean) => {
  const { rootProps, data } = props;
  const treeId = data.id;
  const { config } = rootProps;
  const currentDataSet = config.nodeTreeInfo[treeId] ? config.nodeTreeInfo[treeId]?.dataset ?? {} : {};
  const fullGroups = groups.filter((group) => {
    let show = true;
    switch (group.id) {
      // mark 标准业务数据源不显示排序及上级字段
      case 'sortGroup':
        show = currentDataSet.type !== 'BIZ';
        break;
      // mark 上级字段控制显隐
      case 'subFieldGroup':
        show =
          // *标准业务数据源 上级字段 不显示
          currentDataSet.type !== 'BIZ' &&
          // *二级节点数据源不为表单数据源或者数据加工数据源 不显示
          (floor === 2 ? isEbFormData(currentDataSet) || currentDataSet.type === 'ETEAMS' : true);
          // (floor === 2 ? currentDataSet.type === 'FORM' || currentDataSet.type === 'ETEAMS' : true);
        break;
      // mark 根节点不显示数据源
      case 'datasetGroup':
        if (floor === 2) {
          show = !(rootProps?.config?.fromEbuilder && canDelete === false);
        }
        break;
      // mark 如果是标准业务数据源 不显示上下级节点设置
      // mark 三级节点以上才显示上下级节点设置
      case 'upLowNodeGroup':
        show = currentDataSet.type !== 'BIZ' && floor >= 3;
        break;
      default:
        break;
    }
    return show;
  });
  return fullGroups;
};

const DetailConfig = createForm({
  groups,
  items: {
    [NodeNames.Name]: {
      label: getLabel('87166', '节点名称'),
      labelSpan: 8,
      itemType: 'CUSTOM',
      value: '',
      placeholder: getLabel('87166', '节点名称'),
      customRender: RequiredInput,
      groupId: 'nodeSetGroup',
    },
    [NodeNames.NodeSize]: {
      label: getLabel('245996', '节点尺寸'),
      labelSpan: 8,
      itemType: 'CUSTOM',
      value: '',
      customRender: NodeSizeSet,
      groupId: 'nodeSetGroup',
    },
    [NodeNames.NodeStyle]: {
      // 节点动作
      label: getLabel('247418', '节点样式'),
      labelSpan: 8,
      itemType: 'CUSTOM',
      value: '',
      style: { width: '100%' },
      groupId: 'nodeSetGroup',
      customRender: NodeStyleSet,
    },
    [NodeNames.Dataset]: {
      label: getLabel('55058', '数据源'),
      labelSpan: 0,
      itemType: 'CUSTOM',
      value: '',
      placeholder: getLabel('55058', '数据源'),
      customRender: NodeDataSet,
      groupId: 'datasetGroup',
    },
    [NodeNames.ShowFields]: {
      label: getLabel('53855', '显示字段'),
      labelSpan: 0,
      itemType: 'CUSTOM',
      value: '',
      placeholder: getLabel('53855', '显示字段'),
      customRender: ShowFields,
      groupId: 'showFieldGroup',
    },
    [NodeNames.DataFilter]: {
      // 数据过滤
      value: [],
      itemType: 'CUSTOM',
      labelSpan: 0,
      customRender: ParamCondition,
      groupId: 'filterGroup',
    },
    [NodeNames.DataSort]: {
      // 排序字段
      value: [],
      itemType: 'CUSTOM',
      groupId: 'sortGroup',
      labelSpan: 0,
      customRender: Sort,
    },
    [NodeNames.SupField]: {
      label: getLabel('97200', '上级字段'),
      labelSpan: 0,
      itemType: 'CUSTOM',
      value: '',
      groupId: 'subFieldGroup',
      customRender: FieldLink,
    },
    [NodeNames.ActionSet]: {
      // 节点动作
      label: getLabel('87169', '节点动作'),
      labelSpan: 0,
      itemType: 'CUSTOM',
      value: '',
      groupId: 'actionGroup',
      customRender: ActionSet,
    },
    [NodeNames.Curnodefield]: {
      label: getLabel('186458', '本级节点字段'),
      labelSpan: 10,
      itemType: 'CUSTOM',
      value: '',
      groupId: 'upLowNodeGroup',
      customRender: FieldLink,
    },
    [NodeNames.Supnodefield]: {
      label: getLabel('87173', '上级节点字段'),
      labelSpan: 10,
      itemType: 'CUSTOM',
      value: '',
      groupId: 'upLowNodeGroup',
      customRender: FieldLink,
    },
    [NodeNames.NodeKeySet]: {
      label: '',
      labelSpan: 0,
      itemType: 'CUSTOM',
      value: '',
      groupId: 'nodeKeySet',
      customRender: NodeKeySet,
    },
  },
  references: {
    dataset: [NodeNames.ShowFields, NodeNames.DataFilter, NodeNames.DataSort, NodeNames.Curnodefield, NodeNames.ActionSet, NodeNames.NodeKeySet],
  },
  customHide: function customHide(col: any, isMobile: boolean) {
    const { data } = this.props;
    const { floor, canDelete } = data;
    if (col.id === NodeNames.Name || col.id === NodeNames.SupField) {
      const _groups = getGroups(this.props, isMobile, floor, canDelete);
      if (!isEqual(_groups, toJS(this.formStore.groups))) {
        this.formStore.setState({ groups: _groups });
      }
    }
    return col;
  },
});

export default DetailConfig;
