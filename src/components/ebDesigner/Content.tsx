import { observer } from 'mobx-react';
import React, { PureComponent } from 'react';
import { FlowDesignerDemo } from './flow-designer';

@observer
export default class Main extends PureComponent<any> {
  render(): React.ReactNode {
    return (
      <div
        style={{
          height: '100%',
          paddingTop: '20px',
          overflow: 'auto',
        }}
        className="ebcom-mindmap"
      >
        <FlowDesignerDemo weId="hbeo0i" />
      </div>
    );
  }
}
