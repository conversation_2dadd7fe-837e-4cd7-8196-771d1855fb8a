@prefixCls: ui-title;

.@{prefixCls}-left-custom {
  display: flex;
  .@{prefixCls}-title-custom {
    font-size: var(--font-size-12);
    flex: 1 1;
    width: calc(100% - 24px);
    .@{prefixCls}-left {
      padding-left: 0px;
      padding-top: 0px;
      .@{prefixCls}-title {
        padding-left: 0px;
        .@{prefixCls}-title-top {
          padding-left: calc(var(--hd) * 8);
          font-weight: var(--font-weight-bold);
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
          color: var(--regular-fc);
          font-size: var(--font-size-12);
        }
      }
    }
    .@{prefixCls}-left > p, .@{prefixCls}-left > span > p {
      margin: 0;
      margin-left: var(--h-spacing-md);
    }
  }
  .@{prefixCls}-icon-custom {
    display: -webkit-flex;
    display: flex;
    -webkit-align-items: center;
    align-items: center;
    -webkit-justify-content: center;
    justify-content: center;
    border-radius: var(--border-radius-xs);
    border: none;
    color: var(--secondary-fc);
    .ebcoms-assets-icon,
    .ebcoms-assets-img {
      border-radius: var(--border-radius-xs);
    }
  }

}

/** 有tab的情况下标题padding-left= 0*/
.@{prefixCls}-left-tabs {
  .@{prefixCls}-title-custom {
    .@{prefixCls}-left {
      .@{prefixCls}-title {
        .@{prefixCls}-title-top {
          padding-left: 0px;
        }
      }
    }
  }
}

/*dialog的头部字体带小14*/
.@{prefixCls}-left-custom-indialog {
  .@{prefixCls}-title-top {
    font-size: var(--font-size-14) !important;
  }
}

// .@{prefixCls}-left-custom-content{
//   padding-left: var(--h-spacing-lg);
// }
