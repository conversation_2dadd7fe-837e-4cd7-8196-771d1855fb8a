import { DataSet as EbdcomsDataSet } from '@weapp/ebdcoms';
import { EtComponentKey } from "../components/common/constants/EtComponent";

export declare type LazyType = React.LazyExoticComponent<any>;
/* 任意属性对象 */
export type AnyObj = {
  [_: string]: any;
};

/** *
 * 卡片url参数
 */

export enum LayoutType {
  Show = '0', // 显示布局
  Add = '1', // 新建布局
  Edit = '2', // 编辑布局
}
/**
 * OpenMode 页面打开方式，
 * @description 来自 @weapp/ebdcoms src/constants/index.ts
 * */
export enum OpenMode {
  Default = '4',
  New = '0',
  SlideLeftFromRight = '1',
  SlideRightFromLeft = '2',
  SlideUpFromBottom = '3',
  Modal = '5',
  /** 当前布局 */
  Layout = '6',
  /** 右侧分栏 */
  RightLayout = '7',
  /** 重定向 */
  Replace = '8',
}
/**
 * 表单字段
 * */
export type FormFieldItem = {
  componentKey: EtComponentKey;
  config: string;
  fieldId: string;
  fieldName: string;
  fieldShowName: string;
  fieldType: string;
  multiSelect?: boolean;
  canNotLink?: boolean;
  type?: string;
  parentId?: string;
  mainPrimaryKey?: boolean;
};

export enum CommonSearchType {
  Advanced = 0,
  TopSearch = 1,
}

/** *
 * 抽象类公用types
 */

export type ResponseError = {
  status: string;
  message: string;
  description?: string;
};


export enum SearchType {
  QuickSearch = '1',
  AdvanceSearch = '2',
  FilterSearch = '3',
}

/**
 * @weapp/ebdcoms DataSetPermission 类型
 * @filePath \\weapp-ebdcoms\src\common\dataset\types.ts
 * */
export interface DataSetPermission {
  objId?: string;
  containSub?: boolean;
  type?: string;
  shareTypes?: string[];
}

/**
 * 继承@weapp/ebdcoms DataSet 数据源类型
 * @filePath \\weapp-ebdcoms\src\common\dataset\types.ts
 * */
export interface DataSet extends EbdcomsDataSet {
  id: string;
  text: string;
  type: string;
  groupId: string;
  customData?: string;
  customExternalData?: string;
  dataPermissions?: DataSetPermission[];
}
export enum SourceType {
  FORM = 'FORM', // 表单
  ETEAMS = 'ETEAMS', // 数据仓库
  BIZ = 'BIZ', // 标准业务数据
  CUSTOM = 'CUSTOM', // 自定义数据
  LOGIC = 'LOGIC', // 逻辑表
  MOCK = 'mock', // 模拟数据源
  EXTERNALDATASET = 'EXTERNALDATASET', // 数据集合
  EXTERNAL = 'EXTERNAL', // 外部数据源
}
/**
 * 数据源值类型
 */
export type DataSourceValueType = {
  id: string;
  text: string;
  type: SourceType;
  groupId: string;
  customData?: string;
  customExternalData?: string;
  dataPermissions?: any[];
  servicePath?: string;
  params?: string[];
  dataId?: string;
  ebuilderDataConfig?: any;
  data?: any[];
  /** 关联的组件id */
  refComId?: string;
  /** 数字面板，关联的数据项id */
  refPanelId?: string;
  /** 数据源分组信息 数据中心提供 */
  dataGroupConfig?: any;
  /** 数据源分组更改时的时间戳，解决fields缓存问题 */
  dataGroupMemoTime?: number;
  /** 数据源分组原始过滤 */
  dataGroupFilter?: any;
  isPhysical?: boolean;
  /** 数据源支持类型 */
  usage?: string;
  appId?: string;
  comConfig?: any; // 组件配置信息
  objType?: string /** 数据源类型：vform（虚拟表） */;
  detailTable?: boolean /** 是否是明细数据源 */;
};