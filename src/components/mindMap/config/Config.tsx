import { ComConfigData, FormItems } from '@weapp/ebdcoms';
import { getLabel } from '@weapp/utils';
import { MIND_VIEW } from '../../../constants/enum';
import MindMapNodeTree from './NodeTree';
import MindMapFuncSetting from './comps/func-setting';
import NodeSizeSet from './node-slider/node-size';
import NodeStyleSet from './node-slider/node-style';
import FieldFilterSet from './comps/node-fieldFilter';
import CustomRefreshAction from './comps/customRefreshAction';
import LayoutSet from './comps/layout';
import LineStyle from './comps/lineStyle';
const groups: any = [
  {
    id: 'commonNodeSetGroup',
    title: getLabel('245995', '通用设置'),
    visible: true,
    custom: false,
  },
  {
    id: 'nodesetGroup',
    title: getLabel('87164', '节点设置'),
    visible: true,
    custom: false,
  },
];
export const getGroups = (config: any) => {
  const fullGroups = groups.filter((group: any) => {
    if (group.id === 'datasetGroup') {
      return !config?.rootProps?.config?.fromEbuilder;
    }
    return true;
  });
  const hasTop = fullGroups.find((group: any) => group.id === 'build-in-group');
  if (!hasTop) {
    fullGroups.unshift({
      custom: false,
      id: 'build-in-group',
      title: '',
      visible: true,
    });
  }
  return fullGroups;
};
// 通用设置相关
const commonItems: FormItems = {
  mindLayout: {
    label: getLabel('288330', '布局配置'),
    labelSpan: 18,
    itemType: 'CUSTOM',
    groupId: 'commonNodeSetGroup',
    customRender: LayoutSet,
  },
  lineStyle: {
    label: getLabel('265548', '连线配置'),
    labelSpan: 6,
    itemType: 'CUSTOM',
    align: 'right',
    customRender: LineStyle,
    groupId: 'commonNodeSetGroup',
  },
  commonNodeSizeSet: {
    label: getLabel('245996', '节点尺寸'),
    labelSpan: 18,
    itemType: 'CUSTOM',
    groupId: 'commonNodeSetGroup',
    customRender: NodeSizeSet,
  },
  commonNodeStyleSet: {
    label: getLabel('247418', '节点样式'),
    labelSpan: 18,
    itemType: 'CUSTOM',
    groupId: 'commonNodeSetGroup',
    customRender: (props: any) => {
      return <NodeStyleSet weId={`${props.weId || ''}_8qsnxk`} isCommon {...props} />;
    },
  },
};
const data: ComConfigData = {
  title: true,
  footer: false,
  groups,
  items: {
    title: {
      label: '',
      labelSpan: 0,
      itemType: 'CUSTOM',
      value: '',
    },
    nodeTreeList: {
      label: '',
      labelSpan: 0,
      itemType: 'CUSTOM',
      groupId: 'nodesetGroup',
      customRender: MindMapNodeTree,
    },
    nodeTreeInfo: {
      label: '',
      labelSpan: 0,
      itemType: 'CUSTOM',
    },
    funcSetting: {
      label: getLabel('140675', '功能设置'),
      labelSpan: 20,
      itemType: 'CUSTOM',
      align: 'right',
      customRender: MindMapFuncSetting,
    },
    fieldFilterSet: {
      label: getLabel('255250', '卡片字段显隐'),
      labelSpan: 20,
      itemType: 'CUSTOM',
      align: 'right',
      customRender: FieldFilterSet,
    },
    ...commonItems,
  },
  references: {
    nodeTreeInfo: ['nodeTreeList', 'fieldFilterSet'],
    // nodeTreeList: ['fieldFilterSet'],
    commonNodeSet: ['commonNodeStyleSet'],
    commonNodeStyleSet: ['commonNodeSet'],
    mindLayout: ['lineStyle', 'nodeTreeList', 'commonNodeStyleSet'],
    lineStyle: ['nodeTreeList', 'nodeTreeInfo'],
  },
  customHide: function customHide(col: any) {
    const _this = this as any;
    const formDatas: any = _this.form.datas;
    if (col.id === 'nodeTreeInfo') {
      return { ...col, hide: true };
    }
    if (col.id === 'lineStyle') {
      const canSetLineStyleView = [MIND_VIEW.LOGICAL_STRUCTURE, MIND_VIEW.ORGANIZATION_STRUCTURE]
      return { ...col, hide: !canSetLineStyleView.includes(formDatas.mindLayout) };
    }
    return col;
  },
};
export default {
  ...data,
  getGroups,
  eventAction: {
    actions: [
      {
        id: 'RefreshComp',
        config: {
          customRenderConfig: CustomRefreshAction,
        },
      },
    ],
  },
};
