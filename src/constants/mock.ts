import { getLabel } from '@weapp/utils';
export const Mo<PERSON><PERSON>indMapNodeData = () => [
  {
    id: '0_0',
    title: '',
    content: getLabel('56039', '根节点'),
    children: [],
    disabled: false,
    isLeaf: false,
    customParameters: {},
    disabledCheck: false,
    defaultExpendDepth: 2,
    floor: 1,
    appid: '920144031225970690',
  },
];
export const MockMindMapData = () => [
  {
    id: '0_0',
    title: '',
    content: getLabel('56039', '根节点'),
    children: [
      {
        id: '920602153877716996_920603820392136705',
        content: '{"920603554078179329":"node1","id":"920603820392136705"}',
        children: [
          {
            id: '920602153877716996_920603936221216768',
            content: '{"920603554078179329":"node1-1","id":"920603936221216768"}',
            children: [],
            disabled: false,
            parentid: '920602153877716996_920603820392136705',
            isLeaf: false,
            customParameters: {},
            disabledCheck: false,
            floor: 3,
          },
          {
            id: '920602153877716996_920603897642008577',
            content: '{"920603554078179329":"node1-2","id":"920603897642008577"}',
            children: [],
            disabled: false,
            parentid: '920602153877716996_920603820392136705',
            isLeaf: false,
            customParameters: {},
            disabledCheck: false,
            floor: 3,
          },
        ],
        disabled: false,
        parentid: '0_0',
        isLeaf: false,
        customParameters: {},
        disabledCheck: false,
        floor: 2,
      },
    ],
    disabled: false,
    isLeaf: false,
    customParameters: {},
    disabledCheck: false,
    defaultExpendDepth: 2,
    floor: 1,
    appid: '920144031225970690',
    isroot: true
  },
];
export const MockConfigData = () => {
  return {
    id: 'b262fe7c057b407b9173c76c9ba6172b',
    pid: '',
    refCompId: 'a88d3ff596d9449da0d776d4d208ae8d',
    type: 'MindMap',
    category: '4',
    refModule: '',
    tenantKey: 'tp7ed833w9',
    styleCategoryCompKey: 'BasicStyleConfig',
    styleCategoryPackage: '@weapp/ebdcoms',
    applyStyle: '1',
    lock: false,
    config: {
      interData: 'mindMap',
      package: '@weapp/ebdfpage',
      type: 'MindMap',
      nodeTreeInfo: {
        root_: {
          name: getLabel('56039', '根节点'),
          defaultExpendDepth: '2',
          pid: '',
          id: 'root_',
          isHideRoot: false,
          dataFilter: '',
          dataSort: '',
          showFields: '',
        },
        '920602153877716996': {
          actions: [],
          dataFilter: '{}',
          dataSort: '[]',
          dataset: {
            groupId: '920144031225970690',
            id: '920602115297689600',
            text: '',
            type: 'FORM',
          },
          id: '920602153877716996',
          name: '',
          pageNo: 0,
          pageSize: 0,
          pid: 'root_',
          showFields:
            '{"field":[{"type":"String","lazyLoad":false,"compTypec":"","browserType":"","compType":"Text","disAllowedUsage":[],"text":"","mainPrimaryKey":false,"formInfo":{"fieldId":"920603554078179329"},"mainField":true,"deleteType":false,"parentId":"","browserParams":{},"placeholderMark":false,"objId":"920602115297689600","name":"920603554078179329","dbFieldType":"","richEditor":false,"config":{"formId":"920602115297689602","groupId":"-1","isUnique":false,"title":"","content":"","dataCount":0,"dataKey":"jdm2","isDefault":false,"unique":false,"name":"","showOrder":1,"describe":"","placeholder":"","componentKey":"Text","columnName":"jdm2","fieldId":"920603554078179329","maxLen":100},"multiSelect":"false","group":"form","overFlow":{"number":"","type":"line"},"autoFocus":false,"id":"920603554078179329","uid":"c4752b85f85d4e35b622c597cb9693cf"}],"cardLayout":{"split":[{"index":0,"id":"fbbcb531dce84a1ea5c55805200c6809","type":"0","setting":{"width":{"type":"","value":""}}}],"grid":[[[{"x":0,"y":0,"w":1,"h":1,"field":{"id":"920603554078179329","uid":"c4752b85f85d4e35b622c597cb9693cf","style":{},"width":{"type":"1","value":"","unit":"px"},"minWidth":"80","showTrans":[],"align":"left","wordWrap":false,"padding":[0,12,0,0],"image":{"showType":"grid","previewable":true,"h":"100%","w":"100%"},"horAlign":"0","isTransAvatar":false,"overFlow":{"number":"","type":"line"},"type":"String"},"i":"cb77aca361304557a667f522755d5755"}]]],"size":[[4,3]],"row":[[{"index":0,"isHide":false,"setting":{"alignItems":"center","height":""}},{"index":1,"isHide":false,"setting":{"alignItems":"center","height":""}},{"index":2,"isHide":false,"setting":{"alignItems":"center","height":""}},{"index":3,"isHide":false,"setting":{"alignItems":"center","height":""}}]],"extendArea":{"id":"b30e1c377d024c2c83d9b2e6e0029c1d","display":"none","type":"current","pageMode":"part","pageSize":10},"shrinkDisplayType":"single","shrinkRegionIndex":-1}}',
          supField: '920602282609295362',
        },
      },
      fromEbuilder: true,
      layout: {
        hidden: false,
        wrapped: false,
        selected: true,
      },
      name: getLabel('55988', '思维导图'),
      titleEnabled: false,
      objId: '920602115297689600',
      nodeTreeList: [
        {
          name: getLabel('56039', '根节点'),
          pid: '',
          canDelete: false,
          id: 'root_',
          floor: 1,
          dataFilter: '',
          dataSort: '',
          showFields: '',
        },
        {
          name: '',
          pid: 'root_',
          canDelete: false,
          id: '920602153877716996',
          floor: 2,
          dataFilter: '',
          dataSort: '',
          showFields: '',
        },
      ],
      styles: [
        {
          customStyle: {
            container: {
              padding: '0px 16px 0px 0px',
              margin: '0px 0px 0px 0px',
              backgroundColor: '#f6f6f6',
            },
          },
          useCustom: true,
          id: '7',
          category: '7',
        },
        {
          customStyle: {
            title: {
              background: {
                imgUrl: '',
                backgroundColor: 'transparent',
                positionType: 'grid',
                backgroundImage: '',
                backgroundSize: 'auto',
                backgroundPosition: 'center center',
                backgroundRepeat: 'no-repeat',
              },
            },
          },
          useCustom: true,
          id: '',
          category: '1',
        },
      ],
      canDelete: false,
      showLunar: true,
      flow: {
        parent: 'ROOT',
        nodes: [],
        isCanvas: false,
        linkedNodes: {},
        nodeId: '9RYRgNhBhi',
      },
      footerEnabled: false,
    },
    package: '@weapp/ebdmindmap',
    isLock: false,
  };
};
