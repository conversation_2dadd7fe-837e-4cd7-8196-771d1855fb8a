import React from 'react';
import { toJS } from 'mobx';
import FieldLayout from '../../../common/plugins/field-layout';

// 显示字段
export class ShowFieldsCom extends React.PureComponent<any> {
  render() {
    const {
      value = {}, config, form, id, onChange, rootProps
    } = this.props;
    let { dataset } = config;
    if (id === 'promptInfo') {
      dataset = {
        id: form.datas?.resourceFieldObjId,
        text: '',
        type: 'FORM',
        groupId: '',
      };
    }
    const valueObj = value && typeof value === 'string' ? JSON.parse(value) : value;
    const { field = [], ...restVal } = toJS(valueObj);
    return (
      <FieldLayout
        weId={`${this.props.weId || ''}_q34g6g`}
        config={{ dataset }}
        value={{ ...toJS(restVal), field }}
        onChange={onChange}
        page={rootProps.page}
      />
    );
  }
}

export default ShowFieldsCom;
