import { observer } from 'mobx-react';
import React, { PureComponent } from 'react';
import { withRouter } from 'react-router-dom';
import { MindMapBuilderHeader } from '../../../lib';


@observer
class EbQuickCreate extends PureComponent<any, any> {
  render() {
    console.log('**EbQuickCreate**this.props****', this.props);
    const mockJson = {
      appId: '920881378315517953',
      config: {
        objId: '920881438343708679',
        viewId: '992825138370740225'
      },
      refreshFn: () => {
        console.log('****refreshFn****');
      },
    }
    return <>
      <MindMapBuilderHeader weId={`${this.props.weId || ''}_mg49b6`}
        {...mockJson}
      />
    </>
  }
}

export default withRouter(EbQuickCreate);
