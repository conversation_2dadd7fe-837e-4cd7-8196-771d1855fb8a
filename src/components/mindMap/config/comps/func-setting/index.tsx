import { Button, Checkbox, Dialog, ITableColumn, Icon, Table, Switch } from '@weapp/ui';
// import isEmpty from 'lodash-es/isEmpty';
import { ebdfClsPrefix } from '../../../../../constants';
// import { isAdvancedView } from '../../../../../utils';
import { getLabel, isEmpty } from '@weapp/utils';
import React, { PureComponent } from 'react';
import { COMP_DEFAULT_CONFIG } from '../../../../common/constants';
import { getFuncSetting } from '../../../comp/mindMap-view/utils';
import SettingCard from '../../../../common/setting-card';
import '../../index.less';

const DEFAULT_MENU_VALUE = 'toolbar';
const cls = `${ebdfClsPrefix}-funcSetting`;
const getTableData = () => {
  let tableData = [
    {
      id: 'scale',
      name: getLabel('247901', '界面缩放'),
      info: getLabel('247903', '调节思维导图显示比例'),
    },
    {
      id: 'expand_all',
      name: getLabel('55985', '全部展开'),
      info: getLabel('247905', '展开思维导图全部节点'),
    },
    {
      id: 'collapse_all',
      name: getLabel('247902', '全部折叠'),
      info: getLabel('247904', '收起思维导图全部节点'),
    },
    {
      id: 'goRoot',
      name: getLabel('261990', '回到根节点'),
      info: getLabel('261991', '点击回到根节点'),
    },
  ];
  // if (isAdvancedView()) {
  //   tableData = [
  //     ...tableData,
  //     {
  //       id: 'fullScreen',
  //       name: getLabel('261408', '全屏查看'),
  //       info: getLabel('261409', '全屏查看思维导图'),
  //     },
  //   ];
  // }
  return tableData;
}
interface MindFuncSettingProps extends React.Attributes {
  value: MindFuncProps;
  config: any;
  onChange: (value: any, action?: any) => void;
  onConfigChange: (value: any) => void;
  id?: any;
}
interface MindFuncBaseProps {
  isShowExpandNum: boolean; // 是否显示带数量的收起按钮
  useScrollBar: boolean; // 开启滚动条
}
interface MindFuncToolBarItemProps {
  [x: string]: any;
}
export interface MindFuncProps {
  base: MindFuncBaseProps;
  toolbar: MindFuncToolBarItemProps[];
}
export interface MindFuncToolBarTableItemProps {
  id: string
  name: string
  info: string
  status?: boolean
}

export default class MindMapFuncSetting extends PureComponent<MindFuncSettingProps> {
  state = {
    visible: false,
    isCheckAll: false,
    menuValue: DEFAULT_MENU_VALUE,
    base: COMP_DEFAULT_CONFIG().funcSetting.base,
    toolbar: COMP_DEFAULT_CONFIG().funcSetting.toolbar,
    tableData: [] as MindFuncToolBarTableItemProps[]
  };
  componentDidMount() {
    this.init();
  }
  init = () => {
    const { config } = this.props;
    const setting = getFuncSetting(config);
    // this.initBase(setting.base);
    this.initToolBar(setting.toolbar);
  };
  // *初始化基础配置
  initBase = (setting: MindFuncBaseProps) => {
    if (isEmpty(setting)) {
      return;
    }
    this.setState({ base: setting });
  };
  // *初始化工具栏
  initToolBar = (setting: MindFuncToolBarItemProps[]) => {
    // *新的结构 后续都以这种为准
    const tableData = getTableData()
    let sortedTableData = [] as MindFuncToolBarTableItemProps[]
    // 根据setting来排序当前tableData
    for (let i = 0; i < setting.length; i++) {
      const key = Object.keys(setting[i])[0];
      const item = tableData.find((it) => it.id === key);
      if (item) {
        sortedTableData.push({ ...item })
      }
    }
    const isCheckAll = sortedTableData.every((item) => {
      const idx = setting.findIndex((it) => it[item.id]);
      return setting[idx] && setting[idx][item.id];
    });
    this.setState({ toolbar: setting, isCheckAll, tableData: sortedTableData });
  };
  handleDialogVisible = () => {
    if (!this.state.visible) {
      //开启时重置状态
      this.init();
    }
    this.setState({ visible: !this.state.visible, menuValue: DEFAULT_MENU_VALUE });
  };
  onSave = () => {
    const { onConfigChange } = this.props;
    const { base, toolbar } = this.state;
    const payload = {
      base,
      toolbar,
    };
    onConfigChange({ funcSetting: payload });
    this.handleDialogVisible();
  };
  checkAll = () => {
    let { isCheckAll, toolbar } = this.state;
    this.setState({ isCheckAll: !isCheckAll }, () => {
      // @ts-ignore
      toolbar = toolbar.map((item: any) => {
        const key = Object.keys(item)[0];
        return { [key]: !isCheckAll };
      });
      this.setState({ toolbar });
    });
  };
  onDrag = (data: any[], evt: any) => {
    const { toolbar } = this.state
    let settings: { [key: string]: any }[] = [];
    data.forEach((it) => {
      const idx = toolbar.findIndex((item) => Object.keys(item)[0] === it.id);
      settings.push(toolbar[idx]);
    });
    this.setState({ toolbar: [...settings] });
  };
  updateStatus = (data: any) => {
    const { toolbar, tableData } = this.state
    const _toolbar = toolbar.map((item: any) => {
      const key = Object.keys(item)[0];
      if (key === data.id) {
        return { [key]: !item[key] };
      }
      return item;
    });
    const checkAll = _toolbar.filter(i => tableData.find(k => k.id === Object.keys(i)[0])).find((it: any) => !Object.values(it)[0]);
    this.setState({ toolbar: _toolbar, isCheckAll: !checkAll });
  };
  onMenuChange = (value: string) => {
    this.setState({ menuValue: value });
  };
  renderToolbar = () => {
    const { isCheckAll, tableData } = this.state;
    const columns: ITableColumn[] = [
      {
        title: getLabel('230882', '功能名称'),
        dataIndex: 'name',
        width: 100,
      },
      {
        title: getLabel('247906', '功能说明'),
        dataIndex: 'info',
      },
      {
        title: getLabel('54086', '启用'),
        customRenderTh: (columns: any) => {
          return (
            <Checkbox weId={`${this.props.weId || ''}_jfdp1l`} value={isCheckAll} onChange={this.checkAll}>
              {getLabel('55009', '开启')}
            </Checkbox>
          );
        },
        bodyRender: (data) => {
          const { toolbar = [] } = this.state
          const idx = toolbar.findIndex((it: any) => it[data.id]);
          return <Checkbox weId={`${this.props.weId || ''}_9p2vyk`} value={ idx > -1 ? !!toolbar[idx] : false} onChange={() => this.updateStatus(data)}></Checkbox>;
        },
        width: 120,
        dataIndex: 'status',
      },
    ];
    return <Table weId={`${this.props.weId || ''}_8bi8l8`} columns={columns} data={tableData} sortable sortableType={`icon`} onDrag={this.onDrag} />;
  };
  renderBase = () => {
    const { base } = this.state;
    return (
      <div>
        {/* <SettingCard
          compKey='switch'
          value={base.isShowExpandNum}
          title={getLabel('262189', '显示带数量的收起按钮')}
          desc={getLabel('262190', '启用后，展开图标上会显示子节点的数量')}
          onCheck={(value: boolean) => {
            this.setState((prevState) => ({ ...prevState, base: { ...base, isShowExpandNum: value } }));
          }}
        /> */}
        {/* <SettingCard
          compKey='switch'
          value={base.useScrollBar}
          title={getLabel('262427', '显示滚动条')}
          desc={getLabel('262428', '启用后，思维导图画布内会显示横向纵向滚动条')}
          onCheck={(value: boolean) => {
            this.setState((prevState) => ({ ...prevState, base: { ...base, useScrollBar: value } }));
          }}
        /> */}
        {/* <SettingCard
          compKey='btn'
          value={base.useScrollBar}
          title={'switch layout'}
          desc={'desc'}
          onCheck={(value: boolean) => {
            this.setState((prevState) => ({ ...prevState, base: { ...base, useScrollBar: value } }));
          }}
          btnProps={{
            title: 'setting',
            type: 'primary',
            onClick: () => {},
            size: 'small',
          }}
        /> */}
      </div>
    );
  };
  renderContent = () => {
    const { menuValue } = this.state;
    const content: { [x: string]: JSX.Element } = {
      base: this.renderBase(),
      toolbar: this.renderToolbar(),
    };
    return content[menuValue] ?? <div />;
  };
  render() {
    const { visible, menuValue } = this.state;
    let menuConfig = {};
    // menuConfig = {
    //   menus: [
    //     {
    //       id: 'toolbar',
    //       content: getLabel('262191', '底部工具栏配置'),
    //     },
    //     {
    //       id: 'base',
    //       content: getLabel('94024', '基础设置'),
    //     },
    //   ],
    //   menuValue: menuValue,
    //   onMenuChange: this.onMenuChange,
    // };
    return (
      <>
        <div className={`${ebdfClsPrefix}-icon ${cls}-icon`}>
          <Icon weId={`${this.props.weId || ''}_2hjaxx`} name='Icon-set-up-o' onClick={this.handleDialogVisible}></Icon>
        </div>
        {visible ? <Dialog weId={`${this.props.weId || ''}_s5f0ef`}
          title={getLabel('239406', '功能配置')}
          closable
          width={600}
          destroyOnClose
          visible={visible}
          onClose={this.handleDialogVisible}
          mask
          footer={[
            <Button weId={`${this.props.weId || ''}_9le4rz`} key='sure' type='primary' onClick={this.onSave}>
              {getLabel('40565', '确定')}
            </Button>,
            <Button weId={`${this.props.weId || ''}_4assuu`} key='cancel' onClick={this.handleDialogVisible}>
              {getLabel('53937', '取消')}
            </Button>,
          ]}
          {...menuConfig}
        >
          {this.renderContent()}
        </Dialog> : null}
      </>
    );
  }
}
