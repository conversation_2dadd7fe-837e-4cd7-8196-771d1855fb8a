export type WidthParams = {
  width: string;
  resize?: boolean;
  minResizeWidth?: number;
  maxResizeWidth?: number;
};

export const dfDialogWidthParams: WidthParams = {
  width: '800px',
  resize: true,
  minResizeWidth: 800,
  maxResizeWidth: 1400,
};

export const getPercentWidth = (parentWidth: number, percent: string) => {
  const width = getWidthFn(parentWidth, percent, { min: 800, max: 1400 });
  return { ...dfDialogWidthParams, width: `${width}px` };
};

export function getByPersent(parentWidth: number, percent: string) {
  return Math.floor(parentWidth * ((parseInt(percent, 10) || 50) / 100));
}

export function getWidthFn(
  parentWidth: number,
  percent: string,
  { min, max }: { min: number; max: number },
) {
  const width = getByPersent(parentWidth, percent);
  if (width < min) return min;
  if (width > max) return max;
  return width;
}

export default getPercentWidth;
