import { CorsComponent } from '@weapp/ui';
import React, { PureComponent } from 'react';
export const EBUILDER_PAGE_INFO_LOCALEX_CACHE_KEY = 'ebuilder_page_info_localex_cache_key';
export const GET_URL = '/api/bs/ebuilder/designer/multilangset/getdata';
export const SAVE_URL = '/api/bs/i18n/multilangLabel/saveLabelAss';
export const INIT_URL = '/api/bs/i18n/multilangLabel/initTemplateLabel';
export const MODULE = 'ebuilder';
export default class LocaleEx extends PureComponent<any> {
  state = {
    value: ''
  }
  componentDidMount() {
    const { value } = this.props
    this.setState({value})
  }
  onChange = (val: any) => {
    this.props.onChange?.(val);
  };
  render(): React.ReactNode {
    const {value} = this.state
    return (
      <CorsComponent
        weId={`${this.props.weId || ''}_ljo6pf`}
        app="@weapp/ebdcoms"
        compName="LocaleEx"
        module="ebuilder"
        {...this.props}
        value={value}
        onBlur={this.onChange}
        onChange={(value: any) => this.setState({value})}
      />
    );
  }
}

export class LocaleExByRootNode extends PureComponent<any> {
  render(): React.ReactNode {
    return <LocaleEx weId={`${this.props.weId || ''}_9y9dba`} targetId={this.props.pageId} value={this.props?.config?.name} onChange={this.props.onChange} />
  }
}
