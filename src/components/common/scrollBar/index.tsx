/**
 * 容器内滚动条
 */
import { useRef, useState, useEffect } from 'react';
import { ebdfClsPrefix } from '../../../constants';
import './index.less';

interface Props {
  mindMapInstance: any;
  hideScrollBar?: boolean; // 是否显示滚动条
}
const cls = `${ebdfClsPrefix}-main-scrollbar`;
let resizeTimer: any;

const Scrollbar = (props: Props) => {
  const { mindMapInstance: ins, hideScrollBar } = props;
  const horizontalScrollbarRef = useRef(null as any);
  const verticalScrollbarRef = useRef(null as any);
  const [verticalScrollbarStyle, setVerticalScrollbarStyle] = useState({});
  const [horizontalScrollbarStyle, setHorizontalScrollbarStyle] = useState({});

  useEffect(() => {
    setScrollBarWrapSize();
    ins.on('scrollbar_change', updateScrollbar);
    window.addEventListener('resize', onResize);
    return () => {
      ins.off('scrollbar_change', updateScrollbar);
      window.removeEventListener('resize', onResize);
    };
  }, []);
  // 向插件传递滚动条宽高数据
  const setScrollBarWrapSize = () => {
    if (!ins.scrollbar) return;
    const { width } = horizontalScrollbarRef.current.getBoundingClientRect();
    const { height } = verticalScrollbarRef.current.getBoundingClientRect();
    ins.scrollbar.setScrollBarWrapSize(width, height);
  };

  // 窗口尺寸变化
  const onResize = () => {
    clearTimeout(resizeTimer);
    resizeTimer = setTimeout(() => {
      setScrollBarWrapSize();
    }, 300);
  };

  // 调用插件方法更新滚动条位置和大小
  const updateScrollbar = ({ vertical, horizontal }: any) => {
    setVerticalScrollbarStyle({
      top: vertical.top + '%',
      height: vertical.height + '%',
    });
    setHorizontalScrollbarStyle({
      left: horizontal.left + '%',
      width: horizontal.width + '%',
    });
  };
  // 垂直滚动条按下事件调用插件方法
  const onVerticalScrollbarMousedown = (e: any) => {
    ins.scrollbar.onMousedown(e, 'vertical');
  };

  // 垂直滚动条点击事件调用插件方法
  const onVerticalScrollbarClick = (e: any) => {
    // ins.scrollbar.onClick(e, 'vertical');
  };

  // 水平滚动条按下事件调用插件方法
  const onHorizontalScrollbarMousedown = (e: any) => {
    ins.scrollbar.onMousedown(e.nativeEvent, 'horizontal');
  };

  // 水平滚动条点击事件调用插件方法
  const onHorizontalScrollbarClick = (e: any) => {
    // ins.scrollbar.onClick(e.nativeEvent, 'horizontal');
  };
  const stopPropagation = (e: any) => {
    e && e.stopPropagation();
  };
  return (
    <div className={cls}>
      {/* 竖向 */}
      <div className={`scrollbar verticalScrollbar ${hideScrollBar ? 'hide' : 'show'}`} ref={verticalScrollbarRef} onClick={onVerticalScrollbarClick}>
        <div className='scrollbarInner' onMouseDown={onVerticalScrollbarMousedown} style={verticalScrollbarStyle} onClick={stopPropagation}></div>
      </div>
      {/* 横向 */}
      <div className={`scrollbar horizontalScrollbar ${hideScrollBar ? 'hide' : 'show'}`} ref={horizontalScrollbarRef} onClick={onHorizontalScrollbarClick} onMouseDown={stopPropagation}>
        <div className='scrollbarInner' onClick={stopPropagation} onMouseDown={onHorizontalScrollbarMousedown} style={horizontalScrollbarStyle}></div>
      </div>
    </div>
  );
};
export default Scrollbar;
