/**
 * 通用的设置卡片
 */
import { Switch, Button, ButtonProps } from '@weapp/ui';
import { Else, If, Then } from 'react-if';
import { ebdfClsPrefix } from '../../../constants';
import './index.less';

interface Props {
  compKey?: string;
  value: boolean;
  title: string;
  onCheck?: (value: boolean) => void;
  customRenderRight?: JSX.Element;
  desc?: string;
  btnProps?: ButtonProps;
  weId: string;
}
const cls = `${ebdfClsPrefix}-common-setting-card`;
const SettingCard = (props: Props) => {
  const renderRightByCompKey = () => {
    const {btnProps, compKey, onCheck, value} = props
    switch (compKey) {
      case 'switch':
        return <Switch weId={`${props.weId || ''}_a3bwbl`} value={value} onChange={onCheck} />
      case 'btn':
        return <Button weId={`${props.weId || ''}_e89yyp`} onClick={btnProps?.onClick} type={btnProps?.type || 'default'} size={btnProps?.size || 'middle'}>{btnProps?.title}</Button>
      default:
        return <div />
    }
  }
  return (
    <div className={cls}>
      <div className={`${cls}-left`}>
        <div className={`${cls}-left-title`}>{props.title}</div>
        <If weId={`${props.weId || ''}_7sd81c`} condition={!!props.desc}>
          <Then weId={`${props.weId || ''}_soszlr`}>
            <div className={`${cls}-left-desc`}>{props.desc}</div>
          </Then>
        </If>
      </div>
      <div className={`${cls}-right`}>
        <If weId={`${props.weId || ''}_7fmnka`} condition={!!props.customRenderRight}>
          <Then weId={`${props.weId || ''}_uisrrt`}>{props.customRenderRight}</Then>
          <Else weId={`${props.weId || ''}_2b7bth`}>
            {renderRightByCompKey()}
          </Else>
        </If>
      </div>
    </div>
  );
};
export default SettingCard;
