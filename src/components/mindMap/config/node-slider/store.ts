import { getLabel } from '@weapp/utils';
import {
  action, computed, observable, toJS
} from 'mobx';
import { EtComponentKey,BrowserType } from '../../../common/constants/EtComponent';
import { FormFieldItem } from '../../../../types/common';
import { isEbRelationBrowser, isEteams } from '../../../../utils';
import ConfigBaseStore from '../../../common/ConfigBaseStore';
import { DataSetField, DataSetItem } from '../../../common/types';

export class NodeSliderConfigStore extends ConfigBaseStore {
  @observable etFields: any[] = [];

  @observable options: any[] = [];

  @action
  getEtFields = () => {
    this.etFields = this.exchange2EtComponentField(this.fields);
  };

  @computed
  get ebuilderField() {
    // 获取本级节点字段
    return this.getOptionsByComponentKey(this.etFields, (f: FormFieldItem) => {
      if (`${f.multiSelect}` !== 'true' || isEteams(this.dataset)) {
        if (f.fieldName === 'id' || f.mainPrimaryKey || f.fieldName === 'name') {
          // id,标题
          return true;
        }
        if ([
          EtComponentKey.Text,
          EtComponentKey.TextArea,
          EtComponentKey.Employee,
          EtComponentKey.Creator,
          EtComponentKey.Updater,
          EtComponentKey.Department,
          EtComponentKey.Subcompany,
          EtComponentKey.Number,
        ].includes(f.componentKey)) {
          return true;
        }
        if (isEbRelationBrowser(f)
        ) {
          return true;
        } if (BrowserType.includes(f.componentKey)) {
          return true;
        }
      }
      return false;
    });
  }
  @computed
  get SupFieldField() {
    // 获取上级节点字段-就是本级节点数据源字段筛选主键和关联ebuilder
    // 本级节点未选择数据源时不返回
    return this.getOptionsByComponentKey(this.etFields, (f: FormFieldItem) => {
      // 数仓数据源放开
      if (`${f.multiSelect}` !== 'true' || isEteams(this.dataset)) {
        if (f.mainPrimaryKey || f.componentKey === EtComponentKey.Ebuilder) {
          return true;
        }
      }
      return false;
    });
  }

  getOptionsByComponentKey = (fields: DataSetField[], filter: (f: FormFieldItem) => boolean) => {
    const options = this.exchange2EtComponentField(fields)
      .filter(filter)
      .map((f: FormFieldItem) => ({
        id: f.fieldId,
        content: f.fieldShowName || f.fieldName,
      }));
    if (options.length) {
      options.unshift({
        id: '',
        content: getLabel('40502', '请选择'),
      });
    }
    return options;
  };

  @action('获取数据源 源字段')
  getDataFields = async (dataset: DataSetItem) => {
    if (dataset?.id) {
      this.getEtFields();
    }
  };
  @action reset = () => {
    this.etFields = []
    this.options = []
  }
}

export default new NodeSliderConfigStore();
