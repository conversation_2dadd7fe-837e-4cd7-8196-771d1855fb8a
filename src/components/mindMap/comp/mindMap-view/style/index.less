@import (reference) '../../../../../style/index.less';

.ebcom.ebcom-mindmap {
  width: 100%;
  height: 100% !important;

  .@{mindMapViewPrefix} {
    width: 100%;
    height: 100%;
    overflow: hidden;

    >div {
      display: flex;
    }
  }

  .ui-spin-nested-loading,
  .ui-spin-container {
    width: 100%;
    height: 100%;
  }

  &>.content {
    display: flex;
    flex-direction: column;
    padding: 0 !important;
  }
}

.@{mindMapViewPrefix}-ctLoading {
  display: flex;
  justify-content: center;
  align-items: center;
  max-height: calc(100vh - 100px);
}

.@{mindMapViewPrefix}-container {
  width: 100%;
  height: 100%;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  position: relative;

  &.fromEbuilder {
    padding-top: 50px;

    // 表单设计器下新增最小高度
    .ui-mind-map-container {
      min-height: 500px;
    }
  }

  &-content {
    height: 100%;
    overflow: auto;
    display: flex;
    flex-direction: column;
  }

  .@{mindMapViewPrefix}-header {
    width: 100%;
    height: 50px;
    position: absolute;
    top: 0;
    left: 0;
    right: 0;

    .ui-title {
      height: 50px;
    }

    .ui-title-left {
      margin-left: var(--h-spacing-md);
    }
  }
}

.@{mindMapViewPrefix}-node-wrapper {
  position: relative;

  .@{mindMapViewPrefix}-expand-icon {
    position: absolute;
    right: 10px;
    top: 50%;
  }
}

.@{mindMapViewPrefix} {
  height: 0;
  flex: 1 1 0;

  .jsmind-inner {
    z-index: 1;
  }

  .smm-container {
    .@{mindMapViewPrefix}-card {
      transition: background-color 0.3s;

      &-unShowFields {
        font-size: var(--font-size-12);
        color: var(--secondary-fc);
      }

      &-sm {
        padding: 6px;
      }

      &-root {
        font-size: var(--font-size-12);
      }

      border-radius: 3px;
      width: 100%;
      height: 100%;
      display: flex;
      flex-wrap: wrap;

      &-adaptive {
        .ebcoms-list {
          .ebcoms-field-render-text {
            white-space: nowrap;
          }

          .ui-textarea {
            min-height: initial;
            white-space: initial;
            max-width: initial;
          }
        }
      }

      .ebcoms-list {
        padding: var(--h-spacing-md);
        font-family: 'sans-serif';
        height: 100%;
        position: initial; //hack 兼容safari

        &-grid-row {
          position: initial; //hack 兼容safari
        }

        &:hover {
          background: none;
          transition: all 0.3s;
        }

        &:not(:last-child) {
          border-bottom: none;
        }
      }
    }
  }

  /* ebdform theme */
  jmnode,
  .smm-container {
    border-radius: 3px;
    // overflow: hidden;
    box-shadow: none;
    padding: 0;
    font: unset;
    // background-color: transparent;
    // 去掉最大宽度400限制
    max-width: initial !important;

    .@{mindMapViewPrefix}-card {
      padding: 10px 0 10px 10px;
      transition: background-color 0.3s;
      border-radius: 8px;
      width: 100%;
      height: 100%;
      display: flex;
      flex-wrap: wrap;
      border: 1px solid rgba(221, 224, 227, 0.5);
      ;
      box-shadow: 0px 0px 4px 0px rgba(221, 224, 227, 0.2);

      &-sm {
        padding: 6px;
      }

      &-root {
        font-size: var(--font-size-12);
        border-radius: 6px;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        width: 100%;
        padding: 0;
        background-color: #0459B8;
        color: #fff;

        &-iconContainer {

          >.ebcoms-assets-img,
          >.ebcoms-assets-icon {
            width: 100% !important;
            height: 100% !important;
          }
        }

        &-topic-top {
          margin-top: 10px;
        }
      }

      &-second,
      &-third {
        background-color: #F6F9FC;
        box-shadow: none;
      }

      &-second {

        .ebcoms-field-render-text .ui-textarea,
        .ebcoms-list-grid-cell-custom>span {
          // white-space: pre-wrap;
        }

        .ebcoms-list {
          &-vertical-layout {
            .ebcoms-list-grid-row{
              .ebcoms-list-flex{
                flex-wrap: wrap;
              }
            }

            // 二级节点标题和描述默认为第一 第二个节点
            .ebcoms-list-grid-row:first-child {
              .ebcoms-list-grid-cell {
                color: #0459B8;
                font-size: 14px;
              }

              // 默认换行
              .ebcoms-list-flex.ebcoms-list-grid-row-main {
                flex-wrap: wrap;
              }
            }

            .ebcoms-list-grid-row:nth-child(2) {
              .ebcoms-list-grid-cell {
                font-size: 12px;
                color: #666;
              }
            }
          }
        }
      }

      &-third {
        ebcoms-list-grid-row {
          .ebcoms-list-grid-cell {
            color: #111;
          }
        }
      }

      &-customRender {
        flex-wrap: nowrap;
        overflow-x: scroll;
      }

      // 去掉默认的28px
      .ui-textarea {
        min-height: initial;
        white-space: initial;
      }

      .ebcoms-list {
        padding: 0;
        font-family: 'sans-serif';
        height: 100%;
        position: initial; //hack 兼容safari

        &:not(:last-child) {
          border-bottom: none;
        }

        &-grid-row {
          position: initial; //hack 兼容safari
        }

        &:not(:last-child) {
          border-bottom: none;
        }

        &:hover {
          background: none;
        }
      }

      .ebcoms-list-flex.ebcoms-list-grid-row:not(:last-child) {
        .ebcoms-list-flex.ebcoms-list-grid-cell {

          &>div[title]:not([title=""]),
          &>span[title]:not([title=""]) {
            padding-bottom: 3px;
          }
          .ebcoms-field-render-hrm-card{
            padding-bottom: 3px;
          }
        }
      }

      .ebcoms-list-flex.ebcoms-list-grid-cell {
        padding: 0 !important;
      }
      .ebcoms-list-render-cell{
        white-space: initial !important;
      }

      .ebcoms-list-render-cell {
        .ebcoms-list-flex.ebcoms-list-grid-cell {

          &>div[title]:not([title=""]),
          &>span[title]:not([title=""]) {
            padding-right: 10px;
          }
          &  > .ebcoms-list-grid-cell-custom{
            padding-right: 10px;
          }
          .ebcoms-list-ellipsis{
            white-space: initial;
          }
        }
      }

      .ebcoms-list-grid-row:not(:last-child) {
        margin-bottom: 0;
      }
    }

    .@{mindMapViewPrefix}-card-active {
      cursor: pointer;
      width: 100%;
    }

    // 根节点
    .mind-node-root {
      background-color: var(--primary);
      color: white;
    }

    .mind-node-project {
      cursor: pointer;
      position: relative;
      border: 1px solid var(--primary);

      &>* {
        position: relative;
        z-index: 2;
      }

      &:after {
        content: '';
        position: absolute;
        left: 0;
        top: 0;
        bottom: 0;
        right: 0;
        background-color: var(--primary);
        opacity: 0.1;
      }
    }

    .mind-node-stage {
      border: 1px solid #dadada;
      background-color: rgb(249, 249, 249);
    }

    .mind-node-task {
      padding-left: 8px;
      font-size: var(--font-size-12);
    }

    .finished {
      text-decoration: line-through;
    }

    .name+.manager {
      margin-left: 5px;
    }

    .manager,
    .progress {
      color: var(--regular-fc);
      font-size: var(--font-size-12);
    }
  }
}