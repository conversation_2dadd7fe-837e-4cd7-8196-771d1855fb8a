import { CompDesignStoreType } from '@weapp/designer';
import { DatasetValue } from '@weapp/ebdcoms';
import { AnyObj, CorsComponent } from '@weapp/ui';
import { corsImport, getLabel, isArray, isEmpty, isEqual } from '@weapp/utils';
// import isArray from 'lodash-es/isArray';
// import isEmpty from 'lodash-es/isEmpty';
// import isEqual from 'lodash-es/isEqual';
import { toJS } from 'mobx';
import { observer } from 'mobx-react';
import React from 'react';
import { EventsAction, EventsType } from './type';
import { PageType } from '../../../../components/common/constants';
import {
  DefIdentifyIdVal,
  getDftClickPageData,
  getInitIconStyle,
  getParamsOpts,
  getSystemIcon,
  transActionsToEventGroup,
} from './utils';
import { NotUseFields } from "../../../../constants";

const _dftConfig = [
  {
    eventId: EventsType.Click,
    actionIds: [EventsAction.NEWPAGE],
    disableDelete: true,
  },
];

interface EventsActionComProps {
  defaultEvt: EventsType;
  store: CompDesignStoreType;
  onDatasetChange?: (datasetVal?: DatasetValue, type?: string) => void;

  [props: string]: any;
}

@observer
export default class EventsActionCom extends React.PureComponent<EventsActionComProps> {
  state = {
    paramOpts: [],
    defIdentifyIdVal: DefIdentifyIdVal,
  };

  componentDidMount() {
    this.getFields();
  }

  componentDidUpdate(preProps: any) {
    if (!isEqual(preProps.config.dataset, this.props.config?.dataset)) {
      this.getFields();
    }
  }

  getFields = () => {
    const { dataset } = this.props.config || {};

    if (dataset && dataset.id) {
      corsImport('@weapp/components').then(({ dsUtils }) => {
        dsUtils.getFields(dataset, '', false, false).then((fieldDatas: any) => {
          const { paramOpts, defIdentifyIdVal } = getParamsOpts(fieldDatas,'',this.fieldFilter);

          this.setState({ paramOpts, defIdentifyIdVal });
        });
      });
    }
  };

  onSure = (val: any) => {
    const { onChange, compType } = this.props;
    const { eventGroup = [] } = val;
    const pageData = getDftClickPageData(eventGroup, this.props.defalutEvt || EventsType.Click);
    const _data = {
      ...val,
    };

    delete _data.record;

    // 选系统菜单时自动带出标题和图标
    // (前置条件为其对应内容空时)
    const page: any = pageData?.linkInfo?.page;
    if (page?.pageType === PageType.SystemPage) {
      _data.name = _data.name || pageData?.linkInfo?.page?.pageName;
      _data.icon = _data.icon || getSystemIcon(page?.pageId);
      _data.iconStyle = _data.iconStyle || getInitIconStyle(compType);
    }
    onChange(toJS(_data));
  };

  onDatasetChange = (datasetVal?: DatasetValue, type?: string) => {
    const onDatasetChange = this.props.onDatasetChange || this.props.roorProps?.onDatasetChange;
    onDatasetChange?.(datasetVal, type);
  };

  getPageLinkConfig = () => {
    const { layoutInfo, config = {} } = this.props;
    const { paramOpts, defIdentifyIdVal } = this.state;

    let pageLinkMode = '';
    // 判断列表开启了数据分组，支持数据列表钻取，设置mode给pageLink
    if (config.dataset?.dataGroupConfig && !isEmpty(config.dataset?.dataGroupConfig)) {
      pageLinkMode = 'dataList';
    }
    return {
      showLinkField: true,
      layoutInfo,
      paramOpts,
      defIdentifyIdVal,
      mode: pageLinkMode,
      dataset: config.dataset,
      fieldFilter :this.fieldFilter
    };
  };

  fieldFilter = (field:any)=>{
    //屏蔽不支持字段
    if (NotUseFields.includes(field?.id)) {
      return false
    } 
    return true
  }

  render() {
    const getJsPlaceholder = (): string => {
      const jsPlaceholder = `${getLabel('221701','可通过this对象获取行数据，this对象格式',)}：{target: ..., data: {...}}
    //${getLabel('181191', '获取行数据，比如该行数据为')}{id: 1, name: Joy, age: 18}
    var data = this.data;    //data = {id: 1, name: Joy, age: 18}
    var name = data.name;    //${getLabel('181192', '获取行数据字段为name的值')}，name=Joy`;
      return jsPlaceholder;
    };

    const {
      store,
      pageId,
      value,
      appid,
      layoutInfo,
      pageScope,
      client,
      jsCodePlaceholder = getJsPlaceholder(),
      defaultEvt,
      forceDataEvents = [],
      dftConfig = _dftConfig,
    } = this.props;

    const Page = {
      id: pageId,
      module: pageScope,
      appid,
      client,
      datasetVals: layoutInfo?.datasetVals,
    };

    const newVal: AnyObj = isArray(toJS(value)) ? transActionsToEventGroup(value) : value;
    const eventGroup = toJS(newVal?.eventGroup) || [];
    let props = {
      forceDataEvents: [EventsType.Click],
    };
    if (defaultEvt === EventsType.Click) {
      props = {
        ...props,
        forceDataEvents: [...forceDataEvents, EventsType.Click],
      };
    }

    return (
      <>
        <CorsComponent
          weId={`${this.props.weId || ''}_9zez7n`}
          app="@weapp/ebdcoms"
          compName="EventsActionBtn"
          eventGroup={eventGroup}
          store={store || this.props.rootProps?.store}
          page={Page as any}
          disabled={false}
          dftConfig={dftConfig}
          onSure={this.onSure}
          onDatasetChange={this.onDatasetChange}
          pageLinkConfig={this.getPageLinkConfig()}
          jsCodePlaceholder={jsCodePlaceholder}
          {...props}
        />
      </>
    );
  }
}
