import { ReactNode } from 'react';
export interface IComDescription {
  id: string;
  /** 组件名称 */
  name: string;
  /** 组件类型 */
  type: string;
  /** 组件所在前端库的npm包名 */
  package: string;
  /** 组件分类id */
  category: string;
  /** 组件图片路径 */
  icon: string;
  /** 是否禁用，禁用状态下组件不在列表中显示 */
  disabled?: boolean;
  [key: string]: any;
}

export type ComItemData = {
  id: string;
  /** 分类标题，当title为空时，不渲染标题 */
  title?: ReactNode;
  /** 组件分类id，当组件无分类时不传category */
  category?: string;
  /** 组件数据 */
  items: IComDescription[];
};

/** 组件分类 */
export interface IComCategory {
  /** 分类id */
  id: string;
  /** 分类名称 */
  name: string;
  /** 组件信息 */
  comps?: IComDescription[];
  /** 二级分类组件 */
  childs?: IComCategory[];
  [key: string]: any;
}

export type ComListDatas = ComItemData[];
