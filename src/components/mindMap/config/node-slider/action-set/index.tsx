import React from 'react';
import { observer } from 'mobx-react';
import { ActionSetComProps, EventsActionComProps } from './types';
import ActionSetCom from './ActionSetCom';
import { EventsType } from '../../../../common/plugins/events-action/type';
import EventsActionCom from '../../../../common/plugins/events-action';

@observer
export default class ActionSet extends React.PureComponent<ActionSetComProps&EventsActionComProps> {
  render() {
    const { rootProps } = this.props;
    if (rootProps?.config?.fromEbuilder) {
      return <ActionSetCom weId={`${this.props.weId || ''}_cqf91d`} {...this.props} />;
    }
    return <EventsActionCom weId={`${this.props.weId || ''}_vjqjnu`} {...this.props} defaultEvt={EventsType.Click} />;
  }
}
