import React, { PureComponent } from 'react';
import { Input } from '@weapp/ui';
import { getLabel } from '@weapp/utils';
const { InputNumber } = Input;

export default class ExpandItem extends PureComponent<any> {
  render() {
    const { onChange, weId, value, placeholder = getLabel('87182', '默认展开级数'),min = 0, max = 100,
    style = {width: '100%'}, hideOps = true, align = '', suffix = ''
   } = this.props
    return <InputNumber weId={`${this.props.weId || ''}_h8iek5`}
      key={weId}
      onBlur={onChange}
      min={min}
      max={max}
      placeholder={placeholder}
      style={style}
      defaultValue={value}
      hideOps={hideOps}
      align = {align}
      suffix={suffix}
      precision={0}
    />
  }
}
