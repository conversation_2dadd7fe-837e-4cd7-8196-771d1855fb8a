import { SyntheticEvent } from 'react';
import { AnyObj } from '@weapp/ui';
import { corsImport, getLabel, isEmpty, isEqual, cloneDeep, isArray } from '@weapp/utils';
import { DataSet } from '@weapp/ebdcoms';
import commonUtils from '../../../../utils';
import { optimizeNodeTreeList } from '../../engine/drag-tree/utils';
import { NodeNames } from '../../engine/node-config/constants';
import { MindMapKey, MindPluginCenter } from '../../types';
import { TreeDataItem, MindMapDataItem, JmNode, MindMapContentProps } from './types';
import { Direction } from '../../../common/types';
import { COMP_DEFAULT_CONFIG, SysFieldType } from '../../../common/constants';
import { EtComponentKey } from '../../../common/constants/EtComponent';
import { MindMapDefaultLineColor, INIT_NODE_SIZE, INIT_NODE_STYLE, mindMapViewClsPrefix, mindMapUrlParamsCache, MindDefaultPluginName, EbdMindMapEventKeys, MindMapCardFieldAvatarSize } from '../../../../constants';
import { FieldFilterSetProps } from '../../config/comps/node-fieldFilter/types';
import { isEntryEnable, wrapEntry } from '../../../common/utils/encryUtils';
import { invoke, hasRegisterFuncInPlugin, JSONSafeParse } from '../../../../utils';
import { MindMapViewStore } from './store';

const getStr = (str: string = '') => {
  if (!str) return '';
  const div = document.createElement('div');
  div.innerHTML = str;
  const a: any = div;
  return a.textContent;
};
/**
 * 从字符串中提取 data-val 属性中的最后一个数字
 * @param str - 包含 data-val 属性的 HTML 字符串
 * @returns 提取出的数字数组
 */
function extractDataVal(str: string): string[] {
  // 使用正则表达式匹配所有data-val的值
  const regex: RegExp = /data-val="([^"]+)"/g;
  const matches: string[] = [];
  let match: RegExpExecArray | null;

  // 使用while循环找出所有匹配项
  while ((match = regex.exec(str)) !== null) {
    // 提取括号内的完整字符串
    const fullMatch: string = match[1];
    // 提取最后一串数字
    const lastNumber: string = fullMatch.split('.').pop()?.replace(/[{}]/g, '') || '';

    if (lastNumber) {
      matches.push(lastNumber);
    }
  }

  return matches;
}

function calculateTextHeight(text: string, width: number, fontSize: string, fontFamily: string, node?: any) {
  const tempDiv = document.createElement('div');
  tempDiv.style.width = `${width}px`;
  tempDiv.style.position = 'absolute';
  tempDiv.style.left = '-9999px';

  // if (node.id.includes('b8b0f3a737af463c_1005925725068132362')) {
  //   tempDiv.style.left = '0';
  //   tempDiv.style.top = '0';
  // }

  tempDiv.style.fontSize = fontSize;
  tempDiv.style.fontFamily = fontFamily;
  tempDiv.style.lineHeight = 'normal';
  tempDiv.style.whiteSpace = 'pre-wrap';
  // tempDiv.style.wordWrap = 'break-word';
  tempDiv.textContent = text;
  document.body.appendChild(tempDiv);
  const height = tempDiv.offsetHeight;
  // document.body.removeChild(tempDiv);
  return height;
}
export const MindMapNodeMinHeight = 33;
export const MindMapNodeMinWidth = 30;

/**
 * 解析系统字段的字段类型
 * @param fieldNameOrId 系统常量
 */
export function getSystemFieldToStr(fieldNameOrId: string) {
  switch (fieldNameOrId) {
    case '2': // 创建者fieldId: "2"
    case '6': // 最后更新人fieldId: "6"
    case '102':
      return 'creator';
    case '3': // 创建时间fieldId: "3"
    case '103':
      return 'create_time';
    case '5': // 数据ID fieldId: "5"
    case '105':
    case '16': // 审批编号
      return 'id';
    case 'data_status': // 数据状态 fieldId: "8"
    case '8': // 数据状态 fieldId: "8"
    case '108':
      return 'data_status';
    case '1': // 标题 fieldId: "1"
    case '101':
      return 'name';
    case '4': // 最后更新时间
    case '104':
      return 'update_time';
    case '9': // 流程状态 fieldId: "9"
    case '109':
      return 'flow_status';
    case '10': // 当前阶段 fieldId: "10"
    case '1010':
      return 'current_step';
    // case '15':
    //   return SysFieldType.Classification;
    default:
      return fieldNameOrId;
  }
}
export const filterGridData = (grid: any[], nodeData: any) => {
  return grid.filter((i) => {
    return i.find((k: any) => {
      // 特殊处理自定义文本
      if (k.field?.id === '-1') {
        return k.field?.content;
      }
      // 特殊处理序号
      if (k.field?.id === '-3') {
        return k.field?.text;
      }
      const fieldId = getSystemFieldToStr(k.field?.id);
      return nodeData[fieldId];
    });
  });
};
/**
 * 获取节点高
 * 优先取单个节点配置， 没有再取通用配置
 * @param node 节点信息 cardLayout 布局信息
 * @param nodeSize 节点尺寸
 * @param cardLayout 布局信息
 * @param iconStyle 节点图标信息
 * @param countWidth 自适应计算的宽度
 * @returns height
 */
export const getAdaptiveHeight = (node: any, nodeSize?: any, cardLayout?: any, iconStyle?: any, countWidth?: number) => {
  const { isroot, floor } = node;
  let _height = 0;
  // 固定值
  if (nodeSize.height.type === 'fixed') {
    _height = +nodeSize.height.size;
  } else {
    // 自适应
    const { grid = [] } = cardLayout || {};
    const lineHeight = 18;
    const wrapperPadding = 10; // 上下边距
    const rootWrapperPadding = 11; // 根节点上下边距
    const titleDescPadding = 10; // 标题和描述有10px间距
    const rowMarginBottom = 3; // 每一行的距离
    const defaultFontSize = 12; // 默认文字大小
    if (isroot) {
      // 按设计稿处理根节点默认高度
      _height += MindMapNodeMinHeight;
    } else {
      // * 如果节点宽度为固定且为自定义宽度 还需要计算是否换行了
      if (nodeSize.width.type === 'fixed' || nodeSize.height.type === 'adapt') {
        const nodeData = node.isroot ? node.content : JSON.parse(node.content);
        let accumulatedSum = 0;
        grid.forEach((row: any[]) => {
          let rowHeightArr: number[] = [];
          const gridRow = filterGridData(row, nodeData);
          gridRow.forEach((row: any[], rowIndex: number) => {
            const contents = row.map((item: any) => {
              // 是人员头像
              if (`${item.field.id}` === '2') {
                const ruleConfig = item.field.showTrans?.[0]?.ruleConfig;
                if (ruleConfig?.ruleType === 'iconTrans') {
                  return {
                    text: '',
                    style: { ...item.field.style, extraHeight: MindMapCardFieldAvatarSize.mini - rowMarginBottom },
                  };
                }
                const size = ruleConfig?.avatarSize || 'small';
                const avatarSize = MindMapCardFieldAvatarSize[size as keyof typeof MindMapCardFieldAvatarSize];
                const isShowDeptName = ruleConfig?.isShowDeptName;
                const isShowUserName = ruleConfig?.isShowUserName;
                const offset = isShowDeptName && isShowUserName ? 8 : 0;
                return {
                  text: item.field.id,
                  style: { ...item.field.style, fixedHeight: avatarSize + offset },
                };
              }
              // 是进度条 补一下进度条ui(progress-bar)的宽度
              if (item.field.type === 'Number' && item.field.text === getLabel('114589', '进度条')) {
                return {
                  text: `${item.field.text}${item.field.text}`,
                  style: { ...item.field.style },
                };
              }
              // 有字段值
              if (item.field.id in nodeData) {
                let itemInfo = nodeData[item.field.id];
                return {
                  text: Object.prototype.toString.call(itemInfo) === '[object Array]' ? itemInfo.map((i: any) => i.name).join('') : itemInfo,
                  style: item.field.style,
                };
              }
              // 是序号
              if (`${item.field.id}` === '-3') {
                return {
                  text: item.field.id,
                  style: item.field.style,
                };
              }
              // 自定义文本
              if (item.field.id === '-1') {
                const topic = JSONSafeParse(node.topic);
                const content = extractDataVal(item.field?.content);
                if (content && content.length) {
                  let newTexts: string[] = [];
                  content.forEach((i) => {
                    const val = topic[i];
                    if (Array.isArray(val)) {
                      val.forEach((j) => {
                        newTexts.push(j.name);
                      });
                    } else {
                      newTexts.push(topic[i] || '');
                    }
                  });
                  return {
                    text: newTexts.join(','),
                    style: item.field.style,
                  };
                }
              }
              // 纯文本
              return {
                text: item.field.content || '',
                style: item.field.style,
              };
            });
            const newContents = {
              text: '',
              style: {},
            } as any;
            // 同行文本直接拼接
            contents.forEach((element) => {
              newContents.text += element.text;
              newContents.style = { ...newContents.style, ...element.style };
            });
            // 只算当前行最高的
            [newContents].forEach((item: { text: string; style: any; fixedHeight?: number }, idx) => {
              const itemText = item?.text || '';
              const fontSize = item.style.fontSize || extractFontSize(itemText, defaultFontSize);
              const extraHeight = item.style?.extraHeight || item.style?.height || 0;
              const fixedHeight = item.style?.fixedHeight;
              // 有固定高度使用固定高的
              const curentHeight = fixedHeight ? fixedHeight : calculateTextHeight(getStr(itemText), countWidth || nodeSize.width.size || 280, fontSize, 'sans-serif', node) + (extraHeight || 0) + (rowIndex >= 1 ? rowMarginBottom : 0);
              const currentRowIndexHeight = rowHeightArr[rowIndex] || 0;
              const currentRowIndexMaxHeight = Math.max(curentHeight, currentRowIndexHeight);
              rowHeightArr[rowIndex] = currentRowIndexMaxHeight;
            });
            accumulatedSum = rowHeightArr.reduce((sum: number, item: number) => {
              return sum + item;
            }, 0);
          });
        });
        _height = accumulatedSum + wrapperPadding * 2;
      } else {
        // * 节点宽度不固定 都在一行上
        _height = isroot ? lineHeight + rootWrapperPadding * 2 : lineHeight * grid.length + wrapperPadding * 2;
        if (floor === 2 && grid.length > 1) {
          _height += titleDescPadding;
        }
      }
    }
  }
  // 存在图标时 加上图标大小
  if (iconStyle && iconStyle.path) {
    _height += +iconStyle.height;
  }
  if (_height < MindMapNodeMinHeight) {
    _height = MindMapNodeMinHeight;
  }
  return _height;
};
/**
 * 获取节点宽
 * 优先取单个节点配置， 没有再取通用配置
 * @param node 节点信息 cardLayout 布局信息
 * @param nodeSize 节点尺寸
 * @param cardLayout 布局信息
 * @param iconStyle 节点图标信息
 * @param isTitle 是否为标题 标题的font-size 为14
 * @returns width
 */
export const getAdaptiveWidth = (node: any, nodeSize?: any, cardLayout?: any, iconStyle?: any, isTitle?: boolean): { width: number; originWidth: number } => {
  const { isroot } = node;
  const wrapperPadding = 40;
  const blockPadding = 12;
  const defaultFontSize = 12;
  const rowMarginLeft = 14;
  let _width = 0;
  let _originWidth = 0; // 不包含padding的宽度
  // 固定值
  if (nodeSize.width.type === 'fixed') {
    _width = +nodeSize.width.size;
  } else {
    // 自适应
    // 根节点是根节点 单独处理
    if (isroot) {
      _width = +commonUtils.getTextWidthOrHeight(getStr(node.topic), 'width') + wrapperPadding;
    } else {
      const nodeData = JSON.parse(node.content);
      const { grid = [] } = cardLayout || {};
      let maxGridWidth = 0;
      grid.forEach((row: any[]) => {
        const gridRow = filterGridData(row, nodeData);
        let sumWidthArr: number[] = [];
        gridRow.forEach((row: any[], rowIndex: number) => {
          const contents = row.map((item: any) => {
            // 是人员头像
            if (`${item.field.id}` === '2') {
              const username = window?.TEAMS?.currentUser?.username;
              const ruleConfig = item.field.showTrans?.[0]?.ruleConfig;
              if (ruleConfig?.ruleType === 'iconTrans') {
                return {
                  text: username,
                  style: { ...item.field.style, extraWidth: MindMapCardFieldAvatarSize.medium + 30 },
                  y: item.y,
                };
              }
              const size = ruleConfig?.avatarSize || 'small';
              const isShowDeptName = ruleConfig?.isShowDeptName;
              const isShowUserName = ruleConfig?.isShowUserName;
              const avatarSize = MindMapCardFieldAvatarSize[size as keyof typeof MindMapCardFieldAvatarSize];
              let text = '';
              if (isShowDeptName) {
                text = window?.TEAMS?.currentTenant?.tenantName;
              }
              if (isShowUserName) {
                text = username.length > text ? username : text;
              }
              return {
                text: text || item.field.id,
                style: { ...item.field.style, extraWidth: avatarSize },
                y: item.y,
              };
            }
            // 是进度条 补一下进度条ui(progress-bar)的宽度
            if (item.field.type === 'Number' && item.field.text === getLabel('114589', '进度条')) {
              return {
                text: nodeData[item.field.id] || '<div />',
                // 默认给滚动条56px
                style: { ...item.field.style, extraWidth: 56 },
                y: item.y,
              };
            }
            // 有字段值
            if (item.field.id in nodeData) {
              let itemInfo = nodeData[item.field.id];
              // 时间格式
              if (item.field.id === '3') {
                return {
                  text: item.field.format,
                  style: item.field.style,
                  y: item.y,
                };
              }
              // 是标题 标题会带创建时间 需要补'秒'
              // if (item.field.id === '1' && timeReg.test(itemInfo.trim())) {
              //   itemInfo = itemInfo + ':00';
              // }
              return {
                text: Object.prototype.toString.call(itemInfo) === '[object Array]' ? itemInfo.map((i: any) => i.name).join('') : itemInfo,
                style: item.field.style,
                y: item.y,
              };
            }
            // 是序号
            if (`${item.field.id}` === '-3') {
              return {
                text: item.field.id,
                style: item.field.style,
                y: item.y,
              };
            }
            // 自定义文本
            if (`${item.field.id}` === '-1') {
              const topic = JSONSafeParse(node.topic);
              let customContent = item.field?.content;
              // 处理其中的{userid}
              if (item.field?.content.includes('{userid}')) {
                const userid = window?.TEAMS?.currentUser?.id;
                // 替换文本中的{userid}
                customContent = customContent.replace('{userid}', userid);
              }
              const content = extractDataVal(customContent);
              if (content && content.length) {
                let newTexts: string[] = [];
                content.forEach((i) => {
                  const val = topic[i];
                  if (Array.isArray(val)) {
                    val.forEach((j) => {
                      newTexts.push(j.name);
                    });
                  } else {
                    newTexts.push(topic[i] || '');
                  }
                });
                return {
                  text: newTexts.join(','),
                  // 给8px的冗余距离
                  style: { ...item.field.style, extraWidth: 8 },
                  y: item.y,
                };
              }
              return {
                text: customContent || '<div />',
                style: item.field.style,
                y: item.y,
              };
            }
            // 纯文本
            return {
              text: item.field.content || '<div />',
              style: item.field.style,
              y: item.y,
            };
          });
          const total = contents.reduce((sum: number, item: { text: string; style: any; y: number }, currentIndex: number) => {
            const fontSize = nodeSize.fontSize ? nodeSize.fontSize : isTitle && item.y === 0 ? '14px' : item.style.fontSize || `${defaultFontSize}px`;
            const itemText = `${item?.text}` || '';
            let extraWidth = item.style?.extraWidth || 0;
            let countWidth = commonUtils.getTextWidthOrHeight(getStr(itemText), 'width', `${fontSize} sans-serif`);
            countWidth = sum + countWidth + extraWidth + (currentIndex >= 1 ? rowMarginLeft : 0);
            // if (node.floor === 2) {
            //   // 二级节点下 且是第二行 按设计稿默认最大宽度为180 超过就换行
            //   countWidth = countWidth > 160 ? 160 : countWidth;
            // }
            return countWidth;
          }, 0);
          sumWidthArr.push(total);
        });
        const gridMaxWidth = Math.max(...sumWidthArr, 24);
        maxGridWidth = Math.max(maxGridWidth, gridMaxWidth);
      });
      _originWidth = maxGridWidth;
      _width = maxGridWidth + blockPadding * 2;
    }
  }
  // 如果宽度小于图标宽度 以图标宽度为准
  if (iconStyle && iconStyle.path && _width < iconStyle.width) {
    return { width: +(iconStyle.width + wrapperPadding), originWidth: iconStyle.width };
  }
  if (_width < MindMapNodeMinWidth) {
    _width = MindMapNodeMinWidth;
  }
  return { width: +_width, originWidth: +_originWidth };
};
export const setDataByName = (node: any, treeInfoId: any, data: any, parentInfo: any, nodeTreeList: any[]) => {
  const loop = (loopData: any): any => {
    let currentId = loopData.id || '';
    if (loopData.id === '0_0') currentId = 'root_'; // 兼容根节点
    if (loopData.floor === parentInfo.floor && currentId.indexOf(parentInfo.id) > -1) {
      let newData = data.map((j: any) => {
        const floor = parentInfo.floor + 1;
        let id = '';
        for (let i = 0; i < floor; i++) {
          id = id ? `${id}_${commonUtils.UUID()}` : `${treeInfoId}_${j.id}`;
        }
        return { parentid: loopData.id, floor, id, originId: j.id, topic: j.name, content: JSON.stringify(j), children: [], isLeaf: false };
      });
      loopData.children = [...loopData.children, ...newData];
    }
    if (loopData.children) {
      for (let child of loopData.children) {
        loop(child);
      }
    }
  };
  loop(node);
};
/**
 * 处理优化 config
 * 兼容处理历史数据nodeTreeInfo与nodeTreeList配置不匹配问题
 * 以nodeTreeList为正确数据处理无用的nodeTreeInfo（删除后依然存在）
 * 最后更新 config
 * @param config
 */
export const formatConfig = (config: any, onConfigChange?: Function) => {
  let _config: AnyObj = cloneDeep(config);
  const { nodeTreeInfo, nodeTreeList } = _config;
  // 移除无上下级关系的节点
  const _nodeTreeList = optimizeNodeTreeList(nodeTreeList);
  const nodeTreeListIds = _nodeTreeList.map((i: any) => i.id);
  const nodeTreeInfoIds = Object.keys(nodeTreeInfo);
  // 配置一致不处理
  if (nodeTreeListIds.every((i) => nodeTreeInfoIds.includes(i))) {
    return _config;
  }
  let newNodeTreeInfo = {} as AnyObj;
  for (let i in nodeTreeInfo) {
    if (nodeTreeListIds.includes(i)) {
      newNodeTreeInfo[i] = nodeTreeInfo[i];
    }
  }
  _config.nodeTreeInfo = newNodeTreeInfo;
  _config.nodeTreeList = _nodeTreeList;
  // 有更新就再同步给 config
  if (!isEqual(_config, config) && onConfigChange) {
    onConfigChange({ nodeTreeInfo: newNodeTreeInfo, nodeTreeList: _nodeTreeList });
  }
  return _config;
};
const propEqual = (a: any, b: any, propName: string) => {
  if (!a && !b) return true; // 不存在，则直接略过,算是相等，不更新
  if (!a[propName] && !b[propName]) return true;
  return a && a[propName] && b && b[propName] && isEqual(a[propName], b[propName]);
};
// nodeTreeInfo 是否要刷新视图校验
export const checkTreeInfoUpdate = (infoA: any, infoB: any, _comparedKeys: string[] = []) => {
  let needToAjaxGet: boolean = false; // 需要重新初始化去请求信息
  const comparedFunc = (keys: string[], a: any, b: any) => {
    let result = false;
    for (let i = 0, len = keys.length; i < len; i++) {
      if (!propEqual(a, b, keys[i])) {
        result = true;
        break;
      }
    }
    return result;
  };
  // 筛选出所有有数据源的节点，作为有效节点
  Object.keys(infoA).forEach((key: string) => {
    if (needToAjaxGet) return;
    // 逐个获取节点信息
    const dataA = infoA[key] || {};
    const dataB = infoB[key] || {};
    if (!isEmpty(_comparedKeys)) {
      needToAjaxGet = comparedFunc(_comparedKeys, dataA, dataB);
    } else {
      // 判断节点是否含有数据源，有数据源或数据源变化才做比较
      if (NodeNames.Dataset in dataA || NodeNames.Dataset in dataB || (dataA.id === 'root_' && dataB.id === 'root_')) {
        // 比对有效节点，是否含有有效字段,且有效字段是否变化
        let comparedKeys: string[] = [
          NodeNames.Dataset,
          NodeNames.iconInfo,
          NodeNames.IconHeight,
          NodeNames.IconWidth,
          NodeNames.NameShowSwitch,
          NodeNames.Name,
          NodeNames.ShowFields,
          NodeNames.DataFilter,
          NodeNames.DataSort,
          NodeNames.SupField,
          NodeNames.Curnodefield,
          NodeNames.Supnodefield,
          NodeNames.NodeKeySet,
        ];
        needToAjaxGet = comparedFunc(comparedKeys, dataA, dataB);
      } else if (NodeNames.DefaultExpendDepth in dataA || NodeNames.DefaultExpendDepth in dataB) {
        dataA[NodeNames.DefaultExpendDepth] = +dataA[NodeNames.DefaultExpendDepth];
        dataB[NodeNames.DefaultExpendDepth] = +dataB[NodeNames.DefaultExpendDepth];
        needToAjaxGet = comparedFunc([NodeNames.DefaultExpendDepth], dataA, dataB);
      }
    }
  });
  return needToAjaxGet;
};
// nodeTreeList 是否要刷新视图校验
// mark 待废弃 暂不对比nodeTreeList
export const checkTreeListUpdate = (nextInfo: any[], prevInfo: any[]) => {
  let needToAjaxGet: boolean = false; // 是否需要更新
  // const needCheckFields = ['children'];
  // const result = nextInfo.some((i) => {
  //   const prevItem = prevInfo.find((k) => k.id === i.id);
  //   // 选项不等 直接更新
  //   if (!prevItem) {
  //     return true;
  //   }
  //   // 校验needCheckFields中字段值是否相等
  //   return needCheckFields.some((k) => !isEqual(prevItem[k], i[k]));
  // });
  // return result;
  const keysA = nextInfo.map((i) => i.id);
  const keysB = prevInfo.map((i) => i.id);
  // 删除/添加/排序变化
  if (!isEqual(keysA, keysB)) {
    needToAjaxGet = true;
  }
  return needToAjaxGet;
};

export const getNormalBusinessData = (dataset: DataSet, showFields: any, viewCompInfo: any, dataFilter?: any) => {
  let extFilter = [] as any;
  // 处理成weapp-components/dataParser/getFilterParams 识别的格式
  if (!isEmpty(dataFilter.datas)) {
    const fieldNameMap: { [x: string]: string } = {
      endTime: 'duedates',
    };
    extFilter = dataFilter.datas.map((i: any) => {
      return {
        ...i,
        fieldName: fieldNameMap[i.fieldName] || i.fieldName,
        conditions: isArray(i.conditions) ? i.conditions : [i.conditions],
      };
    });
  }
  return new Promise((resolve, reject) => {
    const formData = {
      pageId: viewCompInfo.pageId,
      compId: viewCompInfo.compId,
      config: JSON.stringify({
        dataset: dataset,
        field: showFields.field,
        searchModel: 'condition',
        filter: extFilter,
      }),
      pageNo: '1',
      pageSize: '20',
      isPreview: false,
    } as any;
    corsImport('@weapp/components').then(({ dsUtils }) => {
      dsUtils.queryData('', formData, viewCompInfo.comServicePath, false).then((data: any) => {
        resolve(data.list);
      });
    });
  });
};

export const treeToArr = (datas: TreeDataItem[], func: (data: TreeDataItem) => any): any[] => {
  const result: any[] = [];
  datas.forEach((item: TreeDataItem) => {
    const loop = (data: TreeDataItem) => {
      result.push(func(data));
      const child = data.children;
      if (child) {
        for (let i = 0; i < child.length; i++) {
          loop(child[i]);
        }
      }
    };
    loop(item);
  });
  return result;
};
export const getMindMapDataNodeStyle = (d: TreeDataItem, config: any) => {
  const nodeId = getCurrentNodeId(d);
  const { nodeTreeInfo = {} } = config;
  let nodeStyle: any = {};
  if (d.id === MindMapKey.RootSaved) {
    nodeStyle = nodeTreeInfo?.[MindMapKey.Root]?.nodeStyle;
  } else {
    nodeStyle = nodeTreeInfo?.[nodeId]?.nodeStyle;
  }
  return nodeStyle;
};
//样式属性取config通用样式设置
const getCustomLineColor = (d: TreeDataItem, config: any, key: string = 'lineColor') => {
  const style = getMindMapDataNodeStyle(d, config);
  // 兼容低版本
  const commonStyle = config?.commonNodeStyleSet || config?.commonConfig?.nodeStyle || {};
  let custom_color = commonStyle[key];
  if (style && style.styleType === 'custom' && style[key] && style.lineColor !== 'transparent') {
    custom_color = style[key];
  }
  return custom_color;
};
/**
 * 格式化数组
 **/
type formatMindMapDataParams = {
  datas: TreeDataItem[];
  config: any;
  direction?: Direction;
  isDesign?: boolean;
  showFieldKeys: string[];
};
export const formatNode = (payload: formatMindMapDataParams, d: any, defaultExpendDepth: number) => {
  const { config, direction = Direction.Right, isDesign, showFieldKeys } = payload;
  let expanded = false;
  if (!defaultExpendDepth || d.floor <= defaultExpendDepth) {
    expanded = true;
  }
  const newNode: MindMapDataItem = {
    ...d,
    id: d.id,
    isroot: d.id === MindMapKey.RootSaved,
    parentid: d.parentid,
    topic: d?.content,
    floor: d.floor,
    expanded,
    direction,
    commonNodeStyle: {
      // 连线色
      lineColor: getCustomLineColor(d, config) || MindMapDefaultLineColor,
      // 展开收起色
      expandColor: getCustomLineColor(d, config, 'expandColor'),
    },
    nodeStyle: getMindMapDataNodeStyle(d, config) || {},
  };
  const info = getCurrentNode(config, newNode);
  const sizeInfo = getSizeInfo(config, newNode as JmNode);
  let showFields = getShowFields(showFieldKeys, info, config, isDesign);
  const { cardLayout = {} } = showFields || {};
  const isTitle = newNode.floor === 2;
  const { width, originWidth } = getAdaptiveWidth(newNode, sizeInfo, cardLayout, {}, isTitle);
  console.log('****width****', width);
  return { ...newNode, width };
};
const json = [
  {
    floor: 1,
    id: 'xxx',
    children: [
      {
        floor: 2,
        id: 'xx2x',
      },
       {
        floor: 2,
        id: 'xx2x',
      },
    ],
  },
];
/**
 * 递归处理树结构数据，对每个节点及其子节点都应用formatNode处理
 * 使用缓存和批处理优化性能
 */
export const formatMindMapData = (payload: formatMindMapDataParams): MindMapDataItem[] => {
  const { datas } = payload;
  console.log('****datas****', datas);
  const defaultExpendDepth = datas[0]?.defaultExpendDepth || 0;

  // 创建节点缓存，避免重复计算
  const nodeCache = new Map<string, MindMapDataItem>();

  /**
   * 递归处理单个节点及其所有子节点
   * @param node 当前节点
   * @param depth 当前深度，用于性能优化
   * @returns 处理后的节点数组（扁平化）
   */
  const processNodeRecursively = (node: TreeDataItem, depth: number = 0): MindMapDataItem[] => {
    const result: MindMapDataItem[] = [];

    // 检查缓存
    if (nodeCache.has(node.id)) {
      const cachedNode = nodeCache.get(node.id)!;
      result.push(cachedNode);
    } else {
      // 处理当前节点
      const formattedNode = formatNode(payload, node, defaultExpendDepth);
      nodeCache.set(node.id, formattedNode);
      result.push(formattedNode);
    }

    // 递归处理子节点
    if (node.children && node.children.length > 0) {
      // 批处理子节点，避免深度过大时的性能问题
      const batchSize = 50; // 每批处理50个节点
      for (let i = 0; i < node.children.length; i += batchSize) {
        const batch = node.children.slice(i, i + batchSize);

        batch.forEach((child) => {
          const childResults = processNodeRecursively(child, depth + 1);
          result.push(...childResults);
        });

        // 如果是大批量数据，给浏览器一个喘息的机会
        if (node.children.length > 100 && i % 100 === 0) {
          // 在实际应用中，这里可以使用 requestIdleCallback 或 setTimeout(0)
          // 但在同步函数中，我们只能通过减少单次处理量来优化
        }
      }
    }

    return result;
  };

  console.log('****datas****', datas);

  // 处理所有根节点
  const allResults: MindMapDataItem[] = [];
  datas.forEach((rootNode) => {
    const nodeResults = processNodeRecursively(rootNode);
    allResults.push(...nodeResults);
  });

  return allResults;
};

/**
 * 异步版本的formatMindMapData，适用于大量数据处理
 * 使用Web Worker或分片处理来避免阻塞主线程
 */
export const formatMindMapDataAsync = async (payload: formatMindMapDataParams): Promise<MindMapDataItem[]> => {
  const { datas } = payload;
  const defaultExpendDepth = datas[0]?.defaultExpendDepth || 0;

  // 创建节点缓存
  const nodeCache = new Map<string, MindMapDataItem>();

  /**
   * 分片处理函数，避免长时间阻塞主线程
   */
  const processInChunks = async (nodes: TreeDataItem[], chunkSize: number = 20): Promise<MindMapDataItem[]> => {
    const results: MindMapDataItem[] = [];

    for (let i = 0; i < nodes.length; i += chunkSize) {
      const chunk = nodes.slice(i, i + chunkSize);

      // 处理当前批次
      const chunkResults = chunk.map((node) => {
        if (nodeCache.has(node.id)) {
          return nodeCache.get(node.id)!;
        }

        const formattedNode = formatNode(payload, node, defaultExpendDepth);
        nodeCache.set(node.id, formattedNode);
        return formattedNode;
      });

      results.push(...chunkResults);

      // 让出控制权给浏览器，避免阻塞UI
      if (i + chunkSize < nodes.length) {
        await new Promise((resolve) => setTimeout(resolve, 0));
      }
    }

    return results;
  };

  /**
   * 递归处理节点树
   */
  const processTreeAsync = async (node: TreeDataItem): Promise<MindMapDataItem[]> => {
    const result: MindMapDataItem[] = [];

    // 处理当前节点
    if (nodeCache.has(node.id)) {
      result.push(nodeCache.get(node.id)!);
    } else {
      const formattedNode = formatNode(payload, node, defaultExpendDepth);
      nodeCache.set(node.id, formattedNode);
      result.push(formattedNode);
    }

    // 递归处理子节点
    if (node.children && node.children.length > 0) {
      const childResults = await processInChunks(node.children);
      result.push(...childResults);

      // 递归处理每个子节点的子节点
      for (const child of node.children) {
        if (child.children && child.children.length > 0) {
          const grandChildResults = await processTreeAsync(child);
          // 过滤掉已经处理过的节点（避免重复）
          const newResults = grandChildResults.filter((item) => !result.some((existing) => existing.id === item.id));
          result.push(...newResults);
        }
      }
    }

    return result;
  };

  console.log('****datas (async)****', datas);

  // 处理所有根节点
  const allResults: MindMapDataItem[] = [];
  for (const rootNode of datas) {
    const nodeResults = await processTreeAsync(rootNode);
    allResults.push(...nodeResults);
  }

  return allResults;
};

/**
 * 简化版递归处理函数，专门用于处理树结构数据
 * 对每个节点及其所有子节点递归应用formatNode处理
 * 性能优化：使用Map缓存、避免重复计算
 */
export const formatMindMapDataRecursive = (payload: formatMindMapDataParams): MindMapDataItem[] => {
  const { datas } = payload;
  const defaultExpendDepth = datas[0]?.defaultExpendDepth || 0;

  // 结果数组和缓存
  const results: MindMapDataItem[] = [];
  const processedIds = new Set<string>(); // 避免重复处理

  /**
   * 递归处理节点及其子节点
   * @param node 当前节点
   */
  const processNode = (node: TreeDataItem): void => {
    // 避免重复处理同一个节点
    if (processedIds.has(node.id)) {
      return;
    }

    // 处理当前节点
    const formattedNode = formatNode(payload, node, defaultExpendDepth);
    results.push(formattedNode);
    processedIds.add(node.id);

    // 递归处理所有子节点
    if (node.children && node.children.length > 0) {
      node.children.forEach((child) => {
        processNode(child);
      });
    }
  };

  // 处理所有根节点
  datas.forEach((rootNode) => {
    processNode(rootNode);
  });

  console.log(`****处理完成，共处理 ${results.length} 个节点****`);
  return results;
};

/**
 * 高性能递归处理函数，带有深度限制和内存优化
 * 适用于大型树结构数据
 */
export const formatMindMapDataOptimized = (
  payload: formatMindMapDataParams,
  options: {
    maxDepth?: number; // 最大递归深度
    batchSize?: number; // 批处理大小
    enableCache?: boolean; // 是否启用缓存
  } = {}
): MindMapDataItem[] => {
  const { datas } = payload;
  const { maxDepth = 50, batchSize = 100, enableCache = true } = options;
  const defaultExpendDepth = datas[0]?.defaultExpendDepth || 0;

  const results: MindMapDataItem[] = [];
  const cache = enableCache ? new Map<string, MindMapDataItem>() : null;
  const processedIds = new Set<string>();

  /**
   * 递归处理节点，带深度控制
   */
  const processNodeWithDepth = (node: TreeDataItem, currentDepth: number = 0): void => {
    // 深度限制检查
    if (currentDepth > maxDepth) {
      console.warn(`达到最大递归深度 ${maxDepth}，跳过节点 ${node.id}`);
      return;
    }

    // 避免重复处理
    if (processedIds.has(node.id)) {
      return;
    }

    // 检查缓存
    let formattedNode: MindMapDataItem;
    if (cache && cache.has(node.id)) {
      formattedNode = cache.get(node.id)!;
    } else {
      formattedNode = formatNode(payload, node, defaultExpendDepth);
      if (cache) {
        cache.set(node.id, formattedNode);
      }
    }

    results.push(formattedNode);
    processedIds.add(node.id);

    // 批处理子节点
    if (node.children && node.children.length > 0) {
      // 如果子节点数量很大，分批处理
      if (node.children.length > batchSize) {
        for (let i = 0; i < node.children.length; i += batchSize) {
          const batch = node.children.slice(i, i + batchSize);
          batch.forEach((child) => {
            processNodeWithDepth(child, currentDepth + 1);
          });

          // 给浏览器一个喘息的机会（在实际应用中可以使用 requestIdleCallback）
          if (i + batchSize < node.children.length) {
            // 这里可以添加异步处理逻辑
          }
        }
      } else {
        // 直接处理所有子节点
        node.children.forEach((child) => {
          processNodeWithDepth(child, currentDepth + 1);
        });
      }
    }
  };

  // 处理所有根节点
  datas.forEach((rootNode) => {
    processNodeWithDepth(rootNode, 0);
  });

  console.log(`****优化处理完成，共处理 ${results.length} 个节点，缓存命中 ${cache ? cache.size : 0} 次****`);
  return results;
};
export const formatMindMapDataBak = (payload: formatMindMapDataParams): MindMapDataItem[] => {
  const { datas, config, direction = Direction.Right, isDesign, showFieldKeys } = payload;
  const defaultExpendDepth = datas[0]?.defaultExpendDepth || '';
  const res = treeToArr(datas, (d: TreeDataItem) => {
    let expanded = false;
    if (!defaultExpendDepth || d.floor <= defaultExpendDepth) {
      expanded = true;
    }
    const newNode: MindMapDataItem = {
      ...d,
      id: d.id,
      isroot: d.id === MindMapKey.RootSaved,
      parentid: d.parentid,
      topic: d?.content,
      floor: d.floor,
      expanded,
      direction,
      commonNodeStyle: {
        // 连线色
        lineColor: getCustomLineColor(d, config) || MindMapDefaultLineColor,
        // 展开收起色
        expandColor: getCustomLineColor(d, config, 'expandColor'),
      },
      nodeStyle: getMindMapDataNodeStyle(d, config) || {},
    };
    return newNode;
  });
  return res;
};

/**
 * 由于改了新的结构且要兼容历史数据所以需要判断着返回配置 返回新的格式 只是兼容以前设置过的项
 * @param config 配置项
 * @returns MindFuncProps
 */
export const getFuncSetting = (config: any) => {
  const { funSetting, funcSetting } = config;
  // 默认值
  let setting = COMP_DEFAULT_CONFIG().funcSetting;
  // 改版之前有设置过值 用老的先 后续设置过会出现在funcSetting内
  if (!isEmpty(funSetting)) {
    setting.toolbar = funSetting;
  }
  if (!isEmpty(funcSetting)) {
    setting = funcSetting;
  }
  return setting;
};

const getIsMulti = (coms: any, compId: string, pluginCenter?: MindPluginCenter) => {
  // 二开口子支持定制是否多选
  if (hasRegisterFuncInPlugin(pluginCenter, MindDefaultPluginName, EbdMindMapEventKeys.getRefreshCompIsMulti)) {
    const [isMulti] = invoke(pluginCenter, MindDefaultPluginName, EbdMindMapEventKeys.getRefreshCompIsMulti, {
      args: [coms, compId],
    });
    return isMulti;
  }
  // 目前仅项目树任务树支持开启多选配置（rowMultiple）
  const multiCompsType = ['ProjectTree', 'TaskTree'];
  const currentComp = coms.find((i: any) => i.id === compId && multiCompsType.includes(i.type));
  if (!currentComp) return false;
  return currentComp?.config?.rowMultiple;
};
/**
 * 对接刷新组件，支持单选及多选
 * @param val 选中值
 * @param coms 组件列表
 * @returns params
 */
export const getUrlParamsWidthRefresh = (val: any[], coms: any = [], pluginCenter?: MindPluginCenter) => {
  let urlParams: any = {};
  val.forEach((i: any, index: number) => {
    const { dynamicFieldsValue = [] } = i || {};
    const isMulti = getIsMulti(coms, i.comId, pluginCenter);
    const urlParamsCache = JSON.parse(sessionStorage.getItem(mindMapUrlParamsCache) || '{}');
    if (isMulti) {
      urlParams = urlParamsCache[i.comId] || {};
    }
    (i?.paramsSet || []).forEach((element: any) => {
      const { value = {} } = element || {};
      const item = dynamicFieldsValue.find((k: any) => k.id === value.id);
      if (item) {
        const itemValue = Array.isArray(item.value) ? item.value.map((i: any) => i.id).join(',') : item.value;
        // 开启了多选 就push
        if (isMulti) {
          if (isEmpty(urlParams[value.name])) urlParams[value.name] = [];
          if (urlParams[value.name].includes(itemValue)) {
            // 删除
            urlParams[value.name].splice(urlParams[value.name].indexOf(itemValue), 1);
          } else {
            // 添加
            urlParams[value.name].push(itemValue);
          }
        } else {
          // 单选
          urlParams[element.name || value.name] = itemValue;
        }
      }
    });
    if (index === val.length - 1 && isMulti) {
      // 二开口子支持定制刷新参数
      const [_urlParams] = invoke(pluginCenter, MindDefaultPluginName, EbdMindMapEventKeys.getRefreshCompParams, {
        args: [urlParams],
      });
      const newCache = { ...urlParamsCache, [i.comId]: _urlParams };
      if (isMulti) sessionStorage.setItem(mindMapUrlParamsCache, JSON.stringify(newCache));
    }
  });
  // 字符串化
  const result: any = {};
  for (const key in urlParams) {
    if (isEmpty(urlParams[key])) continue;
    result[key] = Array.isArray(urlParams[key]) ? urlParams[key].join(',') : urlParams[key];
  }
  return result;
};

// 获取当前节点Id
export const getCurrentNodeId = (node: any) => {
  return node.isroot ? MindMapKey.Root : node.id.split('_')[1] ? node.id.split('_')[0] : node.dataNode;
};
// 获取当前节点
export const getCurrentNode = (config: any, node: any) => {
  const { nodeTreeInfo } = config;
  const nodeId = getCurrentNodeId(node);
  return nodeTreeInfo[nodeId] || {};
};

// 有配置用配置的 没有走通用的
export const getSizeInfo = (config: any, node: JmNode) => {
  const { commonConfig, commonNodeSizeSet } = config;
  const info = getCurrentNode(config, node);
  // 是否为自定义
  const isCustom = info && info.nodeSize?.sizeType === 'custom';
  // 兼容低版本
  const commonSizeInfo = commonNodeSizeSet || commonConfig?.nodeSize || INIT_NODE_SIZE(node.isroot);
  return isCustom ? info.nodeSize : commonSizeInfo;
};

// 优先级 默认的 < 主题配置（未来）< 通用 < 自定义的
export const getNodeStyleInfo = (config: any, node: JmNode) => {
  const { commonConfig, commonNodeStyleSet } = config;
  const info = getCurrentNode(config, node);
  const isCustom = info && info.nodeStyle?.styleType === 'custom';
  // 兼容低版本
  const commonStyleInfo = commonNodeStyleSet || commonConfig?.nodeStyle || INIT_NODE_STYLE();
  return isCustom ? info.nodeStyle : commonStyleInfo;
};

export const getShowFields = (showFieldKeys: string[], info: any, config: any, isDesign?: boolean) => {
  const fieldFilterSet: FieldFilterSetProps = config?.fieldFilterSet! || {};
  const controlList = fieldFilterSet.controlList;
  let showFields = info?.showFields && typeof info?.showFields === 'string' ? JSON.parse(info?.showFields) : info?.showFields;
  // 设计器内不生效
  if (isDesign || isEmpty(controlList) || isEmpty(showFields)) return showFields;
  const { cardLayout, tableField = [], field = [] } = showFields;
  // !在controlList内 visible为开启 且选中了相关key 才生效
  const filterFunc = (id: string) => {
    if (!fieldFilterSet?.enable) return true;
    return controlList.filter((k) => k.visible).find((s: any) => s.id === id) ? showFieldKeys.includes(id) : true;
  };
  // 这里是过滤
  const validTableField = tableField.filter((i: any) => filterFunc(i.uid));
  const validCardGrid = cardLayout.grid
    .map((grids: any) => {
      const gridsData = grids.map((row: any) => row.filter((i: any) => filterFunc(i.field.uid)));
      return gridsData.filter((i: any) => i.length);
    })
    .filter((i: any) => i.length);
  const _cardLayout = { ...cardLayout, grid: validCardGrid };
  const validField = field.filter((i: any) => filterFunc(i.uid));
  return { ...showFields, cardLayout: _cardLayout, tableField: validTableField, field: validField };
};

export const hasGridContent = (grid: any[][]): boolean => {
  return grid.some((i) => i.some((k) => !isEmpty(k)));
};

export const runComEventsAction = (props: MindMapContentProps, eventGroup: any[][], data: any, e: SyntheticEvent, targetNode: any) => {
  const { match, history, compId, page, events } = props;
  if (eventGroup) {
    const clickEvent = eventGroup[0][0] || [];
    const linkOpts: any = {
      history,
      match,
      clientType: 'PC',
      // 区分根节点
      fields: data.floor > 1 && data.content ? JSON.parse(data.content) : null,
    };
    corsImport('@weapp/ebdcoms').then(({ executeActions }) => {
      executeActions(e?.target as any, clickEvent.events, linkOpts, {
        isChild: false,
        data,
        page,
        comId: compId,
        datasetMap: [{ ...targetNode?.dataset }],
        dom: e?.target as any,
        events,
      });
    });
  }
};

export const customRenderCell = (field: any, formatValue: any) => {
  const value = isEntryEnable(formatValue) ? wrapEntry(formatValue, false) : formatValue;
  return value;
};

export const getTargetNode = (node: JmNode, jmInstance: any) => {
  let target = null;
  if (!jmInstance) return target;
  const nodeInstance = jmInstance.renderer.findNodeByUid(node?.uid);
  if (!nodeInstance) return target;
  target = nodeInstance.group.node.querySelector(`.${mindMapViewClsPrefix}-card`);
  return target;
};

export const getPrimaryColorWithOpacity = (opacity: number) => {
  const primaryColor = getComputedStyle(document.documentElement).getPropertyValue('--primary');
  if (primaryColor.indexOf('rgba') > -1) {
    // 解析RGBA值
    const rgba: RegExpMatchArray = primaryColor.match(/\d+/g)!;
    const r = parseInt(rgba[0], 10);
    const g = parseInt(rgba[1], 10);
    const b = parseInt(rgba[2], 10);
    return `rgba(${r}, ${g}, ${b}, ${opacity})`;
  } else if (primaryColor.indexOf('#') > -1) {
    // 解析十六进制颜色值
    const r = parseInt(primaryColor.slice(1, 3), 16);
    const g = parseInt(primaryColor.slice(3, 5), 16);
    const b = parseInt(primaryColor.slice(5, 7), 16);
    return `rgba(${r}, ${g}, ${b}, ${opacity})`;
  }
  return primaryColor;
};

export const isEmptyShowFields = (showFields: any) => {
  if (isEmpty(showFields)) return true;
  const { cardLayout, tableField = [], field = [] } = showFields;
  return (isEmpty(cardLayout) && isEmpty(tableField) && isEmpty(field)) || !hasGridContent(cardLayout?.grid || []);
};

/**
 * 从文本中提取 font-size 值
 * @param text
 * @param defaultSize
 * @returns font-size
 */
export const extractFontSize = (text: string, defaultSize: number = 12): string => {
  if (!text) return `${defaultSize}px`;

  // 匹配 font-size: XXpx 或 font-size:XXpx 格式（空格可选）
  const fontSizeRegex = /font-size\s*:\s*(\d+(?:\.\d+)?(?:px|em|rem|%|pt|vw|vh))/i;
  const match = text.match(fontSizeRegex);

  if (match && match[1]) {
    return match[1];
  }

  return `${defaultSize}px`;
};
