import { middleware } from '@weapp/utils';
import { observer, Provider } from 'mobx-react';
import React, { PureComponent, Suspense } from 'react';
import { withRouter } from 'react-router-dom';

import { appName } from '../../../../constants';
import utils from '../../../../utils';

import MindMapContent from './Content';
import { MindMapViewStore } from './store';
import './style/index.less';
import { MindMapViewProps } from './types';
import withEbParams from '../../../common/withEbParams';

@withEbParams
@middleware(appName, 'MindMapView')
@observer
class MindMapView extends PureComponent<MindMapViewProps & React.Attributes> {
  render() {
    const { compId, pageId, config, comServicePath, isDesign, isMobile, page, onRef, isDoc, ebStore, events, pluginCenter, ebParams = {}, coms } = this.props;
    const urlParams = { ...utils.getUrlParams(), ...ebParams };
    return (
      <Provider weId={`${this.props.weId || ''}_hsoqsp`} store={new MindMapViewStore()}>
        <Suspense weId={`${this.props.weId || ''}_338ykf`} fallback={<div />}>
          <MindMapContent
            weId={`${this.props.weId || ''}_9gl4vk`}
            onRef={onRef}
            comServicePath={comServicePath}
            compId={compId}
            page={page}
            pageId={pageId}
            config={config}
            isDesign={isDesign}
            isMobile={isMobile}
            urlParams={urlParams}
            isDoc={isDoc}
            ebStore={ebStore}
            events={events}
            pluginCenter={pluginCenter}
            coms={coms}
          />
        </Suspense>
      </Provider>
    );
  }
}

export default withRouter(MindMapView);
