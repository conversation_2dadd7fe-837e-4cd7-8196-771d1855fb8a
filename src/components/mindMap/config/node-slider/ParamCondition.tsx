import { getLabel } from '@weapp/utils';
import { observer } from 'mobx-react';
import React from 'react';
import EBFilter from '../../../common/plugins/Filter';
import { specialFieldsFilter } from '../../../common/utils';
import { DesignComProps } from '../../types';

@observer
export default class ParamCondition extends React.PureComponent<DesignComProps> {
  render() {
    const { onChange, value, config } = this.props;
    const {
      dataset = {
        id: '',
        type: 'FORM',
        groupId: '',
        text: '',
      },
    } = config;
    return (
      <EBFilter
        weId={`${this.props.weId || ''}_rmgdha`}
        value={value}
        dataset={dataset}
        onChange={onChange}
        filter={specialFieldsFilter}
        title={getLabel('129036', '思维导图固定查询条件')}
      />
    );
  }
}
