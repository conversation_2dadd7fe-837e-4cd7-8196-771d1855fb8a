@import (reference) '../../../../../style/index.less';

.@{prefix}-lineMarkSet {
  &-mainDialog {
    &-add {
      display: flex;
      justify-content: space-between;
      align-items: center;
      font-size: var(--font-size-12);
      color: var(--regular-fc);
      margin-bottom: 8px;
      .ui-icon{
        margin-right: 6px;
        cursor: pointer;
        color: var(--primary);
      }
    }
    &-table{
      &-block{
        display: flex;
        align-items: center;
      }
    }
    &-nodeKeyStyle{
      display: flex;
      flex-wrap: nowrap;
      align-items: center;
      .example{
        display: flex;
        justify-content: space-between;
        flex-wrap: nowrap;
        width: 60%;
        border: 1px solid #e5e5e5;
        border: var(--border-solid);
        height: var(--input-height);
        line-height: var(--input-height);
        padding: 0 12px;
        margin-right: 30px;
        .text{
          margin: 2px 0;
          width: 50px;
          text-align: center;
          border-radius: 3px;
          display: flex;
          align-items: center;
          justify-content: center;
        }
        .number{

        }
      }
      .iconSet{
        cursor: pointer;
      }

      &-dialog{
        .content{
          background-color: white;
          .set-main{
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: flex-start;
            padding-left: 12px;
            border-bottom: 1px solid #e5e5e5;
            font-size: var(--font-size-12);
            color: var(--regular-fc);  
            font-family: var(--regular-ff);        
            .name{
              width: 40%;
            }
          }
          .set-main:last-child{
            border-bottom: 0;
          }
        }
      }
    }
  }
}