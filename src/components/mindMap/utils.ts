import { getLabel } from "@weapp/utils";

/**
 * 阿拉伯数字转中文, 暂时不用转义多语言处理
 * */
export const changeNumToHan = (num: number) => {
    const arr1: string[] = [
      getLabel('', '零'),
      getLabel('', '一'),
      getLabel('', '二'),
      getLabel('', '三'),
      getLabel('', '四'),
      getLabel('', '五'),
      getLabel('', '六'),
      getLabel('', '七'),
      getLabel('', '八'),
      getLabel('', '九'),
    ];
    const arr2: string[] = [
      '',
      getLabel('', '十'),
      getLabel('', '百'),
      getLabel('', '千'),
      getLabel('', '万'),
      getLabel('', '十'),
      getLabel('', '百'),
      getLabel('', '千'),
      getLabel('', '亿'),
      get<PERSON>abel('', '十'),
      getLabel('', '百'),
      getLabel('', '千'),
      getLabel('', '万'),
      getLabel('', '十'),
      getLabel('', '百'),
      getLabel('', '千'),
      getLabel('', '亿'),
    ];
    // export const changeNumToHan = (num: number) => {
    //   const arr1: string[] = [
    //     getLabel('56168', '零'),
    //     getLabel('67439', '一'),
    //     getLabel('67440', '二'),
    //     getLabel('67441', '三'),
    //     getLabel('67442', '四'),
    //     getLabel('67443', '五'),
    //     getLabel('67444', '六'),
    //     getLabel('56175', '七'),
    //     getLabel('56176', '八'),
    //     getLabel('56177', '九'),
    //   ];
    //   const arr2: string[] = [
    //     '',
    //     getLabel('63623', '十'),
    //     getLabel('162879', '百'),
    //     getLabel('162880', '千'),
    //     getLabel('55445', '万'),
    //     getLabel('63623', '十'),
    //     getLabel('162879', '百'),
    //     getLabel('162880', '千'),
    //     getLabel('55446', '亿'),
    //     getLabel('63623', '十'),
    //     getLabel('162879', '百'),
    //     getLabel('162880', '千'),
    //     getLabel('55445', '万'),
    //     getLabel('63623', '十'),
    //     getLabel('162879', '百'),
    //     getLabel('162880', '千'),
    //     getLabel('55446', '亿'),
    //   ];
    if (!num || isNaN(num)) return '';
    const english: string[] = num.toString().split('');
    let result = '';
    for (let i = 0; i < english.length; i++) {
      const desIndex = english.length - 1 - i; // 倒序排列设值
      result = arr2[i] + result;
      const arr1Index: number = +english[desIndex];
      result = arr1[arr1Index] + result;
    }
    result = result.replace(/零([十千百])/g, '零').replace(/十零/g, '十'); // 将【零千、零百】换成【零】 【十零】换成【十】
    result = result.replace(/零+/g, '零'); // 合并中间多个零为一个零
    result = result.replace(/零亿/g, '亿').replace(/零万/g, '万'); // 将【零亿】换成【亿】【零万】换成【万】
    result = result.replace(/亿万/g, '亿'); // 将【亿万】换成【亿】
    result = result.replace(/零+$/, ''); // 移除末尾的零
    // 将【一十】换成【十】
    result = result.replace(/^一十/g, '十');
    return result;
  };
