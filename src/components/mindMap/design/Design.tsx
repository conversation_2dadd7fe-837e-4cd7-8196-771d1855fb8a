import { DesignOptions } from '@weapp/ebdcoms';
import { getLabel } from '@weapp/utils';
import React from 'react';
import { NodeNames } from '../engine/node-config/constants';
import { DesignProps, MindMapKey } from '../types';
import { MindMapViewCom } from '../comp';
import { INIT_NODE_SIZE, INIT_NODE_STYLE } from '../../../constants';
import { MIND_VIEW, MIND_LINE_STYLE } from '../../../constants/enum';
import { COMP_DEFAULT_CONFIG } from '../../common/constants';

export default class MindMapDesign extends React.Component<DesignProps> {
  // 可选，组件在设计区域内的一些配置选项
  static defaultOpts: DesignOptions = {
    // 可选，是否有遮罩，用于控制组件设计模式下，功能的禁用，默认为false
    mask: true,
    layoutSizes: {
      // pc端自由布局
      gl: {
        // 页面被分为12列，默认为4
        w: 12,
        h: 45,
      },
      mgl: {
        adaptive: false,
        h: 650,
      },
    },
  };

  // 必须设置defaultProps，且config必须有值，config中公共的熟悉可以不设置，初始化时会赋默认值
  // 公共属性有title, titleEnabled, footerEnabled
  static defaultProps: DesignProps = {
    config: {
      title: getLabel('129038', '思维导图标题'),
      titleEnabled: false,
      commonNodeSizeSet: INIT_NODE_SIZE(),
      commonNodeStyleSet: INIT_NODE_STYLE(),
      nodeTreeList: [
        {
          id: MindMapKey.Root,
          name: getLabel('56039', '根节点'),
          pid: '',
          floor: 1,
          canDelete: false,

        },
      ],
      nodeTreeInfo: {
        [MindMapKey.Root]: {
          [NodeNames.Name]: getLabel('56039', '根节点'),
          [NodeNames.IsHideRoot]: false,
          [NodeNames.DefaultExpendDepth]: '',
          [NodeNames.NameShowSwitch]: true,
          [NodeNames.ActionSet]: [],
          [NodeNames.NodeSize]: INIT_NODE_SIZE(true),
          // [NodeNames.NodeStyle]: INIT_NODE_STYLE(true),
        },
      },
      funcSetting: COMP_DEFAULT_CONFIG().funcSetting,
      lineStyle: MIND_LINE_STYLE.CURVE,
      mindLayout: MIND_VIEW.LOGICAL_STRUCTURE
    },
  };

  static renderFooter() {
    return <div />;
  }

  render() {
    const {
      id, page, config, comServicePath, pageId, onRef
    } = this.props;
    return (<MindMapViewCom
      weId={`${this.props.weId || ''}_nw3pg6`}
      onRef={onRef}
      id={id}
      compId={id as string}
      pageId={page?.id || pageId}
      config={config}
      isMobile={page.client === 'MOBILE'}
      page={page}
      comServicePath={comServicePath}
      isDesign
    />);
  }
}
