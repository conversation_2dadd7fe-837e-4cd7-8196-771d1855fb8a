import { CorsComponent, utils } from '@weapp/ui';
import { getLabel } from '@weapp/utils';
import { toJS } from 'mobx';
import { observer } from 'mobx-react';
import React from 'react';
import {DataSet} from "../../../../types/common";

const { isEmpty } = utils;

// 动作来源
export enum ActionFromType {
  FieldLink = 'fieldLink', // 表格链接
  Btn = 'button' // 按钮
}

export enum ActionType {
  System = 'system',
  Custom = 'custom',
  OpenPage = 'openpage',
  CustomInterface = 'customInterface',
  Esb = 'esb',
  Reply = 'reply'
}

export interface ActionSetPanelProps {
  appId: string;
  selectedFormId: string;
  formName: string;
  actions: any[];
  onSave: (actions: any[]) => void;
  dataset:DataSet;
  isEngine?: Boolean;
}

@observer
export default class ActionSet extends React.PureComponent<ActionSetPanelProps> {
  onActionSure = (actions: any) => {
    actions = actions.map((item: any) => ({
      ...item,
      enable: item.enable ? 1 : 0,
    }));
    const { onSave } = this.props;
    const empty = isEmpty(actions); // 无链接

    if (!empty) {
      onSave(toJS(actions));
    } else {
      onSave([]);
    }
  };

  render() {
    const {
      actions = [], appId, selectedFormId, formName,dataset
    } = this.props;
    return (
      <>
        <CorsComponent
          weId={`${this.props.weId || ''}_hx4kyh`}
          app="@weapp/ebdform"
          compName="ActionSetPanel"
          actionDatas={toJS(actions)}
          appId={appId}
          selectedFormId={selectedFormId}
          editFormData={
            {
              buttonKey: 'link',
            } as any
          }
          onSure={this.onActionSure}
          title={getLabel('53985', '动作配置')}
          showActionTypes={[
            // 暂时隐藏esb动作流
            ActionType.OpenPage,
            // ActionType.CustomInterface,
          ]}
          type={ActionFromType.FieldLink}
          formName={formName}
          useDataSetField={true}
          dataset={dataset}
        />
      </>
    );
  }
}
