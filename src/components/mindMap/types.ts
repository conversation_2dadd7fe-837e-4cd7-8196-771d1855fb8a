import { ClientType, CommonConfigData } from '@weapp/ebdcoms';

import { TreeData } from './engine/types';
import { AnyObj } from '@weapp/ui';
import { DataSetItem } from '../common/types';
import { NodeSizeDataType } from './config/node-slider/node-size/types';
import { NodeStyleDataType } from './config/node-slider/node-style/types';
import { MindFuncProps } from './config/comps/func-setting';
import { FieldFilterSetProps } from './config/comps/node-fieldFilter/types';
import { MIND_VIEW, MIND_LINE_STYLE } from '../../constants/enum';

export enum MindMapKey {
  Root = 'root_',
  RootSaved = '0_0'
}

// 通用配置相关
export interface MindMapCommonConfigProps {
  nodeSize: NodeSizeDataType
}
export interface MindMapConfigData extends CommonConfigData {
  nodeTreeList: TreeData[];
  nodeTreeInfo: AnyObj;
  commonNodeSizeSet: NodeSizeDataType
  commonNodeStyleSet: NodeStyleDataType
  dataset?: DataSetItem;
  isHideRoot?: Boolean;
  fromEbuilder?: Boolean; // 是否为表单高级视图入口
  funcSetting?: MindFuncProps
  mindLayout?: MIND_VIEW
  lineStyle?: MIND_LINE_STYLE
  fieldFilterSet?: FieldFilterSetProps
}
export interface MindPluginCenter {
  nodeRenderCustom?: any
}
export interface DesignProps extends React.Attributes {
  config: MindMapConfigData;
  page?: any;
  id?: string;
  pid?: string;
  readOnly?: boolean;
  pageId?: string;
  client?: ClientType;
  comServicePath?: string;
  onRef?: (ref: any) => void;
  store?: AnyObj
  events?: AnyObj
  pluginCenter?: MindPluginCenter // 引入插件机制
  coms?: any[]
}

export interface DesignComProps extends DesignProps {
  onChange: (changes: any, ...args: any[]) => void;
  onConfigChange?: (changes: any) => void;
  value: any;
  appid?: string,
  comId?: string,
  rootProps: any;
}

export interface ViewProps extends DesignProps {
  isDesign: boolean;
  isMobile: boolean;
  isDoc?: boolean;
}

export enum CustomAction {
  OpenPage = 'open-page',
}

export enum ActionType {
  System = 'system',
  Custom = 'custom',
}

type Action = 'open-page';

export type ActionRecord<RecordData> = {
  id: string;
  type: ActionType;
  name: string;
  act: Action;
  record: RecordData;
};

export interface NodeKeyDesignComProps extends DesignProps {
  onChange: (changes: any, ...args: any[]) => void;
  onConfigChange?: (changes: any) => void;
  value: any;
  appid?: string,
  comId?: string,
  form?:any
}
