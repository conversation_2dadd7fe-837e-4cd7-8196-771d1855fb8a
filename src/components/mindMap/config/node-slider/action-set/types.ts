/**
 * <AUTHOR>
 * @createTime 2023 -01 -20
 */
import {
  ClientType, DatasetValue, LayoutInfo, PageScope,
} from '@weapp/ebdcoms';
import { AnyObj } from '@weapp/ui';
import { CompDesignStoreType } from '@weapp/designer';

export interface EventsActionComProps {
  client: 'PC' | 'MOBILE';
  pageId: string;
  appid: string;
  value: any;
  onChange: Function;
  layoutInfo: LayoutInfo;
  orgType?: 'hrm' | 'dep';
  rootProps?:any;
  config: AnyObj;
  store: CompDesignStoreType;
  pageScope: PageScope;
  clientType: ClientType;
  compType: string;
  onDatasetChange?: (datasetVal?: DatasetValue, type?: string) => void;
}

export interface ActionSetComProps {
  client: 'PC' | 'MOBILE';
  pageId: string;
  appid: string;
  value: any[];
  onChange: Function;
  layoutInfo: any;
  config: AnyObj;
  rootProps?: any
}
export default {};
