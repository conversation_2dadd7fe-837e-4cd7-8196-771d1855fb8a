/**
 * 首页快速新建
 */
import { observer } from 'mobx-react';
import React, { PureComponent } from 'react';
import { ebdfClsPrefix } from '../../../constants';
import './index.less';

interface GroupCardProps {
  title: React.ReactNode | string;
  classname?: string;
}

@observer
class GroupCard extends PureComponent<GroupCardProps & React.Attributes> {
  render() {
    const { children, title, classname } = this.props;
    return (
      <div className={`${ebdfClsPrefix}-fpage-group-card-content ${classname}`}>
        <div className="group-card-title">{title}</div>
        {children}
      </div>
    );
  }
}

export default GroupCard;
