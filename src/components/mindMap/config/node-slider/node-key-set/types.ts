export interface PageConfig {
    customPage?: boolean;
    pageSize: string;
    customPageSize?: string;
    pageMode?: PageModeType;
  }
  
  export enum PageModeType {
    More = 'more',
    Scroll = 'scroll',
    None = 'none',
  }
  export interface styleSettingProps {
    color: string
    textColor: string
    countColor: string
  }
  export interface MarkItemProps {
    id: string
    name: string
    filter: MarkFilterProps
    styleSetting:styleSettingProps

  }
  export interface MarkConfig {
    legend?: MarkItemProps[];
  }
  export interface MarkFilterProps {
    [x: string]: any
  }
  