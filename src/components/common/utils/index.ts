import ebdcoms from "../../../utils/ebdcoms";
import { EtComponentKey as ETC, EtComponentKey } from "../constants/EtComponent";
import { ua, weappSDK } from '@weapp/utils';
import { SyntheticEvent } from 'react';
import { routeName, staticPath, root } from "../../../constants";
import { PageType } from '../../common/constants/index'

export const specialFieldsFilter = (field: any) => {
  /**
   * 筛选中不允许设置的类型
   * */
  if (field?.fieldId === '8' || field?.id === '8' || field?.fieldId === '9' || field.id === '9' || field?.fieldId === '10' || field.id === '10' || field?.fieldId === '11' || field.id === '11' || field?.fieldId === '12' || field.id === '12') {
    // 数据状态或流程状态或当前阶段
    return false;
  }
  const isSpecialType = [
    'MatrixComponent',
    EtComponentKey.TextArea,
    EtComponentKey.FileComponent,
    EtComponentKey.ImageComponent,
    EtComponentKey.EinvoiceComponent,
    EtComponentKey.ImageRadioBox,
    EtComponentKey.ComboSelect,
    EtComponentKey.TreeSelect,
    'DividingLine',
    EtComponentKey.ImageCheckBox,
    EtComponentKey.ImageRadioBox,
  ].includes(field?.compType || field?.componentKey);
  return !isSpecialType;
};

// 单页关闭浏览器页面
export const closeWindow = () => {
  const isWeapp = ua?.browser === 'WeappPC' || ua?.browser === 'wxwork';
  const closeFn = () => {
    try {
      window.opener && window.close();
    } catch (e) {
      // eslint-disable-next-line no-console
      console.error('closeFn Error', e);
    }
  };
  if (isWeapp) {
    weappSDK.checkApi('closeWindow').then(() => {
      weappSDK.exec('closeWindow', {
        success: () => {},
        fail: closeFn,
      });
    });
  } else {
    closeFn();
  }
};

/**
 * 处理ebdcoms链接中的 params参数，组装参数配置对象
 * @param pageParam
 * @returns
 */
export const getEbcomsParams = (pageParam: any[]) => {
  const params = {} as any;

  if (pageParam && pageParam.length > 0) {
    pageParam.forEach((p) => {
      const name = p.nameType === 'FORMFIELDS' ? p.name.id : p.name;
      if (p.type === 'fixed' || !p.type) {
        params[name] = p.value;
      } else if (p.type === 'field') {
        params[name] = p.value.id;
      }
    });
  }
  return params;
};
/**
 * 阻止冒泡统一方法
 * @param e
 * @returns
 */
export const stopPropagation = (e: SyntheticEvent) => e.stopPropagation();

export function getAssetsJsPath(path: string) {
  return getBuildPath(`/js/${path}`);
}

/**
 * @function getBuildPath
 * @param path [string] 资源路径
 * @param pkgName [routeName] 模块包名称
 * @desc 获取静态资源的地址
 * */
export function getBuildPath(path: string, pkgName = routeName) {
  if (process.env.NODE_ENV === 'production') {
    const buildPath = `/build/${pkgName}`;
    // 如果存在二级域名
    if (staticPath) {
      return `${staticPath}${buildPath}${path}`;
    }
    return (
      [root, buildPath, path]
        .map((p: string) => p.replace(/^\/|\/$/g, ''))
        .join('/')
        // 处理空值情况，如root为空时
        .replace(/\/\//g, '/')
    );
  }

  return path;
}

// 判断是否是详情页面
export const isDetailPage = (page?: any) => {
  if (!page) return false;

  const { SystemPageType } = ebdcoms.synchroGet();

  const { pageType, pageId } = page;
  const detailPages = [
    SystemPageType.CustomerDetail,
    SystemPageType.ContactDetail,
    SystemPageType.ContractDetail,
    SystemPageType.OrderDetail,
    SystemPageType.PriceDetail,
    SystemPageType.OpportunityDetail,
    SystemPageType.ClueDetail,
    SystemPageType.ProductDetail,
    SystemPageType.MarketActivityDetail,
    SystemPageType.CompetitorDetail,
    SystemPageType.DocumentDetail,
    SystemPageType.ApprovalDetail,
    SystemPageType.SubmitApplication,
    SystemPageType.DataDetail,
    SystemPageType.ProjectDetail,
    SystemPageType.TaskDetail,
  ] as string[];

  return pageType === PageType.SystemPage && detailPages.includes(pageId);
};
