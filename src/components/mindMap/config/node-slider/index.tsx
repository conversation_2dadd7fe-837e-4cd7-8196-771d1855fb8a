import { Dialog } from '@weapp/ui';
import { getLabel } from '@weapp/utils';
import { observer } from 'mobx-react';
import React, { Attributes, PureComponent } from 'react';
import { If, Then, Else } from 'react-if';
import { ebdfClsPrefix } from '../../../../constants';
import DetailConfig from './DetailConfig';
import RootConfig from './RootConfig';
import './index.less';
interface NodeSliderProps extends Attributes {
  visible: boolean;
  onClose: () => void;
  fatherProps?: any; // 主页面属性
  data: any; // 节点数据
  onChange?: (data: any) => void;
}
@observer
export default class NodeSlider extends PureComponent<NodeSliderProps, any> {
  constructor(props: NodeSliderProps) {
    super(props);
    this.state = {
      dlgWidth: 249,
    };
  }

  getContainer = () => document.querySelector('.weapp-ebde-config-panel') as any; // dialog的容器

  customRenderLeft = () => (
    <div className={`${ebdfClsPrefix}-mindmap-node-slider-top`}>
      <span>{getLabel('87164', '节点设置')}</span>
    </div>
  );

  onChildChange = (changedData: any) => {
    let newData = {
      ...this.props.data,
      ...changedData,
    };
    this.props.onChange?.(newData);
  };

  render(): React.ReactNode {
    const { visible, onClose, data, fatherProps } = this.props;
    const configData = {
      ...data,
      type: 'MindMap',
      layout: { scope: fatherProps.layoutInfo.comLayoutScope }, // 默认的
    };
    const page = {
      ...fatherProps,
      layout: fatherProps.layoutInfo.pageLayout,
      appid: fatherProps.appid,
      id: fatherProps.pageId,
      ...fatherProps?.layoutInfo,
    };
    return (
      <Dialog
        weId={`${this.props.weId || ''}_s6pagn`}
        visible={visible}
        onClose={onClose}
        getContainer={this.getContainer}
        placement='right'
        title={getLabel('87164', '节点设置')}
        closable
        mask={false}
        noMaskClose={false}
        destroyOnClose
        className={`${ebdfClsPrefix}-mindmap-node-slider`}
        width={this.state.dlgWidth}
        top={75}
        leftCol={20}
        rightCol={4}
        customRenderLeft={this.customRenderLeft}
      >
        <If weId={`${this.props.weId || ''}_5sqoht`} condition={!data?.pid}>
          <Then weId={`${this.props.weId || ''}_hvfrbg`}>
            <RootConfig weId={`${this.props.weId || ''}_a4ivux`} data={configData} page={page} onChange={this.onChildChange} />
          </Then>
          <Else weId={`${this.props.weId || ''}_trx5fo`}>
            <DetailConfig weId={`${this.props.weId || ''}_snbwof`} data={configData} page={page} onChange={this.onChildChange} />
          </Else>
        </If>
      </Dialog>
    );
  }
}
