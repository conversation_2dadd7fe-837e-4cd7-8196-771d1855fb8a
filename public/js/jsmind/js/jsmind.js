!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?module.exports=t(require("core-js/modules/es.function.name.js"),require("core-js/modules/es.array.slice.js"),require("core-js/modules/es.string.starts-with.js"),require("core-js/modules/es.object.keys.js"),require("core-js/modules/es.object.to-string.js"),require("core-js/modules/es.regexp.exec.js"),require("core-js/modules/es.regexp.to-string.js"),require("core-js/modules/es.string.replace.js"),require("core-js/modules/es.array.sort.js"),require("core-js/modules/es.array.splice.js"),require("core-js/modules/es.array.join.js"),require("core-js/modules/es.string.match.js"),require("core-js/modules/es.string.split.js"),require("core-js/modules/es.array.includes.js"),require("core-js/modules/es.array.map.js"),require("core-js/modules/es.string.includes.js"),require("core-js/modules/web.dom-collections.for-each.js"),require("core-js/actual/array/includes")):"function"==typeof define&&define.amd?define(["core-js/modules/es.function.name.js","core-js/modules/es.array.slice.js","core-js/modules/es.string.starts-with.js","core-js/modules/es.object.keys.js","core-js/modules/es.object.to-string.js","core-js/modules/es.regexp.exec.js","core-js/modules/es.regexp.to-string.js","core-js/modules/es.string.replace.js","core-js/modules/es.array.sort.js","core-js/modules/es.array.splice.js","core-js/modules/es.array.join.js","core-js/modules/es.string.match.js","core-js/modules/es.string.split.js","core-js/modules/es.array.includes.js","core-js/modules/es.array.map.js","core-js/modules/es.string.includes.js","core-js/modules/web.dom-collections.for-each.js","core-js/actual/array/includes"],t):(e="undefined"!=typeof globalThis?globalThis:e||self).jsMind=t()}(this,(function(){"use strict";function e(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var i=n.call(e,t||"default");if("object"!=typeof i)return i;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}function t(e){return t="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},t(e)}function n(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function i(t,n){for(var i=0;i<n.length;i++){var o=n[i];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(t,e(o.key),o)}}function o(e,t,n){return t&&i(e.prototype,t),n&&i(e,n),Object.defineProperty(e,"prototype",{writable:!1}),e}function r(t,n,i){return(n=e(n))in t?Object.defineProperty(t,n,{value:i,enumerable:!0,configurable:!0,writable:!0}):t[n]=i,t}function s(e,t){if(e!==t)throw new TypeError("Cannot instantiate an arrow function")}function a(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,i=new Array(t);n<t;n++)i[n]=e[n];return i}function d(e,t){var n="undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(!n){if(Array.isArray(e)||(n=function(e,t){if(e){if("string"==typeof e)return a(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?a(e,t):void 0}}(e))||t&&e&&"number"==typeof e.length){n&&(e=n);var i=0,o=function(){};return{s:o,n:function(){return i>=e.length?{done:!0}:{done:!1,value:e[i++]}},e:function(e){throw e},f:o}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var r,s=!0,d=!1;return{s:function(){n=n.call(e)},n:function(){var e=n.next();return s=e.done,e},e:function(e){d=!0,r=e},f:function(){try{s||null==n.return||n.return()}finally{if(d)throw r}}}}var l="0.8.3";"function"!=typeof String.prototype.startsWith&&(String.prototype.startsWith=function(e){return this.slice(0,e.length)===e});var h={left:-1,center:0,right:1,of:function(e){return e&&-1!==e&&0!==e&&1!==e?"-1"===e||"0"===e||"1"===e?parseInt(e):"left"===e.toLowerCase()?this.left:"right"===e.toLowerCase()?this.right:"center"===e.toLowerCase()?this.center:void 0:e}},u={show:1,resize:2,edit:3,select:4},_={debug:1,info:2,warn:3,error:4,disable:9},c=function(){},v="undefined"==typeof console?{level:c,log:c,debug:c,info:c,warn:c,error:c}:{level:function(e){v.debug=e>_.debug?c:console.debug;v.info=e>_.info?c:console.info;v.warn=e>_.warn?c:console.warn;v.error=e>_.error?c:console.error},log:console.log,debug:console.debug,info:console.info,warn:console.warn,error:console.error};var f=new(o((function e(i){n(this,e),this.w=i,this.d=i.document,this.g=function(e){return this.d.getElementById(e)},this.c=function(e){return this.d.createElement(e)},this.t=function(e,t){e.hasChildNodes()?e.firstChild.nodeValue=t:e.appendChild(this.d.createTextNode(t))},this.h=function(e,t){t instanceof HTMLElement?(e.innerHTML="",e.appendChild(t)):e.innerHTML=t},this.i=function(e){return!!e&&"object"===t(e)&&1===e.nodeType&&"object"===t(e.style)&&"object"===t(e.ownerDocument)},this.on=function(e,t,n){e.addEventListener?e.addEventListener(t,n,!1):e.attachEvent("on"+t,n)}})))(window),p={file:{read:function(e,t){var n=new FileReader;n.onload=function(){"function"==typeof t&&t(this.result,e.name)},n.readAsText(e)},save:function(e,t,n){var i;if("function"==typeof f.w.Blob)i=new Blob([e],{type:t});else{var o=new(f.w.BlobBuilder||f.w.MozBlobBuilder||f.w.WebKitBlobBuilder||f.w.MSBlobBuilder);o.append(e),i=o.getBlob(t)}if(navigator.msSaveBlob)navigator.msSaveBlob(i,n);else{var r=(f.w.URL||f.w.webkitURL).createObjectURL(i),s=f.c("a");if("download"in s){s.style.visibility="hidden",s.href=r,s.download=n,f.d.body.appendChild(s);var a=f.d.createEvent("MouseEvents");a.initEvent("click",!0,!0),s.dispatchEvent(a),f.d.body.removeChild(s)}else location.href=r}}},json:{json2string:function(e){return JSON.stringify(e)},string2json:function(e){return JSON.parse(e)},merge:function(e,n){for(var i in n)i in e?"object"!==t(e[i])||"[object object]"!=Object.prototype.toString.call(e[i]).toLowerCase()||e[i].length?e[i]=n[i]:p.json.merge(e[i],n[i]):e[i]=n[i];return e}},uuid:{newid:function(){return((new Date).getTime().toString(16)+Math.random().toString(16).substring(2)).substring(2,18)}},text:{is_empty:function(e){return!e||0==e.replace(/\s*/,"").length}}},y={container:"",editable:!1,theme:null,mode:"full",support_html:!0,log_level:"info",view:{engine:"canvas",hmargin:100,vmargin:50,line_width:2,line_color:"#555",line_style:"curved",draggable:!1,hide_scrollbars_when_draggable:!1,node_overflow:"hidden",zoom:{min:.5,max:2.1,step:.1},custom_node_render:null,expander_style:"char"},layout:{hspace:30,vspace:20,pspace:13,cousin_space:0},default_event_handle:{enable_mousedown_handle:!0,enable_click_handle:!0,enable_dblclick_handle:!0,enable_mousewheel_handle:!0},shortcut:{enable:!0,handles:{},mapping:{addchild:[45,4109],addbrother:13,editnode:113,delnode:46,toggle:32,left:37,up:38,right:39,down:40}},plugin:{}};var g=function(){function e(t,i,o,r,s,a,d,l){n(this,e),t?"number"==typeof i?(void 0===l&&(l=!0),this.id=t,this.index=i,this.topic=o,this.data=r||{},this.isroot=s,this.parent=a,this.direction=d,this.expanded=!!l,this.children=[],this._data={}):v.error("invalid node index"):v.error("invalid node id")}return o(e,[{key:"get_location",value:function(){var e=this._data.view;return{x:e.abs_x,y:e.abs_y}}},{key:"get_size",value:function(){var e=this._data.view;return{w:e.width,h:e.height}}}],[{key:"compare",value:function(e,t){var n=e.index,i=t.index;return n>=0&&i>=0?n-i:-1==n&&-1==i?0:-1==n?1:-1==i?-1:0}},{key:"inherited",value:function(e,t){if(e&&t){if(e.id===t.id)return!0;if(e.isroot)return!0;for(var n=e.id,i=t;!i.isroot;)if((i=i.parent).id===n)return!0}return!1}},{key:"is_node",value:function(t){return!!t&&t instanceof e}}])}(),m=function(){return o((function e(){n(this,e),this.name=null,this.author=null,this.version=null,this.root=null,this.selected=null,this.nodes={}}),[{key:"get_node",value:function(e){return e in this.nodes?this.nodes[e]:(v.warn("the node[id="+e+"] can not be found"),null)}},{key:"set_root",value:function(e,t,n){return null==this.root?(this.root=new g(e,0,t,n,!0),this._put_node(this.root),this.root):(v.error("root node is already exist"),null)}},{key:"add_node",value:function(e,t,n,i,o,r,s){if(!g.is_node(e))return v.error("the parent_node "+e+" is not a node."),null;var a=new g(t,s||-1,n,i,!1,e,e.direction,r);return e.isroot&&(a.direction=o||h.right),this._put_node(a)?(e.children.push(a),this._update_index(e)):(v.error("fail, the node id '"+a.id+"' has been already exist."),a=null),a}},{key:"insert_node_before",value:function(e,t,n,i,o){if(!g.is_node(e))return v.error("the node_before "+e+" is not a node."),null;var r=e.index-.5;return this.add_node(e.parent,t,n,i,o,!0,r)}},{key:"get_node_before",value:function(e){if(!g.is_node(e)){var t=this.get_node(e);return t?this.get_node_before(t):(v.error("the node[id="+e+"] can not be found."),null)}if(e.isroot)return null;var n=e.index-2;return n>=0?e.parent.children[n]:null}},{key:"insert_node_after",value:function(e,t,n,i,o){if(!g.is_node(e))return v.error("the node_after "+e+" is not a node."),null;var r=e.index+.5;return this.add_node(e.parent,t,n,i,o,!0,r)}},{key:"get_node_after",value:function(e){if(!g.is_node(e)){var t=this.get_node(e);return t?this.get_node_after(t):(v.error("the node[id="+e+"] can not be found."),null)}if(e.isroot)return null;var n=e.index;return e.parent.children.length>n?e.parent.children[n]:null}},{key:"move_node",value:function(e,t,n,i){return g.is_node(e)?(n||(n=e.parent.id),this._move_node(e,t,n,i)):(v.error("the parameter node "+e+" is not a node."),null)}},{key:"_flow_node_direction",value:function(e,t){void 0===t?t=e.direction:e.direction=t;for(var n=e.children.length;n--;)this._flow_node_direction(e.children[n],t)}},{key:"_move_node_internal",value:function(e,t){if(e&&t)if("_last_"==t)e.index=-1,this._update_index(e.parent);else if("_first_"==t)e.index=0,this._update_index(e.parent);else{var n=t?this.get_node(t):null;null!=n&&null!=n.parent&&n.parent.id==e.parent.id&&(e.index=n.index-.5,this._update_index(e.parent))}return e}},{key:"_move_node",value:function(e,t,n,i){if(e&&n){var o=this.get_node(n);if(g.inherited(e,o))return v.error("can not move a node to its children"),null;if(e.parent.id!=n){for(var r=e.parent.children,s=r.length;s--;)if(r[s].id==e.id){r.splice(s,1);break}var a=e.parent;e.parent=o,o.children.push(e),this._update_index(a)}e.parent.isroot?i==h.left?e.direction=i:e.direction=h.right:e.direction=e.parent.direction,this._move_node_internal(e,t),this._flow_node_direction(e)}return e}},{key:"remove_node",value:function(e){if(!g.is_node(e))return v.error("the parameter node "+e+" is not a node."),!1;if(e.isroot)return v.error("fail, can not remove root node"),!1;null!=this.selected&&this.selected.id==e.id&&(this.selected=null);for(var t=e.children,n=t.length;n--;)this.remove_node(t[n]);t.length=0;for(var i=e.parent,o=i.children,r=o.length;r--;)if(o[r].id==e.id){o.splice(r,1);break}for(var s in delete this.nodes[e.id],e)delete e[s];return e=null,this._update_index(i),!0}},{key:"_put_node",value:function(e){return e.id in this.nodes?(v.warn("the node_id '"+e.id+"' has been already exist."),!1):(this.nodes[e.id]=e,!0)}},{key:"_update_index",value:function(e){if(e instanceof g){e.children.sort(g.compare);for(var t=0;t<e.children.length;t++)e.children[t].index=t+1}}}])}(),w={name:"jsMind",author:"<EMAIL>",version:l},b={node_tree:{example:{meta:w,format:"node_tree",data:{id:"root",topic:"jsMind node_tree example"}},get_mind:function(e){var t=b.node_tree,n=new m;return n.name=e.meta.name,n.author=e.meta.author,n.version=e.meta.version,t._parse(n,e.data),n},get_data:function(e){var t=b.node_tree,n={};return n.meta={name:e.name,author:e.author,version:e.version},n.format="node_tree",n.data=t._build_node(e.root),n},_parse:function(e,t){var n=b.node_tree,i=n._extract_data(t);if(e.set_root(t.id,t.topic,i),"children"in t)for(var o=t.children,r=0;r<o.length;r++)n._extract_subnode(e,e.root,o[r])},_extract_data:function(e){var t={};for(var n in e)"id"!=n&&"topic"!=n&&"children"!=n&&"direction"!=n&&"expanded"!=n&&(t[n]=e[n]);return t},_extract_subnode:function(e,t,n){var i=b.node_tree,o=i._extract_data(n),r=null;t.isroot&&(r="left"==n.direction?h.left:h.right);var s=e.add_node(t,n.id,n.topic,o,r,n.expanded);if(n.children)for(var a=n.children,d=0;d<a.length;d++)i._extract_subnode(e,s,a[d])},_build_node:function(e){var t=b.node_tree;if(e instanceof g){var n={id:e.id,topic:e.topic,expanded:e.expanded};if(e.parent&&e.parent.isroot&&(n.direction=e.direction==h.left?"left":"right"),null!=e.data){var i=e.data;for(var o in i)n[o]=i[o]}var r=e.children;if(r.length>0){n.children=[];for(var s=0;s<r.length;s++)n.children.push(t._build_node(r[s]))}return n}}},node_array:{example:{meta:w,format:"node_array",data:[{id:"root",topic:"jsMind node_array example",isroot:!0}]},get_mind:function(e){var t=b.node_array,n=new m;return n.name=e.meta.name,n.author=e.meta.author,n.version=e.meta.version,t._parse(n,e.data),n},get_data:function(e){var t=b.node_array,n={};return n.meta={name:e.name,author:e.author,version:e.version},n.format="node_array",n.data=[],t._array(e,n.data),n},_parse:function(e,t){var n=b.node_array,i=t.slice(0);i.reverse();var o=n._extract_root(e,i);o?n._extract_subnode(e,o,i):v.error("root node can not be found")},_extract_root:function(e,t){for(var n=b.node_array,i=t.length;i--;)if("isroot"in t[i]&&t[i].isroot){var o=t[i],r=n._extract_data(o),s=e.set_root(o.id,o.topic,r);return t.splice(i,1),s}return null},_extract_subnode:function(e,t,n){for(var i=b.node_array,o=n.length,r=null,s=null,a=0;o--;)if((r=n[o]).parentid==t.id){s=i._extract_data(r);var d=null,l=r.direction;l&&(d="left"==l?h.left:h.right);var u=e.add_node(t,r.id,r.topic,s,d,r.expanded);n.splice(o,1),a++;var _=i._extract_subnode(e,u,n);_>0&&(o=n.length,a+=_)}return a},_extract_data:function(e){var t={};for(var n in e)"id"!=n&&"topic"!=n&&"parentid"!=n&&"isroot"!=n&&"direction"!=n&&"expanded"!=n&&(t[n]=e[n]);return t},_array:function(e,t){b.node_array._array_node(e.root,t)},_array_node:function(e,t){var n=b.node_array;if(e instanceof g){var i={id:e.id,topic:e.topic,expanded:e.expanded};if(e.parent&&(i.parentid=e.parent.id),e.isroot&&(i.isroot=!0),e.parent&&e.parent.isroot&&(i.direction=e.direction==h.left?"left":"right"),null!=e.data){var o=e.data;for(var r in o)i[r]=o[r]}t.push(i);for(var s=e.children.length,a=0;a<s;a++)n._array_node(e.children[a],t)}}},freemind:{example:{meta:w,format:"freemind",data:'<map version="1.0.1"><node ID="root" TEXT="jsMind freemind example"/></map>'},get_mind:function(e){var t=b.freemind,n=new m;n.name=e.meta.name,n.author=e.meta.author,n.version=e.meta.version;var i=e.data,o=t._parse_xml(i),r=t._find_root(o);return t._load_node(n,null,r),n},get_data:function(e){var t=b.freemind,n={};n.meta={name:e.name,author:e.author,version:e.version},n.format="freemind";var i=[];return i.push('<map version="1.0.1">'),t._build_map(e.root,i),i.push("</map>"),n.data=i.join(""),n},_parse_xml:function(e){var t=null;window.DOMParser?t=(new DOMParser).parseFromString(e,"text/xml"):((t=new ActiveXObject("Microsoft.XMLDOM")).async=!1,t.loadXML(e));return t},_find_root:function(e){for(var t=e.childNodes,n=null,i=null,o=0;o<t.length;o++)if(1==(i=t[o]).nodeType&&"map"==i.tagName){n=i;break}if(n){var r=n.childNodes;n=null;for(o=0;o<r.length;o++)if(1==(i=r[o]).nodeType&&"node"==i.tagName){n=i;break}}return n},_load_node:function(e,t,n){var i=b.freemind,o=n.getAttribute("ID"),r=n.getAttribute("TEXT"),s=n.getAttribute("FOLDED");if(null==r)for(var a=n.childNodes,d=null,l=0;l<a.length;l++)if(1==(d=a[l]).nodeType&&"richcontent"===d.tagName){r=d.textContent;break}var u=i._load_attributes(n),_="expanded"in u?"true"==u.expanded:"true"!=s;delete u.expanded;var c=n.getAttribute("POSITION"),v=null;c&&(v="left"==c?h.left:h.right);var f=null;f=t?e.add_node(t,o,r,u,v,_):e.set_root(o,r,u);var p=n.childNodes,y=null;for(l=0;l<p.length;l++)1==(y=p[l]).nodeType&&"node"==y.tagName&&i._load_node(e,f,y)},_load_attributes:function(e){for(var t=e.childNodes,n=null,i={},o=0;o<t.length;o++)1==(n=t[o]).nodeType&&"attribute"===n.tagName&&(i[n.getAttribute("NAME")]=n.getAttribute("VALUE"));return i},_build_map:function(e,t){var n=b.freemind,i=null;e.parent&&e.parent.isroot&&(i=e.direction===h.left?"left":"right"),t.push("<node"),t.push(' ID="'+e.id+'"'),i&&t.push(' POSITION="'+i+'"'),e.expanded||t.push(' FOLDED="true"'),t.push(' TEXT="'+n._escape(e.topic)+'">');var o=e.data;if(null!=o)for(var r in o)t.push('<attribute NAME="'+r+'" VALUE="'+o[r]+'"/>');for(var s=e.children,a=0;a<s.length;a++)n._build_map(s[a],t);t.push("</node>")},_escape:function(e){return e.replace(/&/g,"&amp;").replace(/</g,"&lt;").replace(/>/g,"&gt;").replace(/'/g,"&apos;").replace(/"/g,"&quot;")}},text:{example:{meta:w,format:"text",data:"jsMind text example\n node1\n  node1-sub\n  node1-sub\n node2"},_line_regex:/\s*/,get_mind:function(e){var t=b.text,n=new m;n.name=e.meta.name,n.author=e.meta.author,n.version=e.meta.version;var i=e.data.split(/\n|\r/);return t._fill_nodes(n,i,0,0),n},_fill_nodes:function(e,t){for(var n=[],i=0;i<t.length;){var o=t[i],r=o.match(/\s*/)[0].length,s=o.substr(r);if(0==r&&n.length>0)return void log.error("more than 1 root node was found: "+s);if(r>n.length)return void log.error("a suspended node was found: "+s);for(var a=n.length-r;a--;)n.pop();if(0==r&&0==n.length){var d=e.set_root(p.uuid.newid(),s);n.push(d)}else{var l=e.add_node(n[r-1],p.uuid.newid(),s,{},null);n.push(l)}i++}n.length=0},get_data:function(e){var t=b.text,n={};n.meta={name:e.name,author:e.author,version:e.version},n.format="text";var i=[];return t._build_lines(i,[e.root],0),n.data=i.join("\n"),n},_build_lines:function(e,t,n){var i,o=new Array(n+1).join(" "),r=d(t);try{for(r.s();!(i=r.n()).done;){var s=i.value;e.push(o+s.topic),s.children&&b.text._build_lines(e,s.children,n+1)}}catch(e){r.e(e)}finally{r.f()}}}},k=function(){return o((function e(t){n(this,e),this.jm=t}),[{key:"init",value:function(){v.debug("data.init")}},{key:"reset",value:function(){v.debug("data.reset")}},{key:"load",value:function(e){var n=null,i=null;return"node_array"==(n="object"===t(e)?e.format?e.format:"node_tree":"freemind")?i=b.node_array.get_mind(e):"node_tree"==n?i=b.node_tree.get_mind(e):"freemind"==n?i=b.freemind.get_mind(e):"text"==n?i=b.text.get_mind(e):v.warn("unsupported format"),i}},{key:"get_data",value:function(e){var t=null;return"node_array"==e?t=b.node_array.get_data(this.jm.mind):"node_tree"==e?t=b.node_tree.get_data(this.jm.mind):"freemind"==e?t=b.freemind.get_data(this.jm.mind):"text"==e?t=b.text.get_data(this.jm.mind):v.error("unsupported "+e+" format"),t}}])}(),x=function(){return o((function e(t,i){n(this,e),this.opts=i,this.jm=t,this.isside="side"==this.opts.mode,this.bounds=null,this.cache_valid=!1}),[{key:"init",value:function(){v.debug("layout.init")}},{key:"reset",value:function(){v.debug("layout.reset"),this.bounds={n:0,s:0,w:0,e:0}}},{key:"calculate_next_child_direction",value:function(e){if(this.isside)return h.right;for(var t=e.children||[],n=t.length,i=0,o=0;o<n;o++)t[o].direction===h.left?i--:i++;return n>1&&i>0?h.left:h.right}},{key:"layout",value:function(){v.debug("layout.layout"),this.layout_direction(),this.layout_offset()}},{key:"layout_direction",value:function(){this._layout_direction_root()}},{key:"_layout_direction_root",value:function(){var e=this.jm.mind.root,t=null;"layout"in e._data?t=e._data.layout:(t={},e._data.layout=t);var n=e.children,i=n.length;if(t.direction=h.center,t.side_index=0,this.isside)for(var o=i;o--;)this._layout_direction_side(n[o],h.right,o);else{o=i;for(var r=null;o--;)(r=n[o]).direction==h.left?this._layout_direction_side(r,h.left,o):this._layout_direction_side(r,h.right,o)}}},{key:"_layout_direction_side",value:function(e,t,n){var i=null;"layout"in e._data?i=e._data.layout:(i={},e._data.layout=i);var o=e.children,r=o.length;i.direction=t,i.side_index=n;for(var s=r;s--;)this._layout_direction_side(o[s],t,s)}},{key:"layout_offset",value:function(){var e=this.jm.mind.root,t=e._data.layout;t.offset_x=0,t.offset_y=0,t.outer_height=0;for(var n=e.children,i=n.length,o=[],r=[],s=null;i--;)(s=n[i])._data.layout.direction==h.right?r.unshift(s):o.unshift(s);t.left_nodes=o,t.right_nodes=r,t.outer_height_left=this._layout_offset_subnodes(o),t.outer_height_right=this._layout_offset_subnodes(r),this.bounds.e=e._data.view.width/2,this.bounds.w=0-this.bounds.e,this.bounds.n=0,this.bounds.s=Math.max(t.outer_height_left,t.outer_height_right)}},{key:"_layout_offset_subnodes",value:function(e){for(var t=0,n=e.length,i=n,o=null,r=0,s=null,a=0,d=null;i--;)s=(o=e[i])._data.layout,null==d&&(d=o.parent._data),r=this._layout_offset_subnodes(o.children),o.expanded||(r=0,this.set_visible(o.children,!1)),r=Math.max(o._data.view.height,r),o.children.length>1&&(r+=this.opts.cousin_space),s.outer_height=r,s.offset_y=a-r/2,s.offset_x=this.opts.hspace*s.direction+d.view.width*(d.layout.direction+s.direction)/2,o.parent.isroot||(s.offset_x+=this.opts.pspace*s.direction),a=a-r-this.opts.vspace,t+=r;n>1&&(t+=this.opts.vspace*(n-1)),i=n;for(var l=t/2;i--;)(o=e[i])._data.layout.offset_y+=l;return t}},{key:"_layout_offset_subnodes_height",value:function(e){for(var t=0,n=e.length,i=n,o=null,r=0,s=null,a=0,d=null;i--;)s=(o=e[i])._data.layout,null==d&&(d=o.parent._data),r=this._layout_offset_subnodes_height(o.children),o.expanded||(r=0),r=Math.max(o._data.view.height,r),o.children.length>1&&(r+=this.opts.cousin_space),s.outer_height=r,s.offset_y=a-r/2,a=a-r-this.opts.vspace,t+=r;n>1&&(t+=this.opts.vspace*(n-1)),i=n;for(var l=t/2;i--;)(o=e[i])._data.layout.offset_y+=l;return t}},{key:"get_node_offset",value:function(e){var t=e._data.layout,n=null;if("_offset_"in t&&this.cache_valid?n=t._offset_:(n={x:-1,y:-1},t._offset_=n),-1==n.x||-1==n.y){var i=t.offset_x,o=t.offset_y;if(!e.isroot){var r=this.get_node_offset(e.parent);i+=r.x,o+=r.y}n.x=i,n.y=o}return n}},{key:"get_node_point",value:function(e){var t=e._data.view,n=this.get_node_offset(e),i={};return i.x=n.x+t.width*(e._data.layout.direction-1)/2,i.y=n.y-t.height/2,i}},{key:"get_node_point_in",value:function(e){return this.get_node_offset(e)}},{key:"get_node_point_out",value:function(e){var t=e._data.layout,n=null;if("_pout_"in t&&this.cache_valid?n=t._pout_:(n={x:-1,y:-1},t._pout_=n),-1==n.x||-1==n.y)if(e.isroot)n.x=0,n.y=0;else{var i=e._data.view,o=this.get_node_offset(e);n.x=o.x+(i.width+this.opts.pspace)*e._data.layout.direction,n.y=o.y}return n}},{key:"get_expander_point",value:function(e){var t=this.get_node_point_out(e),n={};return e._data.layout.direction==h.right?n.x=t.x-this.opts.pspace:n.x=t.x,n.y=t.y-Math.ceil(this.opts.pspace/2),n}},{key:"get_min_size",value:function(){var e=this.jm.mind.nodes,t=null,n=null;for(var i in e)t=e[i],(n=this.get_node_point_out(t)).x>this.bounds.e&&(this.bounds.e=n.x),n.x<this.bounds.w&&(this.bounds.w=n.x);return{w:this.bounds.e-this.bounds.w,h:this.bounds.s-this.bounds.n}}},{key:"toggle_node",value:function(e){e.isroot||(e.expanded?this.collapse_node(e):this.expand_node(e))}},{key:"expand_node",value:function(e){e.expanded=!0,this.part_layout(e),this.set_visible(e.children,!0),this.jm.invoke_event_handle(u.show,{evt:"expand_node",data:[],node:e.id})}},{key:"collapse_node",value:function(e){e.expanded=!1,this.part_layout(e),this.set_visible(e.children,!1),this.jm.invoke_event_handle(u.show,{evt:"collapse_node",data:[],node:e.id})}},{key:"expand_all",value:function(){var e,t=this.jm.mind.nodes,n=0;for(var i in t)(e=t[i]).expanded||(e.expanded=!0,n++);if(n>0){var o=this.jm.mind.root;this.part_layout(o),this.set_visible(o.children,!0)}}},{key:"collapse_all",value:function(){var e,t=this.jm.mind.nodes,n=0;for(var i in t)(e=t[i]).expanded&&!e.isroot&&(e.expanded=!1,n++);if(n>0){var o=this.jm.mind.root;this.part_layout(o),this.set_visible(o.children,!0)}}},{key:"expand_to_depth",value:function(e,t,n){if(!(e<1))for(var i=t||this.jm.mind.root.children,o=n||1,r=i.length,s=null;r--;)s=i[r],o<e&&(s.expanded||this.expand_node(s),this.expand_to_depth(e,s.children,o+1)),o==e&&s.expanded&&this.collapse_node(s)}},{key:"part_layout",value:function(e){var t=this.jm.mind.root;if(t){var n=t._data.layout;e.isroot?(n.outer_height_right=this._layout_offset_subnodes_height(n.right_nodes),n.outer_height_left=this._layout_offset_subnodes_height(n.left_nodes)):e._data.layout.direction==h.right?n.outer_height_right=this._layout_offset_subnodes_height(n.right_nodes):n.outer_height_left=this._layout_offset_subnodes_height(n.left_nodes),this.bounds.s=Math.max(n.outer_height_left,n.outer_height_right),this.cache_valid=!1}else v.warn("can not found root node")}},{key:"set_visible",value:function(e,t){for(var n=e.length,i=null;n--;)(i=e[n])._data.layout,i.expanded?this.set_visible(i.children,t):this.set_visible(i.children,!1),i.isroot||(i._data.layout.visible=t)}},{key:"is_expand",value:function(e){return e.expanded}},{key:"is_visible",value:function(e){var t=e._data.layout;return!("visible"in t&&!t.visible)}}])}(),j=function(){function e(t){n(this,e),this.view=t,this.opts=t.opts,this.e_svg=e.c("svg"),this.e_svg.setAttribute("class","jsmind"),this.size={w:0,h:0},this.lines=[],this.line_drawing={straight:this._line_to,curved:this._bezier_to},this.drawing=this.line_drawing[this.opts.line_style]||this.line_drawing.curved}return o(e,[{key:"element",value:function(){return this.e_svg}},{key:"set_size",value:function(e,t){this.size.w=e,this.size.h=t,this.e_svg.setAttribute("width",e),this.e_svg.setAttribute("height",t)}},{key:"clear",value:function(){for(var e=this.lines.length;e--;)this.e_svg.removeChild(this.lines[e]);this.lines.length=0}},{key:"draw_line",value:function(t,n,i,o){var r=e.c("path");r.setAttribute("stroke",o||this.opts.line_color),r.setAttribute("stroke-width",this.opts.line_width),r.setAttribute("fill","transparent"),this.lines.push(r),this.e_svg.appendChild(r),this.drawing(r,n.x+i.x,n.y+i.y,t.x+i.x,t.y+i.y)}},{key:"copy_to",value:function(e,t){var n=new Image;n.onload=function(){e.drawImage(n,0,0),t&&t()},n.src="data:image/svg+xml;base64,"+btoa((new XMLSerializer).serializeToString(this.e_svg))}},{key:"_bezier_to",value:function(e,t,n,i,o){e.setAttribute("d","M "+t+" "+n+" C "+(t+2*(i-t)/3)+" "+n+", "+t+" "+o+", "+i+" "+o)}},{key:"_line_to",value:function(e,t,n,i,o){e.setAttribute("d","M "+t+" "+n+" L "+i+" "+o)}}],[{key:"c",value:function(e){return f.d.createElementNS("http://www.w3.org/2000/svg",e)}}])}(),z=function(){return o((function e(t){n(this,e),this.opts=t.opts,this.e_canvas=f.c("canvas"),this.e_canvas.className="jsmind",this.canvas_ctx=this.e_canvas.getContext("2d"),this.size={w:0,h:0},this.line_drawing={straight:this._line_to,curved:this._bezier_to},this.drawing=this.line_drawing[this.opts.line_style]||this.line_drawing.curved}),[{key:"element",value:function(){return this.e_canvas}},{key:"set_size",value:function(e,t){this.size.w=e,this.size.h=t,this.e_canvas.width=e,this.e_canvas.height=t}},{key:"clear",value:function(){this.canvas_ctx.clearRect(0,0,this.size.w,this.size.h)}},{key:"draw_line",value:function(e,t,n,i){var o=this.canvas_ctx;o.strokeStyle=i||this.opts.line_color,o.lineWidth=this.opts.line_width,o.lineCap="round",this.drawing(o,t.x+n.x,t.y+n.y,e.x+n.x,e.y+n.y)}},{key:"copy_to",value:function(e,t){e.drawImage(this.e_canvas,0,0),t&&t()}},{key:"_bezier_to",value:function(e,t,n,i,o){e.beginPath(),e.moveTo(t,n),e.bezierCurveTo(t+2*(i-t)/3,n,t,o,i,o),e.stroke()}},{key:"_line_to",value:function(e,t,n,i,o){e.beginPath(),e.moveTo(t,n),e.lineTo(i,o),e.stroke()}}])}();var C=function(){return o((function e(t,i){n(this,e),this.opts=i,this.jm=t,this.layout=t.layout,this.container=null,this.e_panel=null,this.e_nodes=null,this.size={w:0,h:0},this.selected_node=null,this.editing_node=null,this.graph=null,this.render_node=i.custom_node_render?this._custom_node_render:this._default_node_render,this._initialized=!1}),[{key:"init",value:function(){var e=this;if(v.debug(this.opts),v.debug("view.init"),this.container=f.i(this.opts.container)?this.opts.container:f.g(this.opts.container),this.container){var t;this.graph=(t=this,"svg"===this.opts.engine.toLowerCase()?new j(t):new z(t)),this.e_panel=f.c("div"),this.e_nodes=f.c("jmnodes"),this.e_editor=f.c("input"),this.e_panel.className="jsmind-inner jmnode-overflow-"+this.opts.node_overflow,this.e_panel.tabIndex=1,this.e_panel.appendChild(this.graph.element()),this.e_panel.appendChild(this.e_nodes),this.e_editor.className="jsmind-editor",this.e_editor.type="text",this.zoom_current=1;var n=this;f.on(this.e_editor,"keydown",(function(e){var t=e||event;13==t.keyCode&&(n.edit_node_end(),t.stopPropagation())})),f.on(this.e_editor,"blur",(function(e){n.edit_node_end()})),this.container.appendChild(this.e_panel),this.container.offsetParent||new IntersectionObserver(function(t,n){s(this,e),t[0].isIntersecting&&(n.unobserve(this.e_panel),this.resize())}.bind(this)).observe(this.e_panel)}else v.error("the options.view.container was not be found in dom")}},{key:"add_event",value:function(e,t,n,i){var o=i?this.e_panel:this.e_nodes;f.on(o,t,(function(t){var i=t||event;n.call(e,i)}))}},{key:"get_binded_nodeid",value:function(e){if(null==e)return null;var t=e.tagName.toLowerCase();return"jmnode"==t||"jmexpander"==t?e.getAttribute("nodeid"):"jmnodes"==t||"body"==t||"html"==t?null:this.get_binded_nodeid(e.parentElement)}},{key:"is_node",value:function(e){if(null==e)return!1;var t=e.tagName.toLowerCase();return"jmnode"==t||"jmnodes"!=t&&"body"!=t&&"html"!=t&&this.is_node(e.parentElement)}},{key:"is_expander",value:function(e){return"jmexpander"==e.tagName.toLowerCase()}},{key:"reset",value:function(){v.debug("view.reset"),this.selected_node=null,this.clear_lines(),this.clear_nodes(),this.reset_theme()}},{key:"reset_theme",value:function(){var e=this.jm.options.theme;this.e_nodes.className=e?"theme-"+e:""}},{key:"reset_custom_style",value:function(){var e=this.jm.mind.nodes;for(var t in e)this.reset_node_custom_style(e[t])}},{key:"load",value:function(){v.debug("view.load"),this.setup_canvas_draggable(this.opts.draggable),this.init_nodes(),this._initialized=!0}},{key:"expand_size",value:function(){var e=this.layout.get_min_size(),t=e.w+2*this.opts.hmargin,n=e.h+2*this.opts.vmargin,i=this.e_panel.clientWidth,o=this.e_panel.clientHeight;i<t&&(i=t),o<n&&(o=n),this.size.w=i,this.size.h=o}},{key:"init_nodes_size",value:function(e){var t=e._data.view;t.width=t.element.clientWidth,t.height=t.element.clientHeight}},{key:"init_nodes",value:function(){var e=this,t=this.jm.mind.nodes,n=f.d.createDocumentFragment();for(var i in t)this.create_node_element(t[i],n);this.e_nodes.appendChild(n),this.run_in_c11y_mode_if_needed(function(){for(var n in s(this,e),t)this.init_nodes_size(t[n])}.bind(this))}},{key:"add_node",value:function(e){var t=this;this.create_node_element(e,this.e_nodes),this.run_in_c11y_mode_if_needed(function(){s(this,t),this.init_nodes_size(e)}.bind(this))}},{key:"run_in_c11y_mode_if_needed",value:function(e){this.container.offsetParent?e():(v.warn("init nodes in compatibility mode. because the container or its parent has style {display:none}. "),this.e_panel.style.position="absolute",this.e_panel.style.top="-100000",f.d.body.appendChild(this.e_panel),e(),this.container.appendChild(this.e_panel),this.e_panel.style.position=null,this.e_panel.style.top=null)}},{key:"create_node_element",value:function(e,t){var n=null;"view"in e._data?n=e._data.view:(n={},e._data.view=n);var i=f.c("jmnode");if(e.isroot)i.className="root";else{var o=f.c("jmexpander");f.t(o,"-"),o.setAttribute("nodeid",e.id),o.style.visibility="hidden",t.appendChild(o),n.expander=o}e.topic&&this.render_node(i,e),i.setAttribute("nodeid",e.id),i.style.visibility="hidden",this._reset_node_custom_style(i,e.data),t.appendChild(i),n.element=i}},{key:"remove_node",value:function(e){null!=this.selected_node&&this.selected_node.id==e.id&&(this.selected_node=null),null!=this.editing_node&&this.editing_node.id==e.id&&(e._data.view.element.removeChild(this.e_editor),this.editing_node=null);for(var t=e.children,n=t.length;n--;)this.remove_node(t[n]);if(e._data.view){var i=e._data.view.element,o=e._data.view.expander;this.e_nodes.removeChild(i),this.e_nodes.removeChild(o),e._data.view.element=null,e._data.view.expander=null}}},{key:"update_node",value:function(e){var t=e._data.view,n=t.element;if(e.topic&&this.render_node(n,e),this.layout.is_visible(e))t.width=n.clientWidth,t.height=n.clientHeight;else{var i=n.getAttribute("style");n.style="visibility: visible; left:0; top:0;",t.width=n.clientWidth,t.height=n.clientHeight,n.style=i}}},{key:"select_node",value:function(e){if(this.selected_node){var t=this.selected_node._data.view.element;t.className=t.className.replace(/\s*selected\b/i,""),this.restore_selected_node_custom_style(this.selected_node)}e&&(this.selected_node=e,e._data.view.element.className+=" selected",this.clear_selected_node_custom_style(e))}},{key:"select_clear",value:function(){this.select_node(null)}},{key:"get_editing_node",value:function(){return this.editing_node}},{key:"is_editing",value:function(){return!!this.editing_node}},{key:"edit_node_begin",value:function(e){if(e.topic){null!=this.editing_node&&this.edit_node_end(),this.editing_node=e;var t=e._data.view.element,n=e.topic,i=getComputedStyle(t);this.e_editor.value=n,this.e_editor.style.width=t.clientWidth-parseInt(i.getPropertyValue("padding-left"))-parseInt(i.getPropertyValue("padding-right"))+"px",t.innerHTML="",t.appendChild(this.e_editor),t.style.zIndex=5,this.e_editor.focus(),this.e_editor.select()}else v.warn("don't edit image nodes")}},{key:"edit_node_end",value:function(){if(null!=this.editing_node){var e=this.editing_node;this.editing_node=null;var t=e._data.view.element,n=this.e_editor.value;t.style.zIndex="auto",t.removeChild(this.e_editor),p.text.is_empty(n)||e.topic===n?this.render_node(t,e):this.jm.update_node(e.id,n)}this.e_panel.focus()}},{key:"get_view_offset",value:function(){var e=this.layout.bounds,t=this.jm.get_root(),n=t._data.view.hideNode,i=(this.size.w-e.e-e.w)/2,o=this.size.h/2;return{x:n?i-t._data.view.width:i,y:o}}},{key:"resize",value:function(){this.graph.set_size(1,1),this.e_nodes.style.width="1px",this.e_nodes.style.height="1px",this.expand_size(),this._show()}},{key:"hideRootNodeVisible",value:function(e){var t=this.jm.get_root();t._data.view.hideNode=e;for(var n=t.children,i=0;i<n.length;i++)n[i]._data.layout.hideLine=e;this.show_nodes(),this.show_lines()}},{key:"_show",value:function(){this.graph.set_size(this.size.w,this.size.h),this.e_nodes.style.width=this.size.w+"px",this.e_nodes.style.height=this.size.h+"px",this.show_nodes(),this.show_lines(),this.jm.invoke_event_handle(u.resize,{data:[]})}},{key:"zoom_in",value:function(e){return this.set_zoom(this.zoom_current+this.opts.zoom.step,e)}},{key:"zoom_out",value:function(e){return this.set_zoom(this.zoom_current-this.opts.zoom.step,e)}},{key:"set_zoom",value:function(e,t){if(e<this.opts.zoom.min||e>this.opts.zoom.max)return!1;var n=this.e_panel.getBoundingClientRect();if(e<1&&e<this.zoom_current&&this.size.w*e<n.width&&this.size.h*e<n.height)return!1;var i=t?{x:t.x-n.x,y:t.y-n.y}:{x:n.width/2,y:n.height/2},o=(this.e_panel.scrollLeft+i.x)*e/this.zoom_current-i.x,r=(this.e_panel.scrollTop+i.y)*e/this.zoom_current-i.y;this.zoom_current=e;for(var s=0;s<this.e_panel.children.length;s++)this.e_panel.children[s].style.zoom=e;return this._show(),this.e_panel.scrollLeft=o,this.e_panel.scrollTop=r,!0}},{key:"show",value:function(e){v.debug("view.show"),this.expand_size(),this._show(),e&&this.center_node(this.jm.mind.root)}},{key:"relayout",value:function(){this.expand_size(),this._show()}},{key:"save_location",value:function(e){var t=e._data.view;t._saved_location={x:parseInt(t.element.style.left)-this.e_panel.scrollLeft,y:parseInt(t.element.style.top)-this.e_panel.scrollTop}}},{key:"restore_location",value:function(e){var t=e._data.view;this.e_panel.scrollLeft=parseInt(t.element.style.left)-t._saved_location.x,this.e_panel.scrollTop=parseInt(t.element.style.top)-t._saved_location.y}},{key:"clear_nodes",value:function(){var e=this.jm.mind;if(null!=e){var t=e.nodes,n=null;for(var i in t)(n=t[i])._data.view.element=null,n._data.view.expander=null;this.e_nodes.innerHTML=""}}},{key:"show_nodes",value:function(){var e=this.jm.mind.nodes,t=null,n=null,i=null,o=null,r=this.get_view_offset();for(var s in e)n=(o=(t=e[s])._data.view).element,this.layout.is_visible(t)&&!o.hideNode?(this.reset_node_custom_style(t),i=this.layout.get_node_point(t),o.abs_x=r.x+i.x,o.abs_y=r.y+i.y,n.style.left=r.x+i.x+"px",n.style.top=r.y+i.y+"px",n.style.display="",n.style.visibility="visible",this._show_expander(t,r)):(n.style.display="none",o.expander&&o.expander.style&&(o.expander.style.display="none"))}},{key:"_show_expander",value:function(e,t){if(!e.isroot){var n=e._data.view.expander;if(0==e.children.length)return n.style.display="none",void(n.style.visibility="hidden");var i=this._get_expander_text(e);f.t(n,i);var o=this.layout.get_expander_point(e);n.style.left=t.x+o.x+3+"px",n.style.top=t.y+o.y+3+"px",n.style.display="",n.style.visibility="visible",n.style.backgroundColor=e.data.expandColor}}},{key:"_get_expander_text",value:function(e){var t=this.opts.expander_style?this.opts.expander_style.toLowerCase():"char";return"number"===t?e.children.length>99?"...":e.children.length:"char"===t?e.expanded?"-":"+":void 0}},{key:"_default_node_render",value:function(e,t){this.opts.support_html?f.h(e,t.topic):f.t(e,t.topic)}},{key:"_custom_node_render",value:function(e,t){this.opts.custom_node_render(this.jm,e,t)||this._default_node_render(e,t)}},{key:"reset_node_custom_style",value:function(e){this._reset_node_custom_style(e._data.view.element,e.data)}},{key:"_reset_node_custom_style",value:function(e,t){if("background-color"in t&&(e.style.backgroundColor=t["background-color"]),"foreground-color"in t&&(e.style.color=t["foreground-color"]),"width"in t&&(e.style.width=t.width+"px"),"height"in t&&(e.style.height=t.height+"px"),"font-size"in t&&(e.style.fontSize=t["font-size"]+"px"),"font-weight"in t&&(e.style.fontWeight=t["font-weight"]),"font-style"in t&&(e.style.fontStyle=t["font-style"]),"background-image"in t){var n=t["background-image"];if(n.startsWith("data")&&t.width&&t.height){var i=new Image;i.onload=function(){var t=f.c("canvas");t.width=e.clientWidth,t.height=e.clientHeight;if(t.getContext){t.getContext("2d").drawImage(this,2,2,e.clientWidth,e.clientHeight);var n=t.toDataURL();e.style.backgroundImage="url("+n+")"}},i.src=n}else e.style.backgroundImage="url("+n+")";e.style.backgroundSize="99%","background-rotation"in t&&(e.style.transform="rotate("+t["background-rotation"]+"deg)")}}},{key:"restore_selected_node_custom_style",value:function(e){var t=e._data.view.element,n=e.data;"background-color"in n&&(t.style.backgroundColor=n["background-color"]),"foreground-color"in n&&(t.style.color=n["foreground-color"])}},{key:"clear_selected_node_custom_style",value:function(e){var t=e._data.view.element;t.style.backgroundColor="",t.style.color=""}},{key:"clear_lines",value:function(){this.graph.clear()}},{key:"show_lines",value:function(){this.clear_lines();var e=this.jm.mind.nodes,t=null,n=null,i=null,o=null,r=this.get_view_offset();for(var s in e)(t=e[s]).isroot||this.layout.is_visible(t)&&(t._data.layout.hideLine||(n=this.layout.get_node_point_in(t),i=this.layout.get_node_point_out(t.parent),o=t.data["leading-line-color"],this.graph.draw_line(i,n,r,o)))}},{key:"setup_canvas_draggable",value:function(e){var t=this;if(this.opts.draggable=e,!this._initialized){var n,i,o=!1;this.opts.hide_scrollbars_when_draggable&&(this.e_panel.style="overflow: hidden"),f.on(this.container,"mousedown",function(e){s(this,t),this.opts.draggable&&(o=!0,n=e.clientX,i=e.clientY)}.bind(this)),f.on(this.container,"mouseup",function(){s(this,t),o=!1}.bind(this)),f.on(this.container,"mousemove",function(e){s(this,t),this.opts.draggable&&o&&(this.e_panel.scrollBy(n-e.clientX,i-e.clientY),n=e.clientX,i=e.clientY)}.bind(this))}}},{key:"center_node",value:function(e){if(!this.layout.is_visible(e))return v.warn("can not scroll to the node, because it is invisible"),!1;var t=e._data.view,n=this.e_panel.getBoundingClientRect(),i=t.abs_x+t.width/2,o=t.abs_y+t.height/2;return this.e_panel.scrollTo(i*this.zoom_current-n.width/2,o*this.zoom_current-n.height/2),!0}},{key:"zoomIn",value:function(e){return v.warn("please use zoom_in instead"),this.zoom_in(e)}},{key:"zoomOut",value:function(e){return v.warn("please use zoom_out instead"),this.zoom_out(e)}},{key:"setZoom",value:function(e,t){return v.warn("please use set_zoom instead"),this.set_zoom(e,t)}}])}(),T=function(){return o((function e(t,i){n(this,e),this.jm=t,this.opts=i,this.mapping=i.mapping,this.handles=i.handles,this._newid=null,this._mapping={}}),[{key:"init",value:function(){for(var e in f.on(this.jm.view.e_panel,"keydown",this.handler.bind(this)),this.handles.addchild=this.handle_addchild,this.handles.addbrother=this.handle_addbrother,this.handles.editnode=this.handle_editnode,this.handles.delnode=this.handle_delnode,this.handles.toggle=this.handle_toggle,this.handles.up=this.handle_up,this.handles.down=this.handle_down,this.handles.left=this.handle_left,this.handles.right=this.handle_right,this.mapping)if(this.mapping[e]&&e in this.handles){var t=this.mapping[e];Array.isArray(t)||(t=[t]);var n,i=d(t);try{for(i.s();!(n=i.n()).done;){var o=n.value;this._mapping[o]=this.handles[e]}}catch(e){i.e(e)}finally{i.f()}}"function"==typeof this.opts.id_generator?this._newid=this.opts.id_generator:this._newid=p.uuid.newid}},{key:"enable_shortcut",value:function(){this.opts.enable=!0}},{key:"disable_shortcut",value:function(){this.opts.enable=!1}},{key:"handler",value:function(e){if(9==e.which&&e.preventDefault(),!this.jm.view.is_editing()){var t=e||event;if(!this.opts.enable)return!0;var n=t.keyCode+(t.metaKey<<13)+(t.ctrlKey<<12)+(t.altKey<<11)+(t.shiftKey<<10);n in this._mapping&&this._mapping[n].call(this,this.jm,e)}}},{key:"handle_addchild",value:function(e,t){var n=e.get_selected_node();if(n){var i=this._newid();e.add_node(n,i,"New Node")&&(e.select_node(i),e.begin_edit(i))}}},{key:"handle_addbrother",value:function(e,t){var n=e.get_selected_node();if(n&&!n.isroot){var i=this._newid();e.insert_node_after(n,i,"New Node")&&(e.select_node(i),e.begin_edit(i))}}},{key:"handle_editnode",value:function(e,t){var n=e.get_selected_node();n&&e.begin_edit(n)}},{key:"handle_delnode",value:function(e,t){var n=e.get_selected_node();n&&!n.isroot&&(e.select_node(n.parent),e.remove_node(n))}},{key:"handle_toggle",value:function(e,t){var n=t||event,i=e.get_selected_node();i&&(e.toggle_node(i.id),n.stopPropagation(),n.preventDefault())}},{key:"handle_up",value:function(e,t){var n=t||event,i=e.get_selected_node();if(i){var o=e.find_node_before(i);if(!o){var r=e.find_node_before(i.parent);r&&r.children.length>0&&(o=r.children[r.children.length-1])}o&&e.select_node(o),n.stopPropagation(),n.preventDefault()}}},{key:"handle_down",value:function(e,t){var n=t||event,i=e.get_selected_node();if(i){var o=e.find_node_after(i);if(!o){var r=e.find_node_after(i.parent);r&&r.children.length>0&&(o=r.children[0])}o&&e.select_node(o),n.stopPropagation(),n.preventDefault()}}},{key:"handle_left",value:function(e,t){this._handle_direction(e,t,h.left)}},{key:"handle_right",value:function(e,t){this._handle_direction(e,t,h.right)}},{key:"_handle_direction",value:function(e,t,n){var i=t||event,o=e.get_selected_node(),r=null;if(o){if(o.isroot){for(var s=o.children,a=[],d=0;d<s.length;d++)s[d].direction===n&&a.push(d);r=s[a[Math.floor((a.length-1)/2)]]}else if(o.direction===n){var l=(a=o.children).length;l>0&&(r=a[Math.floor((l-1)/2)])}else r=o.parent;r&&e.select_node(r),i.stopPropagation(),i.preventDefault()}}}])}();[String,Array].forEach((function(e){e.prototype.includes||(e.prototype.includes=function(e,t){return"number"!=typeof t&&(t=0),!(t+e.length>this.length)&&-1!==this.indexOf(e,t)})}));var N={plugins:[]};function E(e,t){f.w.setTimeout((function(){!function(e,t){var n=this;N.plugins.forEach(function(i){return s(this,n),i.fn_init(e,t[i.name])}.bind(this))}(e,t)}),0)}var L=o((function e(t,i){if(n(this,e),!t)throw new Error("plugin must has a name");if(!i||"function"!=typeof i)throw new Error("plugin must has an init function");this.name=t,this.fn_init=i}));window.scrollTo||(window.scrollTo=function(e){window.scrollLeft=e.left,window.scrollTop=e.top}),document.body.scrollTo||(Element.prototype.scrollTo=function(e){this.scrollLeft=e.left,this.scrollTop=e.top});var M=function(){function e(t){n(this,e),e.current=this,this.options=function(e){var t={};if(p.json.merge(t,y),p.json.merge(t,e),!t.container)throw new Error("the options.container should not be null or empty.");return t}(t),v.level(_[this.options.log_level]),this.version=l,this.initialized=!1,this.mind=null,this.event_handles=[],this.init()}return o(e,[{key:"init",value:function(){if(!this.initialized){this.initialized=!0;var e={mode:this.options.mode,hspace:this.options.layout.hspace,vspace:this.options.layout.vspace,pspace:this.options.layout.pspace,cousin_space:this.options.layout.cousin_space},t={container:this.options.container,support_html:this.options.support_html,engine:this.options.view.engine,hmargin:this.options.view.hmargin,vmargin:this.options.view.vmargin,line_width:this.options.view.line_width,line_color:this.options.view.line_color,line_style:this.options.view.line_style,draggable:this.options.view.draggable,hide_scrollbars_when_draggable:this.options.view.hide_scrollbars_when_draggable,node_overflow:this.options.view.node_overflow,zoom:this.options.view.zoom,custom_node_render:this.options.view.custom_node_render,expander_style:this.options.view.expander_style};this.data=new k(this),this.layout=new x(this,e),this.view=new C(this,t),this.shortcut=new T(this,this.options.shortcut),this.data.init(),this.layout.init(),this.view.init(),this.shortcut.init(),this._event_bind(),E(this,this.options.plugin)}}},{key:"get_editable",value:function(){return this.options.editable}},{key:"enable_edit",value:function(){this.options.editable=!0}},{key:"disable_edit",value:function(){this.options.editable=!1}},{key:"get_view_draggable",value:function(){return this.options.view.draggable}},{key:"enable_view_draggable",value:function(){this.options.view.draggable=!0,this.view.setup_canvas_draggable(!0)}},{key:"disable_view_draggable",value:function(){this.options.view.draggable=!1,this.view.setup_canvas_draggable(!1)}},{key:"enable_event_handle",value:function(e){this.options.default_event_handle["enable_"+e+"_handle"]=!0}},{key:"disable_event_handle",value:function(e){this.options.default_event_handle["enable_"+e+"_handle"]=!1}},{key:"set_theme",value:function(e){var t=this.options.theme;this.options.theme=e||null,t!=this.options.theme&&(this.view.reset_theme(),this.view.reset_custom_style())}},{key:"_event_bind",value:function(){this.view.add_event(this,"mousedown",this.mousedown_handle),this.view.add_event(this,"click",this.click_handle),this.view.add_event(this,"dblclick",this.dblclick_handle),this.view.add_event(this,"mousewheel",this.mousewheel_handle,!0)}},{key:"mousedown_handle",value:function(e){if(this.options.default_event_handle.enable_mousedown_handle){var t=e.target||event.srcElement,n=this.view.get_binded_nodeid(t);n?this.view.is_node(t)&&this.select_node(n):this.select_clear()}}},{key:"click_handle",value:function(e){if(this.options.default_event_handle.enable_click_handle){var t=e.target||event.srcElement;if(this.view.is_expander(t)){var n=this.view.get_binded_nodeid(t);n&&this.toggle_node(n)}}}},{key:"dblclick_handle",value:function(e){if(this.options.default_event_handle.enable_dblclick_handle&&this.get_editable()){var t=e.target||event.srcElement;if(this.view.is_node(t)){var n=this.view.get_binded_nodeid(t);n&&this.begin_edit(n)}}}},{key:"mousewheel_handle",value:function(e){if(this.options.default_event_handle.enable_mousewheel_handle&&e.ctrlKey){var t=e||event;t.preventDefault(),t.deltaY<0?this.view.zoom_in(t):this.view.zoom_out(t)}}},{key:"begin_edit",value:function(e){if(!g.is_node(e)){var t=this.get_node(e);return t?this.begin_edit(t):(v.error("the node[id="+e+"] can not be found."),!1)}this.get_editable()?this.view.edit_node_begin(e):v.error("fail, this mind map is not editable.")}},{key:"end_edit",value:function(){this.view.edit_node_end()}},{key:"toggle_node",value:function(e){if(!g.is_node(e)){var t=this.get_node(e);return t?this.toggle_node(t):void v.error("the node[id="+e+"] can not be found.")}e.isroot||(this.view.save_location(e),this.layout.toggle_node(e),this.view.relayout(),this.view.restore_location(e))}},{key:"expand_node",value:function(e){if(!g.is_node(e)){var t=this.get_node(e);return t?this.expand_node(t):void v.error("the node[id="+e+"] can not be found.")}e.isroot||(this.view.save_location(e),this.layout.expand_node(e),this.view.relayout(),this.view.restore_location(e))}},{key:"collapse_node",value:function(e){if(!g.is_node(e)){var t=this.get_node(e);return t?this.collapse_node(t):void v.error("the node[id="+e+"] can not be found.")}e.isroot||(this.view.save_location(e),this.layout.collapse_node(e),this.view.relayout(),this.view.restore_location(e))}},{key:"expand_all",value:function(){this.layout.expand_all(),this.view.relayout()}},{key:"collapse_all",value:function(){this.layout.collapse_all(),this.view.relayout()}},{key:"expand_to_depth",value:function(e){this.layout.expand_to_depth(e),this.view.relayout()}},{key:"_reset",value:function(){this.view.reset(),this.layout.reset(),this.data.reset()}},{key:"_show",value:function(e){var t=e||b.node_array.example;this.mind=this.data.load(t),this.mind?(v.debug("data.load ok"),this.view.load(),v.debug("view.load ok"),this.layout.layout(),v.debug("layout.layout ok"),this.view.show(!0),v.debug("view.show ok"),this.invoke_event_handle(u.show,{data:[e]})):v.error("data.load error")}},{key:"show",value:function(e){this._reset(),this._show(e)}},{key:"get_meta",value:function(){return{name:this.mind.name,author:this.mind.author,version:this.mind.version}}},{key:"get_data",value:function(e){var t=e||"node_tree";return this.data.get_data(t)}},{key:"get_root",value:function(){return this.mind.root}},{key:"get_node",value:function(e){return g.is_node(e)?e:this.mind.get_node(e)}},{key:"add_node",value:function(e,t,n,i,o){if(this.get_editable()){var r=this.get_node(e),s=h.of(o);void 0===s&&(s=this.layout.calculate_next_child_direction(r));var a=this.mind.add_node(r,t,n,i,s);return a&&(this.view.add_node(a),this.layout.layout(),this.view.show(!1),this.view.reset_node_custom_style(a),this.expand_node(r),this.invoke_event_handle(u.edit,{evt:"add_node",data:[r.id,t,n,i,s],node:t})),a}return v.error("fail, this mind map is not editable"),null}},{key:"insert_node_before",value:function(e,t,n,i,o){if(this.get_editable()){var r=this.get_node(e),s=h.of(o);void 0===s&&(s=this.layout.calculate_next_child_direction(r.parent));var a=this.mind.insert_node_before(r,t,n,i,s);return a&&(this.view.add_node(a),this.layout.layout(),this.view.show(!1),this.invoke_event_handle(u.edit,{evt:"insert_node_before",data:[r.id,t,n,i,s],node:t})),a}return v.error("fail, this mind map is not editable"),null}},{key:"insert_node_after",value:function(e,t,n,i,o){if(this.get_editable()){var r=this.get_node(e),s=h.of(o);void 0===s&&(s=this.layout.calculate_next_child_direction(r.parent));var a=this.mind.insert_node_after(r,t,n,i,s);return a&&(this.view.add_node(a),this.layout.layout(),this.view.show(!1),this.invoke_event_handle(u.edit,{evt:"insert_node_after",data:[r.id,t,n,i,s],node:t})),a}return v.error("fail, this mind map is not editable"),null}},{key:"remove_node",value:function(e){if(!g.is_node(e)){var t=this.get_node(e);return t?this.remove_node(t):(v.error("the node[id="+e+"] can not be found."),!1)}if(this.get_editable()){if(e.isroot)return v.error("fail, can not remove root node"),!1;var n=e.id,i=e.parent.id,o=this.get_node(i);return this.view.save_location(o),this.view.remove_node(e),this.mind.remove_node(e),this.layout.layout(),this.view.show(!1),this.view.restore_location(o),this.invoke_event_handle(u.edit,{evt:"remove_node",data:[n],node:i}),!0}return v.error("fail, this mind map is not editable"),!1}},{key:"update_node",value:function(e,t){if(this.get_editable())if(p.text.is_empty(t))v.warn("fail, topic can not be empty");else{var n=this.get_node(e);if(n){if(n.topic===t)return v.info("nothing changed"),void this.view.update_node(n);n.topic=t,this.view.update_node(n),this.layout.layout(),this.view.show(!1),this.invoke_event_handle(u.edit,{evt:"update_node",data:[e,t],node:e})}}else v.error("fail, this mind map is not editable")}},{key:"move_node",value:function(e,t,n,i){if(this.get_editable()){var o=this.get_node(e),r=this.mind.move_node(o,t,n,i);r&&(this.view.update_node(r),this.layout.layout(),this.view.show(!1),this.invoke_event_handle(u.edit,{evt:"move_node",data:[e,t,n,i],node:e}))}else v.error("fail, this mind map is not editable")}},{key:"select_node",value:function(e){if(!g.is_node(e)){var t=this.get_node(e);return t?this.select_node(t):void v.error("the node[id="+e+"] can not be found.")}this.layout.is_visible(e)&&(this.mind.selected=e,this.view.select_node(e),this.invoke_event_handle(u.select,{evt:"select_node",data:[],node:e.id}))}},{key:"get_selected_node",value:function(){return this.mind?this.mind.selected:null}},{key:"select_clear",value:function(){this.mind&&(this.mind.selected=null,this.view.select_clear())}},{key:"is_node_visible",value:function(e){return this.layout.is_visible(e)}},{key:"scroll_node_to_center",value:function(e){if(g.is_node(e))this.view.center_node(e);else{var t=this.get_node(e);t?this.scroll_node_to_center(t):v.error("the node[id="+e+"] can not be found.")}}},{key:"find_node_before",value:function(e){if(!g.is_node(e)){var t=this.get_node(e);return t?this.find_node_before(t):void v.error("the node[id="+e+"] can not be found.")}if(e.isroot)return null;var n=null;if(e.parent.isroot)for(var i=e.parent.children,o=null,r=null,s=0;s<i.length;s++)r=i[s],e.direction===r.direction&&(e.id===r.id&&(n=o),o=r);else n=this.mind.get_node_before(e);return n}},{key:"find_node_after",value:function(e){if(!g.is_node(e)){var t=this.get_node(e);return t?this.find_node_after(t):void v.error("the node[id="+e+"] can not be found.")}if(e.isroot)return null;var n=null;if(e.parent.isroot){for(var i=e.parent.children,o=!1,r=null,s=0;s<i.length;s++)if(r=i[s],e.direction===r.direction){if(o){n=r;break}e.id===r.id&&(o=!0)}}else n=this.mind.get_node_after(e);return n}},{key:"set_node_color",value:function(e,t,n){if(!this.get_editable())return v.error("fail, this mind map is not editable"),null;var i=this.mind.get_node(e);i&&(t&&(i.data["background-color"]=t),n&&(i.data["foreground-color"]=n),this.view.reset_node_custom_style(i))}},{key:"set_node_font_style",value:function(e,t,n,i){if(!this.get_editable())return v.error("fail, this mind map is not editable"),null;var o=this.mind.get_node(e);o&&(t&&(o.data["font-size"]=t),n&&(o.data["font-weight"]=n),i&&(o.data["font-style"]=i),this.view.reset_node_custom_style(o),this.view.update_node(o),this.layout.layout(),this.view.show(!1))}},{key:"set_node_background_image",value:function(e,t,n,i,o){if(!this.get_editable())return v.error("fail, this mind map is not editable"),null;var r=this.mind.get_node(e);r&&(t&&(r.data["background-image"]=t),n&&(r.data.width=n),i&&(r.data.height=i),o&&(r.data["background-rotation"]=o),this.view.reset_node_custom_style(r),this.view.update_node(r),this.layout.layout(),this.view.show(!1))}},{key:"set_node_background_rotation",value:function(e,t){if(!this.get_editable())return v.error("fail, this mind map is not editable"),null;var n=this.mind.get_node(e);if(n){if(!n.data["background-image"])return v.error("fail, only can change rotation angle of node with background image"),null;n.data["background-rotation"]=t,this.view.reset_node_custom_style(n),this.view.update_node(n),this.layout.layout(),this.view.show(!1)}}},{key:"resize",value:function(){this.view.resize()}},{key:"add_event_listener",value:function(e){"function"==typeof e&&this.event_handles.push(e)}},{key:"clear_event_listener",value:function(){this.event_handles=[]}},{key:"invoke_event_handle",value:function(e,t){var n=this;f.w.setTimeout((function(){n._invoke_event_handle(e,t)}),0)}},{key:"_invoke_event_handle",value:function(e,t){for(var n=this.event_handles.length,i=0;i<n;i++)this.event_handles[i](e,t)}}],[{key:"show",value:function(t,n){v.warn("`jsMind.show(options, mind)` is deprecated, please use `jm = new jsMind(options); jm.show(mind);` instead");var i=new e(t);return i.show(n),i}}])}();return r(M,"mind",m),r(M,"node",g),r(M,"direction",h),r(M,"event_type",u),r(M,"$",f),r(M,"plugin",L),r(M,"register_plugin",(function(e){var t=this;if(!(e instanceof L))throw new Error("can not register plugin, it is not an instance of Plugin");if(N.plugins.map(function(e){return s(this,t),e.name}.bind(this)).includes(e.name))throw new Error("can not register plugin "+e.name+": plugin name already exist");N.plugins.push(e)})),r(M,"util",p),M}));
//# sourceMappingURL=jsmind.js.map
