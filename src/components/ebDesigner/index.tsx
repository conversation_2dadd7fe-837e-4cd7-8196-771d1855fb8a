import React from 'react';

export const EbDesigner = React.lazy(
  () => import(
    /* webpackChunkName: "ebdmindmap_ebDesigner" */
    './Content'
  ),
) as any;
export const EbDesignerViewPage = React.lazy(() => import('./preview')) as any;
export const Doc = React.lazy(() => import('./doc')) as any;
export const EbQuickCreate = React.lazy(() => import('./quick-create')) as any;
export const EbFormEngine = React.lazy(
  () => import(
    /* webpackChunkName: "ebdmindmap_ebFormEngine" */
    './EbFormEngine'
  ),
) as any;

export default {};
