import { CorsComponent } from '@weapp/ui';
import React, { PureComponent } from 'react';
import { LocaleExProps } from './types';

export default class LocaleInput extends PureComponent<LocaleExProps> {
  render(): React.ReactNode {
    const {
      value, readOnly, ebBusinessId, ...restProps
    } = this.props;
    return (
      <CorsComponent
        weId={`${this.props.weId || ''}_2nxere`}
        app="@weapp/ebdform"
        compName="LocaleInput"
        {...(restProps as any)}
        ebBusinessId={ebBusinessId}
        readOnly={readOnly}
        value={value}
      />
    );
  }
}
