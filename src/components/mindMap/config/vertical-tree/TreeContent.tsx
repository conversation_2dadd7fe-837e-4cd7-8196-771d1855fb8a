import { observer } from 'mobx-react';
import React from 'react';
import { TreeBoxProps } from '../../engine/drag-tree/types';
import { TreeData } from '../../engine/types';
import TreeNode from './TreeNode';

@observer
class TreeContent extends React.Component<TreeBoxProps> {
  renderNode(node: TreeData) {
    const {
      onRemove, onNodeOptClick, selector, onCreate,
    } = this.props;

    return (
      <TreeNode
        weId={`${this.props.weId || ''}_i3tco3`}
        node={node}
        key={node.id || ''}
        onRemove={onRemove}
        onNodeOptClick={onNodeOptClick}
        onCreate={onCreate}
        selector={selector}
      >
        {node.children?.map((n) => this.renderNode(n))}
      </TreeNode>
    );
  }

  render() {
    const { data } = this.props;
    const node = data[0];
    if (!node) {
      return null;
    }
    return this.renderNode(node);
  }
}

export default TreeContent;
