import { CorsComponent, Dialog, Icon } from '@weapp/ui';
import { observer } from 'mobx-react';
import React from 'react';
import { UrlDockerDialogProps } from './types';

@observer
export default class UrlDockerDialog extends React.Component<UrlDockerDialogProps> {
  getDelete = () => (<Icon
    name="Icon-error01"
    weId={`${this.props.weId || ''}_a85xb1`}
    onClick={this.props.dialogParams.onClose}
    className="ui-dialog-icon ui-dialog-closeIcon"// 暂时直接使用组件库的样式
  />)

  render() {
    const { dialogParams, ...dockerProps } = this.props;
    return (
      <Dialog
        weId={`${this.props.weId || ''}_ifhs7g`}
        closable
        destroyOnClose
        scale
        maskClosable
        noMaskClose
        placement="right"
        noTitle
        {...dialogParams}
      >
        <div style={{ height: '100%', width: '100%' }}>
         <CorsComponent
            weId={`${this.props.weId || ''}_3766hu`}
            app="@weapp/ebdfpage"
            compName="UrlDocker"
            {...dockerProps}
          />
        </div>
      </Dialog>
    );
  }
}
