import { ILayoutData } from '@weapp/designer';
import { CorsComponent } from '@weapp/ui';
// import { CorsComponent } from '@weapp/ui';
import React from 'react';
import { withRouter } from 'react-router-dom';

interface FlowDesignerDemoStates {
  layoutDatas: ILayoutData[] | null;
}

class FlowDesignDemo extends React.PureComponent<any, FlowDesignerDemoStates> {

  render() {
    const { pageId } = this.props.match.params;
    return (
      // 设计器页面
      <>
         <CorsComponent weId={`${this.props.weId || ''}_qoyb1k`}
          app="@weapp/ebddesigner"
          compName="Design"
          pageId={`${pageId}`}
        />
      </>
    );
  }
}

export default withRouter(FlowDesignDemo);
