import React from "react";
import { configure } from 'mobx';
import { Provider } from 'mobx-react';

import RouteMainEntrance from './routes';
/* 导出库的样式文件，只在应用内使用不做共享的，请写在 style/index.less */
import './style/lib.less';
import { LazyType } from "./types/common";

/* libVersion导出 */
export * as libVersion from './libVersion';
/* mobx 配置 */
configure({ enforceActions: 'always' });

/* 常量导出 */
export * as constants from './constants';

export * from './utils/jsAPI';

const stores = {};

// 导出主路由组件，包裹共享的全局 store
export function RouterMain () {
  return (
    <Provider weId={`_dppjux`} {...stores}>
      <RouteMainEntrance weId={`_giucfr`} isLib/>
    </Provider>
  )
}

export const MindMapBuilderHeader = React.lazy(() => import( /* webpackChunkName: "ebdmindMap_MindMapBuilderHeader" */'./components/mindMap/engine/mindmap-builder')) as LazyType;
export * as ebcoms from './ebcoms';
export * as ebComStyleConfigs from './ebComStyleConfigs';

export * as doc from './doc'; //导出文档