/**
 * <AUTHOR>
 * @createTime 2021 -11 -18
 */

import { action, observable, runInAction } from 'mobx';
import ebdcoms from '../../../utils/ebdcoms';
import { DataSetItem } from '../../common/plugins/DataSet/types';

export class ConfigStore {
  @observable fieldDatas: any = {};

  @observable dataset: DataSetItem = {
    id: '',
    type: '',
    groupId: '',
    text: ''
  };

  /**
   * 请求数据仓库
   * */
  @action
  getFields = async (dataset: DataSetItem) => {
    const { id, type, groupId } = dataset;

    const cacheKey = `${groupId}_${id}`;

    if (this.fieldDatas[cacheKey]) {
      return this.fieldDatas[cacheKey];
    }

    const { dsUtils } = await ebdcoms.get();
    const fieldData = await dsUtils.getFields(dataset, '', false, false);

    runInAction(() => {
      this.fieldDatas[id] = fieldData || null;
    });
    return this.fieldDatas;
  };
}

export default new ConfigStore();
