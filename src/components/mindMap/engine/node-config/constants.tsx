import { FormItemProps } from '@weapp/ui';
import { getLabel } from '@weapp/utils';

/** *
 * 节点字段类型
 */
export enum NodeNames {
  Id = 'id',
  Name = 'name',
  NameShowSwitch = 'isShowName',//显示名称
  // 隐藏根节点
  IsHideRoot = 'isHideRoot',
  DefaultExpendDepth = 'defaultExpendDepth',
  // 二级节点
  Dataset = 'dataset', // 数据源
  ShowFields = 'showFields', // 显示字段
  DataFilter = 'dataFilter', // 数据过滤
  DataSort = 'dataSort', // 排序
  SupField = 'supField', // 上级字段

  // 三级以以上节点
  Curnodefield = 'curnodeField', // 本级节点字段
  Supnodefield = 'supnodeField', // 上级节点字段

  ActionSet = 'actions', // 节点动作

  //图标设置
  iconInfo = 'iconInfo',//图标
  IconHeight = 'iconHeight',//图标高度
  IconWidth = 'iconWidth',//图标宽度
  // -----通用节点（都有这些配置）-----
  NodeSize = 'nodeSize', // 节点尺寸
  NodeStyle = 'nodeStyle', // 节点样式
  NodeKeySet = 'nodeKeySet', // 节点图例设置
}

/* -----------------------根节点字段配置-------------------------*/
const rootNodeItems: FormItemProps = {
  [NodeNames.Name]: {
    itemType: 'CUSTOM',
    value: '',
    required: false,
  },
  [NodeNames.NameShowSwitch]: {
    itemType: 'SWITCH',
    value: true,
    required: false,
  },
  [NodeNames.IsHideRoot]: {
    itemType: 'SWITCH',
    value: false,
  },
  [NodeNames.DefaultExpendDepth]: {
    itemType: 'INPUTNUMBER',
    min: 0,
    max: 100,
    value: '',
  },
  [NodeNames.ActionSet]: {
    itemType: 'CUSTOM',
    value: '',
  },
};
const RootNodeFormLayout = [
  [
    {
      id: NodeNames.Name,
      label: getLabel('56091', '显示名称'),
      items: [NodeNames.Name],
      labelSpan: 6,
      hide: false,
    },
  ],
  // [
  //   {
  //     id: NodeNames.IsHideRoot,
  //     label: getLabel('147347', '是否隐藏'),
  //     items: [NodeNames.isHideRoot],
  //     labelSpan: 6,
  //     hide: false,
  //   },
  // ],
  [
    {
      id: NodeNames.DefaultExpendDepth,
      label: getLabel('56111', '展开级数'),
      items: [NodeNames.DefaultExpendDepth],
      placeholder: getLabel('87182', '默认展开级数'),
      labelSpan: 6,
      hide: false,
    },
  ],
];

/* -----------------------二级及以上分支节点字段配置-------------------------*/
const branchNodeItems: FormItemProps = {
  [NodeNames.Name]: {
    itemType: 'CUSTOM',
    value: '',
    required: false,
  },
  [NodeNames.Dataset]: {
    itemType: 'CUSTOM',
  },
  [NodeNames.ShowFields]: {
    itemType: 'CUSTOM',
  },
  [NodeNames.DataFilter]: {
    itemType: 'CUSTOM',
  },
  [NodeNames.DataSort]: {
    itemType: 'CUSTOM',
  },
  [NodeNames.SupField]: {
    itemType: 'SELECT',
    value: '',
    data: [],
  },
};

const branchNodeLayout = [
  [
    {
      id: NodeNames.Name,
      label: getLabel('87166', '节点名称'),
      items: [NodeNames.Name],
      labelSpan: 6,
      hide: false,
    },
  ],
  [
    {
      id: NodeNames.Dataset,
      label: getLabel('55058', '数据源'),
      items: [NodeNames.Dataset],
      labelSpan: 6,
      hide: false,
    },
  ],
  [
    {
      id: NodeNames.ShowFields,
      label: getLabel('53855', '显示字段'),
      items: [NodeNames.ShowFields],
      labelSpan: 6,
      hide: false,
    },
  ],
  [
    {
      id: NodeNames.DataFilter,
      label: getLabel('53857', '数据过滤'),
      items: [NodeNames.DataFilter],
      labelSpan: 6,
      hide: false,
    },
  ],
  [
    {
      id: NodeNames.DataSort,
      label: getLabel('54298', '排序'),
      items: [NodeNames.DataSort],
      labelSpan: 6,
      hide: false,
    },
  ],
  [
    {
      id: NodeNames.SupField,
      label: getLabel('97200', '上级字段'),
      items: [NodeNames.SupField],
      labelSpan: 6,
      hide: false,
    },
  ],
];

export const getItemAndLayout = (floor: number) => {
  if (floor === 1) {
    return {
      items: rootNodeItems,
      layout: RootNodeFormLayout,
    };
  }
  if (floor === 2) {
    return {
      items: branchNodeItems,
      layout: branchNodeLayout,
    };
  }
  return {
    items: {
      ...branchNodeItems,
      [NodeNames.Curnodefield]: {
        itemType: 'SELECT',
        value: '',
        data: [],
      },
      [NodeNames.Supnodefield]: {
        itemType: 'SELECT',
        value: '',
        data: [],
      },
    } as FormItemProps,
    layout: [
      ...branchNodeLayout,
      [
        {
          id: NodeNames.Curnodefield,
          label: getLabel('186458', '本级节点字段'),
          items: [NodeNames.Curnodefield],
          labelSpan: 6,
          hide: false,
        },
      ],
      [
        {
          id: NodeNames.Supnodefield,
          label: getLabel('87173', '上级节点字段'),
          items: [NodeNames.Supnodefield],
          labelSpan: 6,
          hide: false,
        },
      ],
    ],
  };
};
