export enum MIND_VIEW {
  LOGICAL_STRUCTURE = 'logicalStructure', // 逻辑结构图
  LOGICAL_STRUCTURE_LEFT = 'logicalStructureLeft', // 向左的逻辑结构图
  MIND_MAP = 'mindMap', // 思维导图（双向布局）
  ORGANIZATION_STRUCTURE = 'organizationStructure', // 组织结构图
  CATALOG_ORGANIZATION = 'catalogOrganization', // 目录组织图
  TIMELINE = 'timeline', // 时间轴
  TIMELINE_2 = 'timeline2', // 时间轴2
  VERTICAL_TIMELINE = 'verticalTimeline', // 竖向时间轴
  FISHBONE = 'fishbone', // 鱼骨图
}

export enum MIND_LINE_STYLE {
  CURVE = 'curve', // 曲线
  STRAIGHT = 'straight', // 直线
  DIRECT = 'direct', // 直连
}